apiVersion: v1
kind: ConfigMap
metadata:
  name: vclub-partner
  namespace: vinclub-backend-stag
data:
  JAVA_OPTS: "-Xmx1024m -Dlog4j.formatMsgNoLookups=true -javaagent:/usr/app/elastic-apm-agent.jar -Delastic.apm.service_name=vclub-partner-service -Delastic.apm.application_packages=com.vinloyalty,vn.vinclub -Delastic.apm.server_url=https://stag-apm.vinhomes.vn -Delastic.apm.secret_token=6NZOW0qPVsfVIdKv1oVZa0Gq -Delastic.apm.environment=stag"

  # PROFILER
  PROFILER_KEY: "VinClub@123!"

  #  POSTGRESQL CONFIG
  POSTGRESQL_URL: "******************************************************************************************************************************************"
  DB_DEFAULT_SCHEMA: "partner_db"
  POSTGRESQL_USER: "vinclub_partner_user"
  POSTGRESQL_PASSWORD: "**********************************"

  #  REDIS CONFIG
  REDISSON_CONFIG_HOST: "vhm-vinclub-stag-redis-azc.05cno1.0001.apse1.cache.amazonaws.com"
  REDISSON_CONFIG_PORT: "6379"
  REDISSON_CONFIG_ADDRESS: 'redis://vhm-vinclub-stag-redis-azc.05cno1.0001.apse1.cache.amazonaws.com:6379'
  REDISSON_CONFIG_PASSWORD: ''
  REDISSON_CONFIG_DATABASE: '2'
  REDISSON_CONFIG_RESPONSE_TIMEOUT: '3000'
  REDISSON_CONFIG_CONNECTION_TIMEOUT: '3000'
  REDISSON_CONFIG_CONNECTION_IDLE_TIME: '300000'
  REDISSON_CONFIG_CONNECTION_KEEP_ALIVE: 'true'
  REDISSON_CONFIG_CONNECTION_MAX: '64'
  REDISSON_CONFIG_CONNECTION_MIN: '4'
  REDISSON_CHANNEL_CHANGE: "CHANNEL.UPDATE"

  # KAFKA CONFIG
  KAFKA_BOOTSTRAP_SERVER: "b-2.vhmvinclubstagmsk.lggqx5.c5.kafka.ap-southeast-1.amazonaws.com:9096"
  KAFKA_USERNAME: "vhm-vinclub-stag-msk-consumer"
  KAFKA_PASSWORD: "**********************************"
  KAFKA_SASL_MECHANISM: "SCRAM-SHA-512"
  KAFKA_SASL_JAAS_CONFIG: "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"vhm-vinclub-stag-msk-consumer\" password=\"**********************************\";"
  KAFKA_SECURITY_PROTOCOL: "SASL_SSL"
  KAFKA_MAX_POLL_RECORDS: "1"

  # BEARER INTERACTIVE ADMIN TOKEN
  CORE_SERVICE_INTERNAL_ENDPOINT: "http://vclub-core-service.vinclub-backend-stag:80/r/internal"
  AUTHZ_VCLB_CORE_SVC_TOKEN: "Bearer eyJ4NXQiOiJNell4TW1Ga09HWXdNV0kwWldObU5EY3hOR1l3WW1NNFpUQTNNV0kyTkRBelpHUXpOR00wWkdSbE5qSmtPREZrWkRSaU9URmtNV0ZoTXpVMlpHVmxOZyIsImtpZCI6Ik16WXhNbUZrT0dZd01XSTBaV05tTkRjeE5HWXdZbU00WlRBM01XSTJOREF6WkdRek5HTTBaR1JsTmpKa09ERmtaRFJpT1RGa01XRmhNelUyWkdWbE5nX1JTMjU2IiwiYWxnIjoiUlMyNTYifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RtcMaiR4aASzXmUsmrREFB0uROJUrqkHl8-Z3afsGW8dS4Qqpi4A-QuGh-G49bFthmJbZ9m73LUjLead1npCIE_YaTFm0aGbb-fUbi1-beGS27_j4TXJszBHMb6MGK7Eiw0kaTSRJy5V41uiK5rmuzyVHbjHZ7pBnJLToXFjCxuYoSiVMo-33bmUMXbe_8WghUZMEuCPt0FWEv1ZA9Xyh-QlnebpJdjciZX9CYCdOhdqj-Wb_JkcaPLKS91Zr3srn14munUMRdPnZnmLVCCxAwBC4EeXCcj4X0Xaybnne01Ovcl5KFD2FGhkvksn2iPqAaAwA7bs7nVB-mrQFXFCKg"
  CUSTOMER_SERVICE_INTERNAL_ENDPOINT: "http://stag-svc.vinclub.internal:31158/internal"
  CUSTOMER_SERVICE_INTEGRATION_KEY: "dmNsdWItcGFydG5lcjpOeWticGRId0pZZmFpd3NCUXBpcnlqWUV6c05RblB0ejJr"

  # JWT CONFIG
  KEYCLOAK_BASE_URL: "https://stag-id.vinclub.vn"
  KEYCLOAK_PARTNER_REALM: "loyalty-partner"
  PARTNER_INTEGRATION_JWT_JWKS_URI: "https://stag-id.vinclub.vn/realms/loyalty-partner/protocol/openid-connect/certs"
  VCLUB_CUSTOMER_JWT_PUBLIC_KEY: |
    -----BEGIN PUBLIC KEY-----
    MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAh4z4zEl1yHPne+QM3Iyy
    nbeSHf0HURwfgghhGoleJYC4XkAVFVvqHv+LmQu3UuM0JFdB4GllaGJJaAnSdU26
    aGIctYITlXytQZcEptooCguWGh0VgMdF+DH74q+ZO+yLDtv74irr+KsSHC4qs5gF
    PG000eStbEvd7A8nlbbdldsYOKMza51YIsnDNcYifjoRANBeVxn2pV3f810/mqZr
    tSls+/YndQ9SGCQrtIIDrQotnBBJyZdOSBPo7aWyMcFl+Sv7H1Ngm6MZyWwkqSo1
    kQ7iJAjJyPHZMmlQqLNdAqZT9vryvv4h8T4WvriYzNbdvXymtmF6sVU9dgi2zGhc
    eAGQuAbYKz1FUagbdBL6lKeIdzlnrWlKSL/TR+6Feg6dbL9c1GfMhTqq8MeAKLuK
    AuCBU220AkG1f0ZOD3pw7J6laWCHtz6brp2tdWQc4Q3UNuGWtNGygxP5GCDF9/7P
    i0cB04k5A2wO/1otU7tAeSTjMC/ZZYpmsrIC+CPyRsHNDwkjRDKY5gl1Hnd8KgiG
    IkwlHt7wsFAzmQzYsaOFXXs4LAX8QdxPLOvEtsl5nWOB1LSx5k0HQUzGgFMeqLiL
    59YjCqqJxQYLZsLnfvoHx0JVDcEYpx19ayhOZd3sXXjqNGOkkCzZy01dBfuOMixm
    fkbA/QvfH7wSbvhKCZJrflkCAwEAAQ==
    -----END PUBLIC KEY-----
  

  PARTNER_MODULE_PROCESS_REDIRECT_TO_APP_URI_TEMPLATE: "https://stag-vinclub.vn/app/redirect/partner/{partner_code}/module/{module}?success={success}&next_step={next_step}"
  PARTNER_MODULE_PROCESS_REDIRECT_TO_SERVER_URI_TEMPLATE: "https://stag-api-partner.vinclub.vn/external/redirect/partner/{partner_code}/module/{module}?execution_id={execution_id}"
  PARTNER_INTEGRATION_TOKEN_MAX_ALIVE_TIME_IN_MS: "300000"

  # SKYJOY CONFIG
  SKY_JOY_CODE: "SKYJOY"
  SKY_JOY_AUTH_CLIENT_ID: "7844d303-b156-40fd-b987-e7ee361e4d18"
  SKY_JOY_AUTH_CLIENT_SECRET: "********************************"
  SKY_JOY_AUTH_USERNAME: "be_vinclub_partner"
  SKY_JOY_AUTH_PASSWORD: "xTWDdNdgDMG8"
  SKY_JOY_AUTH_ACCESS_TOKEN_URL: "https://id.uat.skyjoy.io/realms/loyalty-partner/protocol/openid-connect/token"
  SKY_JOY_API_BASE_URL: "https://api.uat.skyjoy.io"
  SKY_JOY_LINK_ACCOUNT_AUTH_URL: "https://id.uat.skyjoy.io/realms/uat-loyalty/protocol/openid-connect/auth"
  SKY_JOY_LINK_ACCOUNT_TOKEN_URL: "https://id.uat.skyjoy.io/realms/uat-loyalty/protocol/openid-connect/token"
  SKY_JOY_LINK_ACCOUNT_CLIENT_ID: "966dfaa1-6db8-454a-8ade-0741ee2fc45c"
  SKY_JOY_JWT_PUBLIC_KEY: |
    -----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAms0io9Fyt2hjlffWDIhh
    OUgHZ12hetRLr2LY1CevIy58lqZPt3stIhWHCMVvbN395yZGo75alAQOeXM6hvIh
    6L9jx3j1wUv/cva39st+nOoPt18FBWCXbQ0WwsWvg/etn9G1JhBgGUAmBsvMf3wB
    G78DQWIdDEBz2CWatjTtLYL3ll00WaOsZ7o/BNJMBkWP8e7IUkzndjd8PMyiTA7B
    NKbOcpZw/xH8YpJpoA65nXGRuHMbatHMI8fzU9Es3WQ/NtqI3shNpYXBpMMBAnaD
    dVyeYkJRYo1bQ2THOm52R1gXOlOJPlON8UAaoY4gfGTQQtrRsCWFQid+H/pzEz7z
    iwIDAQAB
    -----END PUBLIC KEY-----
