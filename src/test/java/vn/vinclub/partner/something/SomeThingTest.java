package vn.vinclub.partner.something;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.client.response.SkyJoyLookupTxnResponse;

@SpringBootTest
public class SomeThingTest {
    @Autowired
    private BaseJsonUtils jsonUtils;

    @Test
    void test() {
        var json = "{\n" +
                "    \"status\": \"SUCCESS\",\n" +
                "    \"transactionPoint\": 888,\n" +
                "    \"transactionAmount\": null,\n" +
                "    \"transactionSource\": \"Swap VPoint to Skypoint\",\n" +
                "    \"bitReference\": \"4IVPHGIM9DY82OD40ONQICPMWO\",\n" +
                "    \"transactionId\": \"4670239014244352\",\n" +
                "    \"transactionType\": \"ACCRUAL\",\n" +
                "    \"sponsor\": \"VinClub\",\n" +
                "    \"transactionTime\": \"2025-07-18T16:31:35+07:00\",\n" +
                "    \"requestId\": \"28641ccd-2853-47ac-ad67-88972a9b677e\"\n" +
                "  }";

        var data = JsonUtils.toObject(json, SkyJoyLookupTxnResponse.class);
        System.out.println(data.getTransactionTime().toInstant().toEpochMilli());


    }
}
