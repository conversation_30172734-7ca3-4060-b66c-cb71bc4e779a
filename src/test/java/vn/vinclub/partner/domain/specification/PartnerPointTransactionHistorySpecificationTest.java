package vn.vinclub.partner.domain.specification;

import org.junit.jupiter.api.Test;
import org.springframework.data.jpa.domain.Specification;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * Test class for PartnerPointTransactionHistorySpecification.
 */
class PartnerPointTransactionHistorySpecificationTest {

    @Test
    void testGetSpecification_withNullFilter_shouldReturnEmptySpecification() {
        // Given
        PartnerPointTxnFilterDto filter = null;

        // When
        Specification<PartnerPointTransactionHistory> spec = 
            PartnerPointTransactionHistorySpecification.getSpecification(filter);

        // Then
        assertNotNull(spec);
    }

    @Test
    void testGetSpecification_withEmptyFilter_shouldReturnEmptySpecification() {
        // Given
        PartnerPointTxnFilterDto filter = PartnerPointTxnFilterDto.builder().build();

        // When
        Specification<PartnerPointTransactionHistory> spec = 
            PartnerPointTransactionHistorySpecification.getSpecification(filter);

        // Then
        assertNotNull(spec);
    }

    @Test
    void testGetSpecification_withBasicFilters_shouldCreateSpecification() {
        // Given
        PartnerPointTxnFilterDto filter = PartnerPointTxnFilterDto.builder()
                .partnerId(1L)
                .partnerUserId("user123")
                .vclubUserId(456L)
                .actorSystem(ActorSystem.PARTNER)
                .transactionType(TransactionType.SPEND_POINT)
                .transactionId("txn123")
                .partnerTransactionId("partner_txn123")
                .internalRefCode("ref123")
                .status(PointHistoryStatus.SUCCESS)
                .active(true)
                .build();

        // When
        Specification<PartnerPointTransactionHistory> spec = 
            PartnerPointTransactionHistorySpecification.getSpecification(filter);

        // Then
        assertNotNull(spec);
    }

    @Test
    void testGetSpecification_withDateRangeFilters_shouldCreateSpecification() {
        // Given
        Long currentTime = System.currentTimeMillis();
        Long pastTime = currentTime - 86400000L; // 24 hours ago

        PartnerPointTxnFilterDto filter = PartnerPointTxnFilterDto.builder()
                .createdOnFrom(pastTime)
                .createdOnTo(currentTime)
                .updatedOnFrom(pastTime)
                .updatedOnTo(currentTime)
                .requestTimeFrom(pastTime)
                .requestTimeTo(currentTime)
                .processedTimeFrom(pastTime)
                .processedTimeTo(currentTime)
                .build();

        // When
        Specification<PartnerPointTransactionHistory> spec = 
            PartnerPointTransactionHistorySpecification.getSpecification(filter);

        // Then
        assertNotNull(spec);
    }

    @Test
    void testGetSpecification_withAmountRangeFilters_shouldCreateSpecification() {
        // Given
        PartnerPointTxnFilterDto filter = PartnerPointTxnFilterDto.builder()
                .pointAmountFrom(100L)
                .pointAmountTo(1000L)
                .build();

        // When
        Specification<PartnerPointTransactionHistory> spec = 
            PartnerPointTransactionHistorySpecification.getSpecification(filter);

        // Then
        assertNotNull(spec);
    }

    @Test
    void testGetSpecification_withListFilters_shouldCreateSpecification() {
        // Given
        List<PointHistoryStatus> statuses = Arrays.asList(
            PointHistoryStatus.SUCCESS, PointHistoryStatus.PROCESSING);
        List<ActorSystem> actorSystems = Arrays.asList(
            ActorSystem.PARTNER, ActorSystem.VINCLUB);
        List<TransactionType> transactionTypes = Arrays.asList(
            TransactionType.SPEND_POINT, TransactionType.TOP_UP_POINT);
        List<String> transactionIds = Arrays.asList("txn1", "txn2", "txn3");
        List<String> partnerTransactionIds = Arrays.asList("partner_txn1", "partner_txn2");
        List<String> internalRefCodes = Arrays.asList("ref1", "ref2", "ref3");

        PartnerPointTxnFilterDto filter = PartnerPointTxnFilterDto.builder()
                .statuses(statuses)
                .actorSystems(actorSystems)
                .transactionTypes(transactionTypes)
                .transactionIds(transactionIds)
                .partnerTransactionIds(partnerTransactionIds)
                .internalRefCodes(internalRefCodes)
                .build();

        // When
        Specification<PartnerPointTransactionHistory> spec = 
            PartnerPointTransactionHistorySpecification.getSpecification(filter);

        // Then
        assertNotNull(spec);
    }

    @Test
    void testGetSpecification_withAllFilters_shouldCreateComplexSpecification() {
        // Given
        Long currentTime = System.currentTimeMillis();
        Long pastTime = currentTime - 86400000L; // 24 hours ago

        PartnerPointTxnFilterDto filter = PartnerPointTxnFilterDto.builder()
                // Basic filters
                .partnerId(1L)
                .partnerUserId("user123")
                .vclubUserId(456L)
                .actorSystem(ActorSystem.PARTNER)
                .transactionType(TransactionType.SPEND_POINT)
                .transactionId("txn123")
                .partnerTransactionId("partner_txn123")
                .internalRefCode("ref123")
                .status(PointHistoryStatus.SUCCESS)
                .active(true)
                // Date range filters
                .createdOnFrom(pastTime)
                .createdOnTo(currentTime)
                .requestTimeFrom(pastTime)
                .requestTimeTo(currentTime)
                // Amount range filters
                .pointAmountFrom(100L)
                .pointAmountTo(1000L)
                // List filters
                .statuses(Arrays.asList(PointHistoryStatus.SUCCESS, PointHistoryStatus.PROCESSING))
                .actorSystems(Arrays.asList(ActorSystem.PARTNER))
                .transactionTypes(Arrays.asList(TransactionType.SPEND_POINT))
                .build();

        // When
        Specification<PartnerPointTransactionHistory> spec = 
            PartnerPointTransactionHistorySpecification.getSpecification(filter);

        // Then
        assertNotNull(spec);
    }
}
