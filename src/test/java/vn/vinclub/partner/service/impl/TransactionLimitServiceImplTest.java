package vn.vinclub.partner.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.domain.dto.UserQuotaDto;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;
import vn.vinclub.partner.service.PartnerModuleService;
import vn.vinclub.partner.service.PartnerPointConfigService;
import vn.vinclub.partner.service.PartnerPointTransactionHistoryService;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Test class for TransactionLimitServiceImpl focusing on incrementQuota and decrementQuota methods.
 */
@ExtendWith(MockitoExtension.class)
class TransactionLimitServiceImplTest {

    @Mock
    private PartnerModuleService partnerModuleService;

    @Mock
    private PartnerPointConfigService partnerPointConfigService;

    @Mock
    private PartnerPointTransactionHistoryService partnerPointTransactionHistoryService;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private BaseJsonUtils jsonUtils;

    @Mock
    private JsonJacksonCodec jsonJacksonCodec;

    @Mock
    @SuppressWarnings("unchecked")
    private RBucket<UserQuotaDto> rBucket;

    private TransactionLimitServiceImpl transactionLimitService;

    private PartnerPointTransactionHistory testTransaction;
    private UserQuotaDto testQuota;

    @BeforeEach
    void setUp() {
        // Initialize the service with mocked dependencies
        transactionLimitService = new TransactionLimitServiceImpl(
                partnerModuleService,
                partnerPointConfigService,
                partnerPointTransactionHistoryService,
                redissonClient,
                jsonUtils,
                jsonJacksonCodec
        );

        // Use reflection to set private fields that are injected via @Value
        try {
            var defaultVinclubPointCodeField = TransactionLimitServiceImpl.class.getDeclaredField("defaultVinclubPointCode");
            defaultVinclubPointCodeField.setAccessible(true);
            defaultVinclubPointCodeField.set(transactionLimitService, "VPOINT");

            var defaultVinclubPointCashValueField = TransactionLimitServiceImpl.class.getDeclaredField("defaultVinclubPointCashValue");
            defaultVinclubPointCashValueField.setAccessible(true);
            defaultVinclubPointCashValueField.set(transactionLimitService, BigDecimal.valueOf(1000));

            var maxRequestByUserPerDayField = TransactionLimitServiceImpl.class.getDeclaredField("maxRequestByUserPerDay");
            maxRequestByUserPerDayField.setAccessible(true);
            maxRequestByUserPerDayField.set(transactionLimitService, 100L);

            var maxCashAmountByUserPerDayField = TransactionLimitServiceImpl.class.getDeclaredField("maxCashAmountByUserPerDay");
            maxCashAmountByUserPerDayField.setAccessible(true);
            maxCashAmountByUserPerDayField.set(transactionLimitService, 1000000L);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set up test", e);
        }

        // Create test transaction
        testTransaction = new PartnerPointTransactionHistory();
        testTransaction.setId(1L);
        testTransaction.setPartnerId(100L);
        testTransaction.setPartnerUserId("user123");
        testTransaction.setVclubUserId(200L);
        testTransaction.setActorSystem(ActorSystem.VINCLUB);
        testTransaction.setTransactionType(TransactionType.SPEND_POINT);
        testTransaction.setTransactionId("txn-123");
        testTransaction.setPointAmount(1000L);
        testTransaction.setPointCode("POINT_CODE_1");
        testTransaction.setStatus(PointHistoryStatus.SUCCESS);

        // Create test quota
        Map<TransactionType, Map<String, Long>> partnerPointTransactionByTypeAndPointCode = new HashMap<>();
        Map<String, Long> pointCodeMap = new HashMap<>();
        pointCodeMap.put("POINT_CODE_1", 5L);
        partnerPointTransactionByTypeAndPointCode.put(TransactionType.SPEND_POINT, pointCodeMap);

        Map<TransactionType, Map<String, Long>> partnerPointAmountByTypeAndPointCode = new HashMap<>();
        Map<String, Long> amountMap = new HashMap<>();
        amountMap.put("POINT_CODE_1", 5000L);
        partnerPointAmountByTypeAndPointCode.put(TransactionType.SPEND_POINT, amountMap);

        Map<TransactionType, Long> vinclubPointTransactionByType = new HashMap<>();
        vinclubPointTransactionByType.put(TransactionType.SPEND_POINT, 3L);

        Map<TransactionType, Long> vinclubPointAmountByType = new HashMap<>();
        vinclubPointAmountByType.put(TransactionType.SPEND_POINT, 3000L);

        testQuota = UserQuotaDto.builder()
                .totalTransaction(10L)
                .totalCashAmount(BigDecimal.valueOf(10000))
                .totalPartnerPointTransactionByTypeAndPointCode(partnerPointTransactionByTypeAndPointCode)
                .totalPartnerPointAmountByTypeAndPointCode(partnerPointAmountByTypeAndPointCode)
                .totalVinclubPointTransactionByType(vinclubPointTransactionByType)
                .totalVinclubPointAmountByType(vinclubPointAmountByType)
                .build();
    }

    @Test
    @SuppressWarnings("unchecked")
    void testUpdateQuota_NewTransaction_ShouldIncrementQuota() {
        // Arrange
        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn((RBucket) rBucket);
        when(rBucket.isExists()).thenReturn(true);
        when(rBucket.get()).thenReturn(testQuota);

        // Act
        transactionLimitService.updateQuota(null, testTransaction);

        // Assert
        verify(rBucket, times(2)).set(any(UserQuotaDto.class), any(Duration.class)); // Daily and monthly
    }

    @Test
    @SuppressWarnings("unchecked")
    void testUpdateQuota_DeleteTransaction_ShouldDecrementQuota() {
        // Arrange
        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn((RBucket) rBucket);
        when(rBucket.isExists()).thenReturn(true);
        when(rBucket.get()).thenReturn(testQuota);

        // Act
        transactionLimitService.updateQuota(testTransaction, null);

        // Assert
        verify(rBucket, times(2)).set(any(UserQuotaDto.class), any(Duration.class)); // Daily and monthly
    }

    @Test
    @SuppressWarnings("unchecked")
    void testUpdateQuota_StatusChange_FromFailedToSuccess_ShouldIncrementQuota() {
        // Arrange
        PartnerPointTransactionHistory oldTransaction = new PartnerPointTransactionHistory();
        oldTransaction.setId(1L);
        oldTransaction.setPartnerId(100L);
        oldTransaction.setPartnerUserId("user123");
        oldTransaction.setStatus(PointHistoryStatus.FAILED); // FAILED is not in COUNTED_STATUSES
        oldTransaction.setActorSystem(ActorSystem.VINCLUB);
        oldTransaction.setTransactionType(TransactionType.SPEND_POINT);
        oldTransaction.setPointAmount(1000L);
        oldTransaction.setPointCode("POINT_CODE_1");

        PartnerPointTransactionHistory newTransaction = new PartnerPointTransactionHistory();
        newTransaction.setId(1L);
        newTransaction.setPartnerId(100L);
        newTransaction.setPartnerUserId("user123");
        newTransaction.setStatus(PointHistoryStatus.SUCCESS); // SUCCESS is in COUNTED_STATUSES
        newTransaction.setActorSystem(ActorSystem.VINCLUB);
        newTransaction.setTransactionType(TransactionType.SPEND_POINT);
        newTransaction.setPointAmount(1000L);
        newTransaction.setPointCode("POINT_CODE_1");

        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn((RBucket) rBucket);
        when(rBucket.isExists()).thenReturn(true);
        when(rBucket.get()).thenReturn(testQuota);

        // Act
        transactionLimitService.updateQuota(oldTransaction, newTransaction);

        // Assert
        verify(rBucket, times(2)).set(any(UserQuotaDto.class), any(Duration.class)); // Daily and monthly
    }

    @Test
    @SuppressWarnings("unchecked")
    void testUpdateQuota_StatusChange_FromProcessingToSuccess_ShouldNotChangeQuota() {
        // Arrange - Both PROCESSING and SUCCESS are in COUNTED_STATUSES, so no change should occur
        PartnerPointTransactionHistory oldTransaction = new PartnerPointTransactionHistory();
        oldTransaction.setId(1L);
        oldTransaction.setPartnerId(100L);
        oldTransaction.setPartnerUserId("user123");
        oldTransaction.setStatus(PointHistoryStatus.PROCESSING);
        oldTransaction.setActorSystem(ActorSystem.VINCLUB);
        oldTransaction.setTransactionType(TransactionType.SPEND_POINT);
        oldTransaction.setPointAmount(1000L);
        oldTransaction.setPointCode("POINT_CODE_1");

        PartnerPointTransactionHistory newTransaction = new PartnerPointTransactionHistory();
        newTransaction.setId(1L);
        newTransaction.setPartnerId(100L);
        newTransaction.setPartnerUserId("user123");
        newTransaction.setStatus(PointHistoryStatus.SUCCESS);
        newTransaction.setActorSystem(ActorSystem.VINCLUB);
        newTransaction.setTransactionType(TransactionType.SPEND_POINT);
        newTransaction.setPointAmount(1000L);
        newTransaction.setPointCode("POINT_CODE_1");

        // Act
        transactionLimitService.updateQuota(oldTransaction, newTransaction);

        // Assert - No interactions with Redis should occur since both statuses are counted
        verify(redissonClient, never()).getBucket(anyString(), any(JsonJacksonCodec.class));
    }

    @Test
    @SuppressWarnings("unchecked")
    void testUpdateQuota_StatusChange_FromSuccessToFailed_ShouldDecrementQuota() {
        // Arrange
        PartnerPointTransactionHistory oldTransaction = new PartnerPointTransactionHistory();
        oldTransaction.setId(1L);
        oldTransaction.setPartnerId(100L);
        oldTransaction.setPartnerUserId("user123");
        oldTransaction.setStatus(PointHistoryStatus.SUCCESS);
        oldTransaction.setActorSystem(ActorSystem.VINCLUB);
        oldTransaction.setTransactionType(TransactionType.SPEND_POINT);
        oldTransaction.setPointAmount(1000L);
        oldTransaction.setPointCode("POINT_CODE_1");

        PartnerPointTransactionHistory newTransaction = new PartnerPointTransactionHistory();
        newTransaction.setId(1L);
        newTransaction.setPartnerId(100L);
        newTransaction.setPartnerUserId("user123");
        newTransaction.setStatus(PointHistoryStatus.FAILED);
        newTransaction.setActorSystem(ActorSystem.VINCLUB);
        newTransaction.setTransactionType(TransactionType.SPEND_POINT);
        newTransaction.setPointAmount(1000L);
        newTransaction.setPointCode("POINT_CODE_1");

        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn((RBucket) rBucket);
        when(rBucket.isExists()).thenReturn(true);
        when(rBucket.get()).thenReturn(testQuota);

        // Act
        transactionLimitService.updateQuota(oldTransaction, newTransaction);

        // Assert
        verify(rBucket, times(2)).set(any(UserQuotaDto.class), any(Duration.class)); // Daily and monthly
    }

    @Test
    void testUpdateQuota_NullTransaction_ShouldNotThrowException() {
        // Act & Assert
        assertDoesNotThrow(() -> transactionLimitService.updateQuota(null, null));
    }

    @Test
    void testUpdateQuota_TransactionWithNullPartnerId_ShouldLogWarningAndReturn() {
        // Arrange
        testTransaction.setPartnerId(null);

        // Act
        transactionLimitService.updateQuota(null, testTransaction);

        // Assert - No exception should be thrown, method should return early
        verify(redissonClient, never()).getBucket(anyString(), any(JsonJacksonCodec.class));
    }

    @Test
    void testUpdateQuota_TransactionWithNullPartnerUserId_ShouldLogWarningAndReturn() {
        // Arrange
        testTransaction.setPartnerUserId(null);

        // Act
        transactionLimitService.updateQuota(null, testTransaction);

        // Assert - No exception should be thrown, method should return early
        verify(redissonClient, never()).getBucket(anyString(), any(JsonJacksonCodec.class));
    }

    @Test
    @SuppressWarnings("unchecked")
    void testUpdateQuota_VinclubToPartnerTransaction_ShouldUpdatePartnerPointMaps() {
        // Arrange
        testTransaction.setActorSystem(ActorSystem.VINCLUB); // Vinclub -> Partner
        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn((RBucket) rBucket);
        when(rBucket.isExists()).thenReturn(true);
        when(rBucket.get()).thenReturn(testQuota);

        // Act
        transactionLimitService.updateQuota(null, testTransaction);

        // Assert
        verify(rBucket, times(2)).set(any(UserQuotaDto.class), any(Duration.class));
    }

    @Test
    @SuppressWarnings("unchecked")
    void testUpdateQuota_PartnerToVinclubTransaction_ShouldUpdateVinclubPointMaps() {
        // Arrange
        testTransaction.setActorSystem(ActorSystem.PARTNER); // Partner -> Vinclub
        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn((RBucket) rBucket);
        when(rBucket.isExists()).thenReturn(true);
        when(rBucket.get()).thenReturn(testQuota);

        // Act
        transactionLimitService.updateQuota(null, testTransaction);

        // Assert
        verify(rBucket, times(2)).set(any(UserQuotaDto.class), any(Duration.class));
    }
}
