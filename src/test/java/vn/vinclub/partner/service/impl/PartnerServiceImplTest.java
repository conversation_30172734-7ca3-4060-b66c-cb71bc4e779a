package vn.vinclub.partner.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.SignedUrlInfoDto;
import vn.vinclub.partner.domain.dto.encryption.EncryptKeyData;
import vn.vinclub.partner.domain.dto.encryption.EncryptKeySetting;
import vn.vinclub.partner.domain.dto.partner.PartnerCreateDto;
import vn.vinclub.partner.domain.dto.partner.PartnerFilterDto;
import vn.vinclub.partner.domain.dto.partner.PartnerUpdateDto;
import vn.vinclub.partner.domain.entity.Partner;
import vn.vinclub.partner.domain.enums.EncryptKeyType;
import vn.vinclub.partner.domain.enums.PartnerStatus;
import vn.vinclub.partner.domain.mapper.PartnerMapper;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.redis.RedisPublish;
import vn.vinclub.partner.repository.PartnerRepository;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PartnerServiceImplTest {

    @Mock
    private PartnerRepository partnerRepository;

    @Mock
    private PartnerMapper partnerMapper;

    @Mock
    private RedisPublish redisPublish;

    @InjectMocks
    private PartnerServiceImpl partnerService;

    private Partner partner;
    private PartnerCreateDto createDto;
    private PartnerUpdateDto updateDto;
    private EncryptKeyData encryptKeyData;

    @BeforeEach
    void setUp() {
        // Setup test data
        partner = new Partner();
        partner.setId(1L);
        partner.setCode("TEST_PARTNER");
        partner.setActive(true);
        partner.setStatus(PartnerStatus.ACTIVE);

        encryptKeyData = EncryptKeyData.builder()
                .encryptKeyType(EncryptKeyType.AES)
                .secretKey("test-key")
                .build();
        partner.setEncryptionKey(encryptKeyData);

        createDto = PartnerCreateDto.builder()
                .code("TEST_PARTNER")
                .displayNames(Map.of("en", "Test Partner"))
                .logos(Map.of("default", SignedUrlInfoDto.builder().build()))
                .encryptKeySetting(EncryptKeySetting.builder().keyType(EncryptKeyType.AES).keySize(256).build())
                .build();

        updateDto = PartnerUpdateDto.builder()
                .displayNames(Map.of("en", "Updated Test Partner"))
                .build();
    }

    @Test
    void testCreate_Success() {
        // Arrange
        when(partnerRepository.findByCode(anyString())).thenReturn(Optional.empty());
        when(partnerMapper.toEntity(any(PartnerCreateDto.class))).thenReturn(partner);
        when(partnerRepository.save(any(Partner.class))).thenReturn(partner);

        // Act
        Partner result = partnerService.create(createDto);

        // Assert
        assertNotNull(result);
        assertEquals(partner.getId(), result.getId());
        assertEquals(partner.getCode(), result.getCode());
        assertTrue(result.isActive());
        assertEquals(PartnerStatus.ACTIVE, result.getStatus());

        verify(partnerRepository).findByCode(createDto.getCode());
        verify(partnerMapper).toEntity(createDto);
        verify(partnerRepository).save(partner);
    }

    @Test
    void testCreate_PartnerAlreadyExists() {
        // Arrange
        when(partnerRepository.findByCode(anyString())).thenReturn(Optional.of(partner));

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerService.create(createDto);
        });

        assertEquals(AppErrorCode.OBJECT_EXISTED.getCode(), exception.getPayload().getCode());

        verify(partnerRepository).findByCode(createDto.getCode());
        verify(partnerMapper, never()).toEntity(any(PartnerCreateDto.class));
        verify(partnerRepository, never()).save(any(Partner.class));
    }

    @Test
    void testUpdate_Success() {
        // Arrange
        when(partnerRepository.findById(anyLong())).thenReturn(Optional.of(partner));
        when(partnerRepository.save(any(Partner.class))).thenReturn(partner);

        // Act
        Partner result = partnerService.update(1L, updateDto);

        // Assert
        assertNotNull(result);
        assertEquals(partner.getId(), result.getId());

        verify(partnerRepository).findById(1L);
        verify(partnerMapper).partialUpdate(partner, updateDto);
        verify(partnerRepository).save(partner);
    }

    @Test
    void testUpdate_PartnerNotFound() {
        // Arrange
        when(partnerRepository.findById(anyLong())).thenReturn(Optional.empty());

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerService.update(1L, updateDto);
        });

        assertEquals(AppErrorCode.NOT_FOUND.getCode(), exception.getPayload().getCode());

        verify(partnerRepository).findById(1L);
        verify(partnerMapper, never()).partialUpdate(any(Partner.class), any(PartnerUpdateDto.class));
        verify(partnerRepository, never()).save(any(Partner.class));
    }

    @Test
    void testDelete_Success() {
        // Arrange
        when(partnerRepository.findById(anyLong())).thenReturn(Optional.of(partner));
        when(partnerRepository.save(any(Partner.class))).thenReturn(partner);

        // Act
        partnerService.delete(1L);

        // Assert
        verify(partnerRepository).findById(1L);
        verify(partnerRepository).save(partner);
        assertFalse(partner.isActive());
    }

    @Test
    void testFindById_Success() {
        // Arrange
        when(partnerRepository.findById(anyLong())).thenReturn(Optional.of(partner));

        // Act
        Partner result = partnerService.findById(1L);

        // Assert
        assertNotNull(result);
        assertEquals(partner.getId(), result.getId());
        assertEquals(partner.getCode(), result.getCode());

        verify(partnerRepository).findById(1L);
    }

    @Test
    void testFindById_NotFound() {
        // Arrange
        when(partnerRepository.findById(anyLong())).thenReturn(Optional.empty());

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerService.findById(1L);
        });

        assertEquals(AppErrorCode.NOT_FOUND.getCode(), exception.getPayload().getCode());

        verify(partnerRepository).findById(1L);
    }

    @Test
    void testFindByCode_Success() {
        // Arrange
        when(partnerRepository.findByCode(anyString())).thenReturn(Optional.of(partner));

        // Act
        Partner result = partnerService.findByCode("TEST_PARTNER");

        // Assert
        assertNotNull(result);
        assertEquals(partner.getId(), result.getId());
        assertEquals(partner.getCode(), result.getCode());

        verify(partnerRepository).findByCode("TEST_PARTNER");
    }

    @Test
    void testFindByCode_NotFound() {
        // Arrange
        when(partnerRepository.findByCode(anyString())).thenReturn(Optional.empty());

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerService.findByCode("TEST_PARTNER");
        });

        assertEquals(AppErrorCode.NOT_FOUND.getCode(), exception.getPayload().getCode());

        verify(partnerRepository).findByCode("TEST_PARTNER");
    }

    @Test
    void testFilter_Success() {
        // Arrange
        List<Partner> partners = Arrays.asList(partner);
        Page<Partner> page = new PageImpl<>(partners);
        PartnerFilterDto filter = new PartnerFilterDto();
        Pageable pageable = Pageable.unpaged();

        when(partnerRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(page);

        // Act
        Page<Partner> result = partnerService.filter(filter, pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(partner.getId(), result.getContent().get(0).getId());

        verify(partnerRepository).findAll(any(Specification.class), eq(pageable));
    }

    @Test
    void testGetEncryptKey_Success() {
        // Arrange
        when(partnerRepository.findByCode(anyString())).thenReturn(Optional.of(partner));

        // Act
        EncryptKeyData result = partnerService.getEncryptKey("TEST_PARTNER");

        // Assert
        assertNotNull(result);
        // Use the correct fields from EncryptKeyData
        assertEquals(encryptKeyData.getEncryptKeyType(), result.getEncryptKeyType());
        assertEquals(encryptKeyData.getSecretKey(), result.getSecretKey());

        verify(partnerRepository).findByCode("TEST_PARTNER");
    }

    @Test
    void testInvalidateCache() {
        // Act
        partnerService.invalidateCache(1L, "TEST_PARTNER");

        // No assertions needed as this is just testing that the method doesn't throw exceptions
    }
}
