package vn.vinclub.partner.security.impl;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.util.ServiceResponse;

import java.util.Map;


// Prefer component over util class
// because if we need to inject bean, util class cannot do it properly without some kind of ContextUtil
@Component
public class VClubCustomerAuthenticationFacadeImpl {

    public Map<String, Object> getAuthenticatedClaims() {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof JwtAuthenticationToken jwtAuthenticationToken && jwtAuthenticationToken.getPrincipal() instanceof Jwt jwt) {
            return jwt.getClaims();
        }

        throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.USER_UNAUTHORIZED));
    }

    public String getUsername() {
        Object username = getAuthenticatedClaims().get(AppConst.CUSTOMER_JWT_CLAIM.USERNAME);
        return username == null ? null : username.toString();
    }

    public Long getVclubUserId() {
        Object vclubUserId = getAuthenticatedClaims().get(AppConst.CUSTOMER_JWT_CLAIM.VCLUB_USER_ID);
        return vclubUserId == null ? null : Long.valueOf(vclubUserId.toString());
    }

    public String getPhone() {
        Object phone = getAuthenticatedClaims().get(AppConst.CUSTOMER_JWT_CLAIM.PHONE);
        return phone == null ? null : phone.toString();
    }

    public String getPartnerCode() {
        Object partnerCode = getAuthenticatedClaims().get(AppConst.CUSTOMER_JWT_CLAIM.PARTNER_CODE);
        String authenticatedPartnerCode = partnerCode == null ? null : partnerCode.toString();
        if (authenticatedPartnerCode == null) {
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.USER_UNAUTHORIZED));
        }
        return authenticatedPartnerCode;
    }

    public String getPartnerUserId() {
        Object partnerUserId = getAuthenticatedClaims().get(AppConst.CUSTOMER_JWT_CLAIM.PARTNER_USER_ID);
        return partnerUserId == null ? null : partnerUserId.toString();
    }
}
