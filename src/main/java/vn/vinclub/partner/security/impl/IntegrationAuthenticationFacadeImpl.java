package vn.vinclub.partner.security.impl;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.util.ServiceResponse;

import java.util.Map;

@Component
public class IntegrationAuthenticationFacadeImpl {

    public Map<String, Object> getAuthenticatedClaims() {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof JwtAuthenticationToken jwtAuthenticationToken && jwtAuthenticationToken.getPrincipal() instanceof Jwt jwt) {
            return jwt.getClaims();
        }

        throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.USER_UNAUTHORIZED));
    }

    public String getUsername() {
        Object username = getAuthenticatedClaims().get(AppConst.INTEGRATION_JWT_CLAIM.USERNAME);
        return username == null ? null : username.toString();
    }

    public String getPartnerCode() {
        Object partnerCode = getAuthenticatedClaims().get(AppConst.INTEGRATION_JWT_CLAIM.PARTNER_CODE);
        return partnerCode == null ? null : partnerCode.toString();
    }

}
