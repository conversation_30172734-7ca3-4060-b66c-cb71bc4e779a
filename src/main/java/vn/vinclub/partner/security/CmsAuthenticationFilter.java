package vn.vinclub.partner.security;


import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.web.filter.OncePerRequestFilter;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.service.impl.JwtCommonImpl;
import vn.vinclub.partner.util.ServiceResponse;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
public class CmsAuthenticationFilter extends OncePerRequestFilter {

    private static final String AUTHORIZATION = "Authorization";
    private static final String BEARERS = "Bearer ";

    private static final String[] IP_HEADER_CANDIDATES = {"CF-Connecting-IP", "X-Forwarded-For", "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_X_FORWARDED_FOR", "HTTP_X_FORWARDED", "HTTP_X_CLUSTER_CLIENT_IP", "HTTP_CLIENT_IP", "HTTP_FORWARDED_FOR", "HTTP_FORWARDED", "HTTP_VIA", "REMOTE_ADDR"};
    private static final List<String> FILTER_PREFIXES = List.of(
            "/internal"
    );

    private final JwtCommonImpl jwtService;
    private final JwtAuthenticationConverter jwtAuthConverter;

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String servletPath = request.getServletPath();
        return FILTER_PREFIXES.stream().anyMatch(servletPath::startsWith);
    }

    @Override
    public void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {

        String token = request.getHeader(AUTHORIZATION);
        if (ObjectUtils.isEmpty(token) || !token.startsWith(BEARERS)) {
            responseAuthenFail(response);
            return;
        }
        String jwtString = token.substring(BEARERS.length()).trim();

        Jwt jwt = jwtService.decodeCmsJwt(jwtString);
        if (Objects.isNull(jwt)) {
            responseAuthenFail(response);
            return;
        }

        AbstractAuthenticationToken jwtAuthenticationToken = jwtAuthConverter.convert(jwt);
        SecurityContextHolder.getContext().setAuthentication(jwtAuthenticationToken);

        filterChain.doFilter(request, response);
    }

    private String responseAuthenFail(ServletResponse servletResponse) throws IOException {
        ((HttpServletResponse) servletResponse).setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        servletResponse.setCharacterEncoding("UTF-8");
        servletResponse.setContentType("application/json");
        servletResponse.getWriter().write(JsonUtils.toString(ServiceResponse.error(HttpStatus.UNAUTHORIZED.value(), HttpStatus.UNAUTHORIZED.getReasonPhrase())));
        return AppConst.SUCCESS;
    }

}
