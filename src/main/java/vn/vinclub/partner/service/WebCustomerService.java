package vn.vinclub.partner.service;

import com.fasterxml.jackson.databind.node.ObjectNode;
import vn.vinclub.partner.domain.dto.request.PartnerSignedTokenRequest;
import vn.vinclub.partner.domain.dto.response.LinkedAccountResponse;

public interface WebCustomerService {
    ObjectNode exchangePartnerToken(PartnerSignedTokenRequest signedTokenDto);

    LinkedAccountResponse getLinkedAccount(String partnerCode, Long vclubUserId);

    LinkedAccountResponse linkAccount(Long vclubUserId, PartnerSignedTokenRequest signedTokenDto);
}
