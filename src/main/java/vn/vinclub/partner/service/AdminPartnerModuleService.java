package vn.vinclub.partner.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.partner.domain.dto.module.PartnerModuleCreateDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleFilterDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleUpdateDto;

public interface AdminPartnerModuleService {
    PartnerModuleDto createPartnerModule(PartnerModuleCreateDto createDto);

    PartnerModuleDto updatePartnerModule(Long partnerId, Long id, PartnerModuleUpdateDto updateDto);

    void deletePartnerModule(Long partnerId, Long id);

    PartnerModuleDto getPartnerModuleDetails(Long partnerId, Long id);

    Page<PartnerModuleDto> searchPartnerModules(PartnerModuleFilterDto filter, Pageable pageable);
}
