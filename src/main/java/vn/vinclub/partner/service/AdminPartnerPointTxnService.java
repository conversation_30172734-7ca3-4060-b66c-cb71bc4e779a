package vn.vinclub.partner.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.partner.domain.dto.point.PartnerPointTransactionHistoryDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnRollbackDto;

public interface AdminPartnerPointTxnService {
    Page<PartnerPointTransactionHistoryDto> searchPartnerPointTransactions(PartnerPointTxnFilterDto filter, Pageable pageable);

    PartnerPointTransactionHistoryDto getPartnerPointTransactionHistory(Long id);

    PartnerPointTransactionHistoryDto rollbackPartnerPointTransaction(Long id, PartnerPointTxnRollbackDto rollbackDto);
}
