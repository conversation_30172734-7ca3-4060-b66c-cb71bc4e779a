package vn.vinclub.partner.service;

import vn.vinclub.partner.domain.dto.request.LinkAccountRequest;
import vn.vinclub.partner.domain.dto.response.LinkAccountResponse;
import vn.vinclub.partner.domain.dto.response.LinkablePartnerResponse;
import vn.vinclub.partner.domain.dto.response.LinkedAccountResponse;
import vn.vinclub.partner.domain.dto.response.PartnerPointTxnHistoryResponse;

import java.util.List;

public interface CustomerPartnerService {

    List<LinkablePartnerResponse> getLinkablePartner(Long vclubUserId);

    LinkedAccountResponse getLinkedAccount(String partnerCode, Long vclubUserId);

    LinkAccountResponse processLinkAccountModule(LinkAccountRequest request);

    void unlinkAccount(String partnerCode, Long vclubUserId);

    PartnerPointTxnHistoryResponse getPartnerPointTransactions(Long vclubUserId, String transactionId);
}
