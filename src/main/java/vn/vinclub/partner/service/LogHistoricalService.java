package vn.vinclub.partner.service;

import vn.vinclub.partner.domain.entity.BaseEntity;
import vn.vinclub.partner.domain.enums.HistoricalAction;

public interface LogHistoricalService {
    <T extends BaseEntity> void logHistoricalEvent(T oldObj, T newObj, Long timestamp, HistoricalAction action);

    default <T extends BaseEntity> void logHistoricalEvent(T oldObj, T newObj) {
        logHistoricalEvent(oldObj, newObj, null, null);
    }
}
