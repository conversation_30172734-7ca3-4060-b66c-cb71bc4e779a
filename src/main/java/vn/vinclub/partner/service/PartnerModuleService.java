package vn.vinclub.partner.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.partner.domain.dto.module.PartnerModuleCreateDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleFilterDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleUpdateDto;
import vn.vinclub.partner.domain.entity.PartnerModule;
import vn.vinclub.partner.domain.enums.ModuleType;

import java.util.List;
import java.util.Optional;

public interface PartnerModuleService {
    PartnerModule create(PartnerModuleCreateDto createDto);

    PartnerModule update(Long partnerId, Long id, PartnerModuleUpdateDto updateDto);

    void delete(Long partnerId, Long id);

    PartnerModule findById(Long id);

    Optional<PartnerModule> optByPartnerAndModule(Long partnerId, ModuleType module);

    PartnerModule findByPartnerAndModule(Long partnerId, ModuleType module);

    List<PartnerModule> findEnabledByModule(ModuleType module);

    Page<PartnerModule> filter(PartnerModuleFilterDto filter, Pageable pageable);

    void invalidateCache(Long id);
}
