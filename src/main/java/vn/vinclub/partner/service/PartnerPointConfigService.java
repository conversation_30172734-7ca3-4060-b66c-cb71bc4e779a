package vn.vinclub.partner.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigCreateDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigUpdateDto;
import vn.vinclub.partner.domain.entity.PartnerPointConfig;

public interface PartnerPointConfigService {
    PartnerPointConfig create(PartnerPointConfigCreateDto createDto);

    PartnerPointConfig update(Long partnerId, Long id, PartnerPointConfigUpdateDto updateDto);

    void delete(Long partnerId, Long id);

    PartnerPointConfig findById(Long id);

    PartnerPointConfig findByCode(Long partnerId, String code);

    Page<PartnerPointConfig> filter(PartnerPointConfigFilterDto filter, Pageable pageable);

    void invalidateCache(Long id);
}
