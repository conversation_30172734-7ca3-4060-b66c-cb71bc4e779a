package vn.vinclub.partner.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.partner.domain.dto.partner.PartnerCreateDto;
import vn.vinclub.partner.domain.dto.partner.PartnerDto;
import vn.vinclub.partner.domain.dto.partner.PartnerFilterDto;
import vn.vinclub.partner.domain.dto.partner.PartnerUpdateDto;

public interface AdminPartnerService {

    PartnerDto registerPartner(PartnerCreateDto createDto);

    PartnerDto updatePartner(Long id, PartnerUpdateDto updateDto);

    void deletePartner(Long id);

    PartnerDto getPartnerDetails(Long id);

    Page<PartnerDto> searchPartners(PartnerFilterDto filter, Pageable pageable);
}
