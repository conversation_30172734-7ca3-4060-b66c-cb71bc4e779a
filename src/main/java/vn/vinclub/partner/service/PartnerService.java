package vn.vinclub.partner.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.partner.domain.dto.encryption.EncryptKeyData;
import vn.vinclub.partner.domain.dto.partner.PartnerCreateDto;
import vn.vinclub.partner.domain.dto.partner.PartnerFilterDto;
import vn.vinclub.partner.domain.dto.partner.PartnerUpdateDto;
import vn.vinclub.partner.domain.entity.Partner;

import java.util.Optional;
import java.util.Set;

public interface PartnerService {
    Partner create(PartnerCreateDto createDto);

    Partner update(Long id, PartnerUpdateDto updateDto);

    void delete(Long id);

    Partner findById(Long id);

    Partner findByCode(String partnerCode);

    Page<Partner> filter(PartnerFilterDto filter, Pageable pageable);

    Optional<Partner> optById(Long id);

    Optional<Partner> optByCode(String code);

    EncryptKeyData getEncryptKey(String partnerCode);

    void invalidateCache(Long id, String code);

    void addTag(Long partnerId, Set<String> tags);
}