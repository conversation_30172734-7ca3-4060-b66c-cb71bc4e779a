package vn.vinclub.partner.service.impl;

import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.client.InternalCustomerClient;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.request.LinkAccountRequest;
import vn.vinclub.partner.domain.dto.request.PartnerSignedTokenRequest;
import vn.vinclub.partner.domain.dto.response.LinkedAccountResponse;
import vn.vinclub.partner.domain.enums.LinkAccountStep;
import vn.vinclub.partner.domain.mapper.PartnerUserMappingMapper;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.module.account.registry.PartnerLinkAccountServiceRegistry;
import vn.vinclub.partner.module.token.registry.PartnerTokenServiceRegistry;
import vn.vinclub.partner.service.PartnerService;
import vn.vinclub.partner.service.PartnerUserMappingService;
import vn.vinclub.partner.service.WebCustomerService;

import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class WebCustomerServiceImpl implements WebCustomerService {

    private final PartnerService partnerService;
    private final PartnerUserMappingService partnerUserMappingService;
    private final InternalCustomerClient internalCustomerClient;
    private final BaseJsonUtils jsonUtils;

    private final PartnerLinkAccountServiceRegistry partnerLinkAccountServiceRegistry;
    private final PartnerTokenServiceRegistry partnerTokenServiceRegistry;

    @Profiler
    @Override
    public ObjectNode exchangePartnerToken(PartnerSignedTokenRequest signedTokenDto) {
        log.info("process partner token: {}", signedTokenDto.getPartnerSignedToken());
        var partnerUserId = extractPartnerUserIdFromToken(signedTokenDto);

        var partner = partnerService.findByCode(signedTokenDto.getPartnerCode());
        var mapping = partnerUserMappingService.optByPartnerIdAndPartnerUserId(partner.getId(), partnerUserId);

        if (mapping.isEmpty()) {
            log.error("partner_code={}, partner_user_id={} is not linked to any vclub user", partner.getCode(), partnerUserId);
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "Liên kết tài khoản", "partner_user_id", partnerUserId);
        }

        // Exchange to VClub token
        // Builds additional customer claims
        Map<String, Object> additionalClaims = Map.of(
                AppConst.CUSTOMER_JWT_CLAIM.PARTNER_CODE, partner.getCode(),
                AppConst.CUSTOMER_JWT_CLAIM.PARTNER_USER_ID, partnerUserId
        );

        ObjectNode auth = internalCustomerClient.internalLoginByCustomerId(mapping.get().getVclubUserId(), additionalClaims);
        if (Objects.isNull(auth)) {
            throw new BusinessLogicException(AppErrorCode.USER_UNAUTHORIZED);
        }

        // remove refresh token
        auth.withObject("/jwt").remove("refresh_token");

        return auth;
    }

    @Profiler
    @Override
    public LinkedAccountResponse getLinkedAccount(String partnerCode, Long vclubUserId) {
        var partner = partnerService.findByCode(partnerCode);
        var mapping = partnerUserMappingService.optByPartnerIdAndVClubUserId(partner.getId(), vclubUserId);

        if (mapping.isEmpty()) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "Liên kết tài khoản", "vclub_user_id", vclubUserId);
        }
        var linkedAccount = PartnerUserMappingMapper.INSTANCE.toLinkedAccountResponse(mapping.get());
        linkedAccount.setPartnerCode(partner.getCode());

        return linkedAccount;
    }

    @Profiler
    @Override
    public LinkedAccountResponse linkAccount(Long vclubUserId, PartnerSignedTokenRequest signedTokenDto) {
        var partnerUserId = extractPartnerUserIdFromToken(signedTokenDto);

        var linkAccountRequest = LinkAccountRequest.builder()
                .partnerCode(signedTokenDto.getPartnerCode())
                .vclubUserId(vclubUserId)
                .partnerUserId(partnerUserId)
                .step(LinkAccountStep.CALL_LINK_API)
                .build();
        var response = partnerLinkAccountServiceRegistry.getService(signedTokenDto.getPartnerCode()).linkAccount(linkAccountRequest);

        return jsonUtils.treeToValue(response.data(), LinkedAccountResponse.class);
    }

    @Profiler
    private String extractPartnerUserIdFromToken(PartnerSignedTokenRequest signedTokenDto) {
        var tokenService = partnerTokenServiceRegistry.getService(signedTokenDto.getPartnerCode());
        var partnerUserId = tokenService.getPartnerUserId(signedTokenDto.getPartnerSignedToken());

        if (!StringUtils.hasText(partnerUserId)) {
            log.error("Missing partner_user_id in token via token: {}", signedTokenDto.getPartnerSignedToken());
            throw new BusinessLogicException(AppErrorCode.INVALID_SIGNED_TOKEN, "partner_user_id không tồn tại");
        }
        return partnerUserId;
    }
}
