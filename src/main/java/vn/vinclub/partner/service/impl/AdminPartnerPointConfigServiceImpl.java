package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigCreateDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigUpdateDto;
import vn.vinclub.partner.domain.entity.PartnerPointConfig;
import vn.vinclub.partner.domain.mapper.PartnerPointConfigMapper;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.service.AdminPartnerPointConfigService;
import vn.vinclub.partner.service.PartnerPointConfigService;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdminPartnerPointConfigServiceImpl implements AdminPartnerPointConfigService {
    private final PartnerPointConfigService partnerPointConfigService;
    private final PartnerPointConfigMapper partnerPointConfigMapper;

    @Profiler
    @Override
    public PartnerPointConfigDto createPartnerPointConfig(PartnerPointConfigCreateDto createDto) {
        PartnerPointConfig partnerPointConfig = partnerPointConfigService.create(createDto);
        return partnerPointConfigMapper.toDto(partnerPointConfig);
    }

    @Profiler
    @Override
    public PartnerPointConfigDto updatePartnerPointConfig(Long partnerId, Long id, PartnerPointConfigUpdateDto updateDto) {
        PartnerPointConfig partnerPointConfig = partnerPointConfigService.update(partnerId, id, updateDto);
        return partnerPointConfigMapper.toDto(partnerPointConfig);
    }

    @Profiler
    @Override
    public void deletePartnerPointConfig(Long partnerId, Long id) {
        partnerPointConfigService.delete(partnerId, id);
    }

    @Profiler
    @Override
    public PartnerPointConfigDto getPartnerPointConfigDetails(Long partnerId, Long id) {
        PartnerPointConfig partnerPointConfig = partnerPointConfigService.findById(id);

        if (!partnerPointConfig.getPartnerId().equals(partnerId)) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "PartnerPointConfig", "partnerId", partnerId);
        }

        return partnerPointConfigMapper.toDto(partnerPointConfig);
    }

    @Profiler
    @Override
    public Page<PartnerPointConfigDto> searchPartnerPointConfigs(PartnerPointConfigFilterDto filter, Pageable pageable) {
        Page<PartnerPointConfig> partnerPointConfigPage = partnerPointConfigService.filter(filter, pageable);
        return partnerPointConfigPage.map(partnerPointConfigMapper::toDto);
    }

    @Profiler
    @Override
    public PartnerPointConfigDto getPartnerPointConfigByCode(Long partnerId, String code) {
        PartnerPointConfig partnerPointConfig = partnerPointConfigService.findByCode(partnerId, code);
        return partnerPointConfigMapper.toDto(partnerPointConfig);
    }
}
