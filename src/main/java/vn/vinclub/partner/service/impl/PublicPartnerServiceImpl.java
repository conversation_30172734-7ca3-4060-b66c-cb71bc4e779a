package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.HandleRedirectResult;
import vn.vinclub.partner.domain.dto.request.LinkAccountRequest;
import vn.vinclub.partner.domain.enums.LinkAccountStep;
import vn.vinclub.partner.domain.enums.ModuleType;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.module.account.registry.PartnerLinkAccountServiceRegistry;
import vn.vinclub.partner.service.PartnerService;
import vn.vinclub.partner.service.PublicPartnerService;

import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class PublicPartnerServiceImpl implements PublicPartnerService {
    private final BaseJsonUtils jsonUtils;
    private final PartnerLinkAccountServiceRegistry partnerLinkAccountServiceRegistry;
    private final PartnerService partnerService;

    @Profiler
    @Override
    public HandleRedirectResult handleRedirect(String partnerCode, ModuleType module, Map<String, String> params) {
        // validate partner
        var partner = partnerService.findByCode(partnerCode);
        partnerCode = partner.getCode();

        log.info("[handleRedirect] partner_code={}, module={}, params={}", partnerCode, module, params);

        if (MapUtils.isEmpty(params) || !params.containsKey("execution_id")) {
            log.error("[handleRedirect] execution_id is null");
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "execution_id");
        }

        // process redirect
        try {
            if (ModuleType.LINK_ACCOUNT.equals(module)) {
                var response = partnerLinkAccountServiceRegistry.getService(partnerCode).linkAccount(LinkAccountRequest.builder()
                        .step(LinkAccountStep.CALLBACK)
                        .data(jsonUtils.toNode(params))
                        .build());
                return HandleRedirectResult.builder()
                        .partnerCode(partnerCode)
                        .module(module)
                        .nextStep(response.nextStep().name())
                        .success(response.success())
                        .build();
            }
        } catch (Exception e) {
            log.error("[handleRedirect] error when handle redirect", e);
            int errorCode = AppErrorCode.INTERNAL_SERVER_ERROR.getCode();
            String errorMessage = e.getMessage();
            if (e instanceof BusinessLogicException ex) {
                errorCode = ex.getPayload().getCode();
                errorMessage = ex.getPayload().getMessage().getFirst();
            }
            return HandleRedirectResult.builder()
                    .partnerCode(partnerCode)
                    .module(module)
                    .success(false)
                    .errorCode(errorCode)
                    .errorMessage(errorMessage)
                    .build();
        }

        throw new IllegalArgumentException("Module not support: " + module);
    }
}
