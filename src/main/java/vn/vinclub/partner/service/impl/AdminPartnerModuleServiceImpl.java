package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.module.PartnerModuleCreateDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleFilterDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleUpdateDto;
import vn.vinclub.partner.domain.entity.PartnerModule;
import vn.vinclub.partner.domain.mapper.PartnerModuleMapper;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.service.AdminPartnerModuleService;
import vn.vinclub.partner.service.PartnerModuleService;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdminPartnerModuleServiceImpl implements AdminPartnerModuleService {

    private final PartnerModuleService partnerModuleService;
    private final PartnerModuleMapper partnerModuleMapper;
    private final BaseJsonUtils jsonUtils;

    @Profiler
    @Override
    public PartnerModuleDto createPartnerModule(PartnerModuleCreateDto createDto) {
        PartnerModule partnerModule = partnerModuleService.create(createDto);
        return partnerModuleMapper.toDto(partnerModule);
    }

    @Profiler
    @Override
    public PartnerModuleDto updatePartnerModule(Long partnerId, Long id, PartnerModuleUpdateDto updateDto) {
        PartnerModule partnerModule = partnerModuleService.update(partnerId, id, updateDto);
        return partnerModuleMapper.toDto(partnerModule);
    }

    @Profiler
    @Override
    public void deletePartnerModule(Long partnerId, Long id) {
        partnerModuleService.delete(partnerId, id);
    }

    @Profiler
    @Override
    public PartnerModuleDto getPartnerModuleDetails(Long partnerId, Long id) {
        PartnerModule partnerModule = partnerModuleService.findById(id);

        if (!partnerModule.getPartnerId().equals(partnerId)) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "PartnerModule", "partnerId", partnerId);
        }

        return partnerModuleMapper.toDto(partnerModule);
    }

    @Profiler
    @Override
    public Page<PartnerModuleDto> searchPartnerModules(PartnerModuleFilterDto filter, Pageable pageable) {
        Page<PartnerModule> partnerModulePage = partnerModuleService.filter(filter, pageable);
        return partnerModulePage.map(partnerModuleMapper::toDto);
    }
}
