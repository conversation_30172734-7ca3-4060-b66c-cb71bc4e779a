package vn.vinclub.partner.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import vn.vinclub.partner.domain.entity.SnowflakeId;
import vn.vinclub.partner.service.DistributedIdGenerator;

import java.time.Duration;


/**
 * <AUTHOR> 12/12/24 11:48
 */
@Slf4j
public class SnowflakeNodeIdBasedRedis implements DistributedIdGenerator {

    private final SnowflakeId snowflakeId;
    private final String prefixId;

    public SnowflakeNodeIdBasedRedis(RedissonClient redissonClient, String group, String prefixId) {

        var bucket = redissonClient.getAtomicLong(String.format("snowflake-id-gen:%s", group));
        long nodeId = bucket.incrementAndGet();
        bucket.expireAsync(Duration.ofDays(365));

        this.prefixId = prefixId;
        this.snowflakeId = new SnowflakeId(group, nodeId);

    }

    public SnowflakeNodeIdBasedRedis(RedissonClient redissonClient, String group) {
        this(redissonClient, group, "");
    }

    @Override
    public Long nextId() {
        return this.snowflakeId.nextId();
    }

    @Override
    public String nextIdAsString() {
        if (this.prefixId == null || this.prefixId.isEmpty()) {
            return String.valueOf(this.nextId());
        }
        return String.format("%s%s", this.prefixId, this.nextId());
    }

}
