package vn.vinclub.partner.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.module.PartnerModuleCreateDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleFilterDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleUpdateDto;
import vn.vinclub.partner.domain.dto.module.config.*;
import vn.vinclub.partner.domain.entity.Partner;
import vn.vinclub.partner.domain.entity.PartnerModule;
import vn.vinclub.partner.domain.enums.IntegrationTag;
import vn.vinclub.partner.domain.enums.ModuleType;
import vn.vinclub.partner.domain.enums.PartnerStatus;
import vn.vinclub.partner.domain.mapper.PartnerModuleMapper;
import vn.vinclub.partner.domain.specification.PartnerModuleSpecification;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.redis.RedisPublish;
import vn.vinclub.partner.repository.PartnerModuleRepository;
import vn.vinclub.partner.service.PartnerModuleService;
import vn.vinclub.partner.service.PartnerPointConfigService;
import vn.vinclub.partner.service.PartnerService;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class PartnerModuleServiceImpl implements PartnerModuleService {

    private final PartnerModuleRepository partnerModuleRepository;
    private final PartnerModuleMapper partnerModuleMapper;
    private final PartnerService partnerService;
    private final RedisPublish redisPublish;

    private final Cache<Long, PartnerModule> cacheById = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .maximumSize(20)
            .build();

    private final Cache<String, Long> partnerModuleCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .maximumSize(20)
            .build();

    private final Cache<String, List<PartnerModule>> listCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(20)
            .build();
    private final BaseJsonUtils jsonUtils;
    private final PartnerPointConfigService partnerPointConfigService;

    @Profiler
    @Transactional
    @Override
    public PartnerModule create(PartnerModuleCreateDto createDto) {
        log.debug("Creating PartnerModule: {}", createDto);

        // Verify partner exists
        partnerService.findById(createDto.getPartnerId());

        // Check if module already exists for this partner
        Optional<PartnerModule> existingModule = optByPartnerAndModule(createDto.getPartnerId(), createDto.getModule());
        if (existingModule.isPresent() && existingModule.get().isEnabled()) {
            throw new BusinessLogicException(AppErrorCode.OBJECT_EXISTED, "PartnerModule", "module", createDto.getModule());
        }

        PartnerModule partnerModule = partnerModuleMapper.toEntity(createDto);
        var newIntegrationTags = validateConfig(partnerModule);

        partnerModule.setEnabled(true);
        save(partnerModule, newIntegrationTags);

        log.debug("Created PartnerModule with id: {}", partnerModule.getId());
        return partnerModule;
    }

    @Profiler
    @Transactional
    @Override
    public PartnerModule update(Long partnerId, Long id, PartnerModuleUpdateDto updateDto) {
        log.debug("Updating PartnerModule with id: {} and data: {}", id, updateDto);

        Partner partner = partnerService.findById(partnerId);
        PartnerModule partnerModule = findById(id);

        if (!partnerModule.getPartnerId().equals(partner.getId())) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "PartnerModule", "partnerId", partnerId);
        }

        partnerModuleMapper.partialUpdate(updateDto, partnerModule);
        var newIntegrationTags = validateConfig(partnerModule);
        save(partnerModule, newIntegrationTags);

        log.debug("Updated PartnerModule with id: {}", partnerModule.getId());
        return partnerModule;
    }

    @Profiler
    @Transactional
    @Override
    public void delete(Long partnerId, Long id) {
        log.debug("Deleting PartnerModule with id: {}", id);

        Partner partner = partnerService.findById(partnerId);
        PartnerModule partnerModule = findById(id);

        if (!partnerModule.getPartnerId().equals(partner.getId())) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "PartnerModule", "partnerId", partnerId);
        }

        // Soft delete by setting enabled to false
        partnerModule.setEnabled(false);
        save(partnerModule, null);

        log.debug("Deleted (soft) PartnerModule with id: {}", id);
    }

    @Profiler
    @Override
    public PartnerModule findById(Long id) {
        log.debug("Finding PartnerModule by id: {}", id);
        Optional<PartnerModule> partnerModule = opt(id);
        if (partnerModule.isEmpty() || !partnerModule.get().isEnabled()) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "PartnerModule", "id", id);
        }
        return partnerModule.get();
    }

    @Profiler
    @Override
    public Optional<PartnerModule> optByPartnerAndModule(Long partnerId, ModuleType module) {
        log.debug("Finding PartnerModule by partnerId: {} and module: {}", partnerId, module);

        String cacheKey = partnerId + ":" + module;
        Long id = partnerModuleCache.getIfPresent(cacheKey);

        if (id != null) {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "optByPartnerAndModule.hit")) {
                return opt(id);
            }
        }

        try (var p = new vn.vinclub.common.model.Profiler(getClass(), "optByPartnerAndModule.miss")) {
            Optional<PartnerModule> optionalPartnerModule = partnerModuleRepository.findByPartnerIdAndModule(partnerId, module)
                    .filter(PartnerModule::isEnabled);
            if (optionalPartnerModule.isPresent()) {
                PartnerModule partnerModule = optionalPartnerModule.get();
                cacheById.put(partnerModule.getId(), partnerModule);
                partnerModuleCache.put(cacheKey, partnerModule.getId());
            }
            return optionalPartnerModule;
        }
    }

    @Profiler
    @Override
    public PartnerModule findByPartnerAndModule(Long partnerId, ModuleType module) {
        log.debug("Finding PartnerModule by partnerId: {} and module: {}", partnerId, module);
        Optional<PartnerModule> partnerModule = optByPartnerAndModule(partnerId, module);
        if (partnerModule.isEmpty() || !partnerModule.get().isEnabled()) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "PartnerModule", "partnerId", partnerId);
        }
        return partnerModule.get();
    }

    @Profiler
    @Override
    public List<PartnerModule> findEnabledByModule(ModuleType module) {
        return partnerModuleRepository.findByModuleAndEnabledIsTrue(module);
    }

    @Profiler
    @Override
    public Page<PartnerModule> filter(PartnerModuleFilterDto filter, Pageable pageable) {
        return partnerModuleRepository.findAll(PartnerModuleSpecification.getSpecification(filter), pageable);
    }

    /**
     * Invalidate caches for a partner module
     * This method is called by the RedisListener when a CHANGE_PARTNER_MODULE message is received
     *
     * @param id the ID of the partner module
     */
    @Override
    @Profiler
    public void invalidateCache(Long id) {
        log.debug("Invalidating cache for PartnerModule with id: {}", id);

        // Get the module from cache if available
        PartnerModule partnerModule = cacheById.getIfPresent(id);

        // Invalidate entity cache
        cacheById.invalidate(id);

        // If we have the module in cache, invalidate related caches
        if (partnerModule != null) {
            // Invalidate module cache
            String moduleKey = partnerModule.getPartnerId() + ":" + partnerModule.getModule();
            partnerModuleCache.invalidate(moduleKey);

            // Invalidate list caches
            String partnerListKey = "partner:" + partnerModule.getPartnerId();
            String enabledListKey = "partner:" + partnerModule.getPartnerId() + ":enabled";
            listCache.invalidate(partnerListKey);
            listCache.invalidate(enabledListKey);
        }
    }

    private Optional<PartnerModule> opt(long id) {
        PartnerModule cachedPartnerModule = cacheById.getIfPresent(id);
        if (cachedPartnerModule != null) {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "opt.hit")) {
                return Optional.of(cachedPartnerModule);
            }
        }
        try (var p = new vn.vinclub.common.model.Profiler(getClass(), "opt.miss")) {
            Optional<PartnerModule> optionalPartnerModule = partnerModuleRepository.findById(id).filter(PartnerModule::isEnabled);
            if (optionalPartnerModule.isEmpty()) {
                return Optional.empty();
            }
            PartnerModule partnerModule = optionalPartnerModule.get();
            cacheById.put(id, partnerModule);
            String cacheKey = partnerModule.getPartnerId() + ":" + partnerModule.getModule();
            partnerModuleCache.put(cacheKey, partnerModule.getId());
            return Optional.of(partnerModule);
        }
    }

    private Set<String> validateConfig(PartnerModule partnerModule) {
        if (partnerModule == null) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "partnerModule", "PartnerModule cannot be null");
        }

        if (partnerModule.getModule() == null) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "module", "Module type is required");
        }

        Set<String> integrationTags = new HashSet<>();

        switch (partnerModule.getModule()) {
            case LINK_ACCOUNT:
                integrationTags.addAll(validateLinkAccountConfig(partnerModule));
                break;
            case TOPUP_POINT:
                integrationTags.addAll(validateTopUpPointConfig(partnerModule));
                break;
            case SPEND_POINT:
                integrationTags.addAll(validateSpendPointConfig(partnerModule));
                break;
            case SWAP_POINT:
                integrationTags.addAll(validatePointSwapConfig(partnerModule));
                break;
            default:
                break;
        }
        return integrationTags;
    }

    private Set<String> validateLinkAccountConfig(PartnerModule partnerModule) {
        var configNode = partnerModule.getConfig();
        if (configNode == null) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config");
        }
        var config = jsonUtils.treeToValue(configNode, LinkAccountConfig.class);
        if (config == null) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config", "Cấu hình Liên kết tài khoản không hợp lệ");
        }
        if (MapUtils.isEmpty(config.getTermsAndConditions())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.termsAndConditions");
        }

        if (config.isAllowUnlink() && config.getUnLinkTermsAndConditions() == null) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.unLinkTermsAndConditions");
        }

        configNode = jsonUtils.valueToTree(config);
        partnerModule.setConfig(configNode);

        // build integration tags
        var tags = new HashSet<String>();
        tags.add(IntegrationTag.LINK_ACCOUNT.toValue());
        if (config.isAllowUnlink()) {
            tags.add(IntegrationTag.UNLINK_ACCOUNT.toValue());
        }
        return tags;
    }

    private Set<String> validateTopUpPointConfig(PartnerModule partnerModule) {
        var configNode = partnerModule.getConfig();
        if (configNode == null) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config");
        }
        var config = jsonUtils.treeToValue(configNode, TopUpPointConfig.class);

        if (config == null) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config", "Cấu hình nạp điểm không hợp lệ");
        }

        if (!config.isPartnerEnabled() && !config.isVclubEnabled()) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.vclubEnabled or config.partnerEnabled");
        }

        var tags = new HashSet<String>();

        // Validate partner configuration
        if (config.isPartnerEnabled()) {
            if (CollectionUtils.isEmpty(config.getAllowPartnerPointCodes())) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.allowPartnerPointCodes");
            }
            config.getAllowPartnerPointCodes().forEach(code -> {
                var pointConfig = partnerPointConfigService.findByCode(partnerModule.getPartnerId(), code);
                if (!PartnerStatus.ACTIVE.equals(pointConfig.getStatus())) {
                    throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.allowPartnerPointCodes", String.format("Partner point code = %s is not ACTIVE", code));
                }
            });

            // Validate partner transaction limit
            if (MapUtils.isNotEmpty(config.getMaxPartnerPointPerTransactionByPointCode())) {
                config.getMaxPartnerPointPerTransactionByPointCode().forEach((code, limit) -> {
                    if (!config.getAllowPartnerPointCodes().contains(code)) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.maxPartnerPointPerTransactionByPointCode", String.format("Point code = %s is not allowed", code));
                    }
                    if (limit == null || limit <= 0) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.maxPartnerPointPerTransactionByPointCode", String.format("Limit for point code = %s must be greater than 0", code));
                    }
                });
            }

            // Validate partner daily quota
            if (MapUtils.isNotEmpty(config.getPartnerDailyQuotaByPointCode())) {
                config.getPartnerDailyQuotaByPointCode().forEach((code, quota) -> {
                    if (!config.getAllowPartnerPointCodes().contains(code)) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.partnerDailyQuotaByPointCode", String.format("Point code = %s is not allowed", code));
                    }
                    if (quota == null || quota <= 0) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.partnerDailyQuotaByPointCode", String.format("Quota for point code = %s must be greater than 0", code));
                    }
                });
            }

            // Validate partner monthly quota
            if (MapUtils.isNotEmpty(config.getPartnerMonthlyQuotaByPointCode())) {
                config.getPartnerMonthlyQuotaByPointCode().forEach((code, quota) -> {
                    if (!config.getAllowPartnerPointCodes().contains(code)) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.partnerMonthlyQuotaByPointCode", String.format("Point code = %s is not allowed", code));
                    }
                    if (quota == null || quota <= 0) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.partnerMonthlyQuotaByPointCode", String.format("Quota for point code = %s must be greater than 0", code));
                    }
                });
            }

            configNode = jsonUtils.valueToTree(config);
            partnerModule.setConfig(configNode);

            tags.add(IntegrationTag.TOPUP_PARTNER_POINT.toValue());
        }

        // Validate vclub configuration
        if (config.isVclubEnabled()) {
            // Validate vclub transaction limit
            if (Objects.nonNull(config.getMaxVclubPointPerTransaction())) {
                if (config.getMaxVclubPointPerTransaction() <= 0) {
                    throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.maxVclubPointPerTransaction", "config.maxVclubPointPerTransaction must be greater than 0");
                }
            }

            // Validate vclub daily quota
            if (Objects.nonNull(config.getVclubDailyQuota())) {
                if (config.getVclubDailyQuota() <= 0) {
                    throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.vclubDailyQuota", "config.vclubDailyQuota must be greater than 0");
                }
            }

            // Validate vclub monthly quota
            if (Objects.nonNull(config.getVclubMonthlyQuota())) {
                if (config.getVclubMonthlyQuota() <= 0) {
                    throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.vclubMonthlyQuota", "config.vclubMonthlyQuota must be greater than 0");
                }
            }
            tags.add(IntegrationTag.TOPUP_VCLUB_POINT.toValue());
        }
        return tags;
    }

    private Set<String> validateSpendPointConfig(PartnerModule partnerModule) {
        var configNode = partnerModule.getConfig();
        if (configNode == null) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config");
        }
        var config = jsonUtils.treeToValue(configNode, SpendPointConfig.class);

        if (config == null) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config", "Cấu hình tiêu điểm không hợp lệ");
        }

        if (!config.isPartnerEnabled() && !config.isVclubEnabled()) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.partnerEnabled or config.vclubEnabled");
        }

        var tags = new HashSet<String>();

        // Validate partner configuration
        if (config.isPartnerEnabled()) {
            if (CollectionUtils.isEmpty(config.getAllowPartnerPointCodes())) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.allowPartnerPointCodes");
            }
            config.getAllowPartnerPointCodes().forEach(code -> {
                var pointConfig = partnerPointConfigService.findByCode(partnerModule.getPartnerId(), code);
                if (!PartnerStatus.ACTIVE.equals(pointConfig.getStatus())) {
                    throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.allowPartnerPointCodes", String.format("Partner point code = %s is not ACTIVE", code));
                }
            });

            // Validate partner transaction limit
            if (MapUtils.isNotEmpty(config.getMaxPartnerPointPerTransactionByPointCode())) {
                config.getMaxPartnerPointPerTransactionByPointCode().forEach((code, limit) -> {
                    if (!config.getAllowPartnerPointCodes().contains(code)) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.maxPartnerPointPerTransactionByPointCode", String.format("Point code = %s is not allowed", code));
                    }
                    if (limit == null || limit <= 0) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.maxPartnerPointPerTransactionByPointCode", String.format("Limit for point code = %s must be greater than 0", code));
                    }
                });
            }

            // Validate partner daily quota
            if (MapUtils.isNotEmpty(config.getPartnerDailyQuotaByPointCode())) {
                config.getPartnerDailyQuotaByPointCode().forEach((code, quota) -> {
                    if (!config.getAllowPartnerPointCodes().contains(code)) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.partnerDailyQuotaByPointCode", String.format("Point code = %s is not allowed", code));
                    }
                    if (quota == null || quota <= 0) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.partnerDailyQuotaByPointCode", String.format("Quota for point code = %s must be greater than 0", code));
                    }
                });
            }

            // Validate partner monthly quota
            if (MapUtils.isNotEmpty(config.getPartnerMonthlyQuotaByPointCode())) {
                config.getPartnerMonthlyQuotaByPointCode().forEach((code, quota) -> {
                    if (!config.getAllowPartnerPointCodes().contains(code)) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.partnerMonthlyQuotaByPointCode", String.format("Point code = %s is not allowed", code));
                    }
                    if (quota == null || quota <= 0) {
                        throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.partnerMonthlyQuotaByPointCode", String.format("Quota for point code = %s must be greater than 0", code));
                    }
                });
            }

            configNode = jsonUtils.valueToTree(config);
            partnerModule.setConfig(configNode);

            tags.add(IntegrationTag.SPEND_PARTNER_POINT.toValue());
        }

        // Validate vclub configuration
        if (config.isVclubEnabled()) {
            // Validate vclub transaction limit
            if (Objects.nonNull(config.getMaxVclubPointPerTransaction())) {
                if (config.getMaxVclubPointPerTransaction() <= 0) {
                    throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.maxVclubPointPerTransaction", "config.maxVclubPointPerTransaction must be greater than 0");
                }
            }

            // Validate vclub daily quota
            if (Objects.nonNull(config.getVclubDailyQuota())) {
                if (config.getVclubDailyQuota() <= 0) {
                    throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.vclubDailyQuota", "config.vclubDailyQuota must be greater than 0");
                }
            }

            // Validate vclub monthly quota
            if (Objects.nonNull(config.getVclubMonthlyQuota())) {
                if (config.getVclubMonthlyQuota() <= 0) {
                    throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.vclubMonthlyQuota", "config.vclubMonthlyQuota must be greater than 0");
                }
            }
            tags.add(IntegrationTag.SPEND_VCLUB_POINT.toValue());
        }
        return tags;
    }

    private Set<String> validatePointSwapConfig(PartnerModule partnerModule) {
        var configNode = partnerModule.getConfig();
        if (configNode == null) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config");
        }
        var config = jsonUtils.treeToValue(configNode, SwapPointConfig.class);

        if (config == null) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config", "Cấu hình Liên kết tài khoản không hợp lệ");
        }

        if (!config.isPartnerToVclubEnabled() && !config.isVclubToPartnerEnabled()) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.partnerToVclubEnabled hoặc config.vclubToPartnerEnabled");
        }

        // validate limit
        if (config.isLimitEnabled()) {
            if (config.getMinAmount() == null || config.getMinAmount() <= 0) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.minAmount");
            }
            if (config.getMaxAmount() == null || config.getMaxAmount() <= 0) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.maxAmount");
            }
            if (config.getMinAmount() > config.getMaxAmount()) {
                throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.minAmount", "config.minAmount phải nhỏ hơn config.maxAmount");
            }
        }

        // validate conversion rule
        var conversionRule = config.getConversionRule();
        if (conversionRule == null) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.conversionRule");
        }
        if (conversionRule.getType() == null) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.conversionRule.type");
        }

        switch (conversionRule.getType()) {
            case FIXED:
                validateFixedConversionRule(conversionRule, config);
                break;
            case CASH_EQUIVALENT:
                validateCashEquivalentConversionRule(conversionRule);
                break;
            case AMOUNT_BASED:
                validateAmountBasedConversionRule(conversionRule, config);
                break;
            case TIME_BASED:
                validateTimeBasedConversionRule(conversionRule, config);
                break;
            default:
                throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "config.conversionRule.type", "Cấu hình quy đổi điểm không hợp lệ");
        }
        
        configNode = jsonUtils.valueToTree(config);
        partnerModule.setConfig(configNode);

        return Set.of(IntegrationTag.SWAP_POINT.toValue());
    }

    private void validateFixedConversionRule(PointConversionRule conversionRule, SwapPointConfig config) {
        if (config.isVclubToPartnerEnabled() && (conversionRule.getToPartnerRate() == null || conversionRule.getToPartnerRate().compareTo(BigDecimal.ZERO) <= 0)) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.conversionRule.toPartnerRate");
        }
        if (config.isPartnerToVclubEnabled() && (conversionRule.getToVinClubRate() == null || conversionRule.getToVinClubRate().compareTo(BigDecimal.ZERO) <= 0)) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.conversionRule.toVinClubRate");
        }
    }

    private void validateCashEquivalentConversionRule(PointConversionRule conversionRule) {
        // Cash equivalent conversion rule doesn't depend on direction
        if (conversionRule.getPartnerCashValue() == null || conversionRule.getPartnerCashValue().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.conversionRule.partnerCashValue");
        }
    }

    private void validateAmountBasedConversionRule(PointConversionRule conversionRule, SwapPointConfig config) {
        if (config.isVclubToPartnerEnabled()) {
            if (conversionRule.getToPartnerRateByAmount() == null || conversionRule.getToPartnerRateByAmount().isEmpty()) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.conversionRule.toPartnerRateByAmount");
            }
            validateRateByRangeList(conversionRule.getToPartnerRateByAmount(), "config.conversionRule.toPartnerRateByAmount");
        }

        if (config.isPartnerToVclubEnabled()) {
            if (conversionRule.getToVinClubRateByAmount() == null || conversionRule.getToVinClubRateByAmount().isEmpty()) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.conversionRule.toVinClubRateByAmount");
            }
            validateRateByRangeList(conversionRule.getToVinClubRateByAmount(), "config.conversionRule.toVinClubRateByAmount");
        }
    }

    private void validateTimeBasedConversionRule(PointConversionRule conversionRule, SwapPointConfig config) {
        if (config.isVclubToPartnerEnabled()) {
            if (conversionRule.getToPartnerRateByTime() == null || conversionRule.getToPartnerRateByTime().isEmpty()) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.conversionRule.toPartnerRateByTime");
            }
            validateRateByRangeList(conversionRule.getToPartnerRateByTime(), "config.conversionRule.toPartnerRateByTime");
        }

        if (config.isPartnerToVclubEnabled()) {
            if (conversionRule.getToVinClubRateByTime() == null || conversionRule.getToVinClubRateByTime().isEmpty()) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "config.conversionRule.toVinClubRateByTime");
            }
            validateRateByRangeList(conversionRule.getToVinClubRateByTime(), "config.conversionRule.toVinClubRateByTime");
        }
    }

    private void validateRateByRangeList(List<RateByRange> rateByRangeList, String fieldName) {
        for (int i = 0; i < rateByRangeList.size(); i++) {
            RateByRange rateByRange = rateByRangeList.get(i);
            String indexedFieldName = fieldName + "[" + i + "]";

            if (rateByRange.getFrom() == null) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, indexedFieldName + ".from");
            }
            if (rateByRange.getTo() == null) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, indexedFieldName + ".to");
            }
            if (rateByRange.getRate() == null || rateByRange.getRate().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, indexedFieldName + ".rate");
            }

            if (rateByRange.getFrom() >= rateByRange.getTo()) {
                throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, indexedFieldName + ".from",
                        "Giá trị from phải nhỏ hơn giá trị to");
            }

            // Check for overlapping ranges
            if (i > 0) {
                RateByRange previousRange = rateByRangeList.get(i - 1);
                if (rateByRange.getFrom() <= previousRange.getTo()) {
                    throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, indexedFieldName + ".from",
                            "Các khoảng không được chồng chéo nhau");
                }
            }
        }
    }

    @Profiler
    @Transactional
    protected void save(PartnerModule partnerModule, Set<String> newIntegrationTags) {
        partnerModuleRepository.save(partnerModule);

        if (CollectionUtils.isNotEmpty(newIntegrationTags)) {
            partnerService.addTag(partnerModule.getPartnerId(), newIntegrationTags);
        }

        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublish.sendChange(AppConst.RedisMessage.CHANGE_PARTNER_MODULE, partnerModule.getId());
                }
            });
        } else {
            redisPublish.sendChange(AppConst.RedisMessage.CHANGE_PARTNER_MODULE, partnerModule.getId());
        }
    }
}
