package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.domain.entity.OutboxEvent;
import vn.vinclub.partner.domain.enums.OutboxStatusEnum;
import vn.vinclub.partner.domain.event.BaseEvent;
import vn.vinclub.partner.domain.event.audit.ExternalActivityLogEvent;
import vn.vinclub.partner.domain.event.audit.IntegrationLogEvent;
import vn.vinclub.partner.domain.event.internal.PartnerPointTxnRequestEvent;
import vn.vinclub.partner.domain.event.internal.PartnerPointTxnResponseEvent;
import vn.vinclub.partner.kafka.KafkaProducer;
import vn.vinclub.partner.repository.OutboxEventRepository;
import vn.vinclub.partner.service.EventService;

import java.util.Objects;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EventServiceImpl implements EventService {

    private final OutboxEventRepository outboxEventRepository;
    private final KafkaProducer kafkaProducer;

    @Value("${kafka.partner_point_txn_request.topic.name}")
    private String partnerPointTxnRequestKafkaTopic;

    @Value("${kafka.partner_point_txn_response.topic.name}")
    private String partnerPointTxnResponseKafkaTopic;

    @Value("${kafka.log.external_activity_audit.topic.name}")
    private String externalActivityAuditKafkaTopic;

    @Value("${kafka.log.vclub_integration_audit.topic.name}")
    private String vclubIntegrationAuditKafkaTopic;

    @Override
    public <T extends BaseEvent> void persistOutboxEvent(T event, String outboxEventId) {
        try (var p = new Profiler(getClass(), "persistOutboxEvent")) {
            OutboxEvent outboxEvent = new OutboxEvent();
            outboxEvent.setPayload(JsonUtils.toNode(event));
            outboxEvent.setEventId(outboxEventId);
            outboxEvent.setStatus(OutboxStatusEnum.WAITING);
            outboxEvent.setEventCode(event.getEventCode());
            outboxEvent.setMessageKey(event.getKafkaMessageKey());
            if (Objects.nonNull(event.getOutBoxMetadata())) {
                outboxEvent.setMetadata(event.getOutBoxMetadata());
            }
            outboxEventRepository.save(outboxEvent);
        }
    }

    @Override
    public void processPendingOutboxEvent() {
        try (var p = new Profiler(getClass(), "processPendingOutboxEvent")) {
            outboxEventRepository.findAllByStatusOrderByIdAsc(OutboxStatusEnum.WAITING)
                    .forEach(outboxEvent -> {
                        try {
                            sendOutboxEvent(outboxEvent.getEventId(), outboxEvent);
                        } catch (Exception e) {
                            log.error("Error when processing pending outbox {}", JsonUtils.toString(outboxEvent), e);
                            throw new RuntimeException(e);
                        }
                    });
        }
    }

    @Override
    public void sendOutboxEvent(String outboxEventId, OutboxEvent outboxEvent) throws ExecutionException, InterruptedException {
        try (var p = new Profiler(getClass(), "sendOutboxEvent")) {
            if (outboxEvent == null) {
                outboxEvent = outboxEventRepository.findByEventId(outboxEventId).orElse(null);
            }

            if (outboxEvent == null) {
                log.error("Outbox event not found for ID: {}", outboxEventId);
                return;
            }

            if (OutboxStatusEnum.SENT.equals(outboxEvent.getStatus())) {
                log.warn("Outbox event already sent: {}", outboxEventId);
                return;
            }

            String messageKey = getMessageKeyByOutboxEvent(outboxEvent);
            String topic = getTopicByOutboxEvent(outboxEvent);
            kafkaProducer.sendMessage(topic, messageKey, JsonUtils.toString(outboxEvent.getPayload()));

            outboxEvent.setEventId(outboxEventId);
            outboxEvent.setStatus(OutboxStatusEnum.SENT);
            outboxEventRepository.save(outboxEvent);
        }
    }

    @Override
    public <T extends BaseEvent> void sendEvent(T event) {
        try (var p = new Profiler(getClass(), "sendEvent")) {
            String messageKey = getMessageKeyByEvent(event);
            String topic = getTopicByEvent(event);
            kafkaProducer.sendMessage(topic, messageKey, JsonUtils.toString(event));
        } catch (ExecutionException | InterruptedException e) {
            log.error("Error when sending event {}", JsonUtils.toString(event), e);
        }
    }

    private String getTopicByEvent(BaseEvent event) {
        return getByEventCode(event.getEventCode());
    }

    private String getTopicByOutboxEvent(OutboxEvent outboxEvent) {
        return getByEventCode(outboxEvent.getEventCode());
    }

    private String getByEventCode(String eventCode) {
        return switch (eventCode) {
            case PartnerPointTxnRequestEvent.EVENT_CODE -> partnerPointTxnRequestKafkaTopic;
            case PartnerPointTxnResponseEvent.EVENT_CODE -> partnerPointTxnResponseKafkaTopic;
            case ExternalActivityLogEvent.EVENT_CODE -> externalActivityAuditKafkaTopic;
            case IntegrationLogEvent.EVENT_CODE -> vclubIntegrationAuditKafkaTopic;
            default -> throw new IllegalArgumentException("Unknown event code: " + eventCode);
        };
    }

    private String getMessageKeyByEvent(BaseEvent event) {
        return event.getKafkaMessageKey();
    }

    private String getMessageKeyByOutboxEvent(OutboxEvent outboxEvent) {
        return outboxEvent.getMessageKey();
    }

}
