package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.client.InternalCoreClient;
import vn.vinclub.partner.client.response.CoreCustomerBalanceDto;
import vn.vinclub.partner.client.response.CoreCustomerDto;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.external.customer.UnlinkAccountRequestDto;
import vn.vinclub.partner.domain.dto.external.customer.VClubCustomerBalanceDto;
import vn.vinclub.partner.domain.dto.external.customer.VClubCustomerProfileDto;
import vn.vinclub.partner.domain.dto.mapping.PartnerUserMappingUpdateDto;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.redis.VClubLock;
import vn.vinclub.partner.service.IntegrationCustomerService;
import vn.vinclub.partner.service.PartnerService;
import vn.vinclub.partner.service.PartnerUserMappingService;

import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class IntegrationCustomerServiceImpl implements IntegrationCustomerService {
    private final PartnerService partnerService;
    private final PartnerUserMappingService partnerUserMappingService;
    private final InternalCoreClient internalCoreClient;
    private final RedissonClient redissonClient;

    @Profiler
    @Override
    public VClubCustomerProfileDto getCustomerProfile(String partnerCode, String partnerUserId) {
        var partner = partnerService.findByCode(partnerCode);
        var mapping = partnerUserMappingService.findByPartnerIdAndPartnerUserId(partner.getId(), partnerUserId);

        CoreCustomerDto customerDto = internalCoreClient.getCustomerById(mapping.getVclubUserId(), true, false);
        if (Objects.isNull(customerDto)) {
            log.warn("vclub_user_id={} is not found", mapping.getVclubUserId());
            throw new BusinessLogicException(AppErrorCode.LINKED_VINCLUB_ACCOUNT_NOT_EXIST);
        }

        return VClubCustomerProfileDto.from(customerDto);
    }

    @Profiler
    @Override
    public VClubCustomerBalanceDto getCustomerPointBalance(String partnerCode, String partnerUserId) {
        var partner = partnerService.findByCode(partnerCode);
        var mapping = partnerUserMappingService.findByPartnerIdAndPartnerUserId(partner.getId(), partnerUserId);

        CoreCustomerBalanceDto customerBalanceDto = internalCoreClient.getCustomerBalanceById(mapping.getVclubUserId());
        if (Objects.isNull(customerBalanceDto)) {
            log.warn("vclub_user_id={} does not have a balance account", mapping.getVclubUserId());
            throw new BusinessLogicException(AppErrorCode.VINCLUB_BALANCE_ACCOUNT_NOT_CREATED);
        }

        return VClubCustomerBalanceDto.from(customerBalanceDto);
    }

    @Profiler
    @Override
    public Boolean unlinkAccount(String partnerCode, UnlinkAccountRequestDto request) {
        var partner = partnerService.findByCode(partnerCode);
        var lockKey = String.format("%s-%s-%s", partnerCode, request.getPartnerUserId(), request.getVclubUserId());
        try (var lock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_USER_MAPPING_CUD + lockKey)) {
            // Check if mapping is already deleted
            var isMapped = partnerUserMappingService.isMapped(partner.getId(), request.getPartnerUserId(), request.getVclubUserId());
            if (!isMapped) {
                log.error("partner_code={}, partner_user_id={}, vclub_user_id={} is not mapped", partnerCode, request.getPartnerUserId(), request.getVclubUserId());
                throw new BusinessLogicException(AppErrorCode.ACCOUNT_LINKED_INFO_MISMATCH);
            }

            // Build update mapping request
            var mappingId = partnerUserMappingService.findByPartnerIdAndPartnerUserId(partner.getId(), request.getPartnerUserId()).getId();
            var updateDto = PartnerUserMappingUpdateDto.builder()
                    .active(false)
                    .deleteAt(Objects.isNull(request.getTimestamp()) ? System.currentTimeMillis() : request.getTimestamp())
                    .build();

            var updatedMapping = partnerUserMappingService.update(mappingId, updateDto);

            return Objects.nonNull(updatedMapping);
        }
    }
}
