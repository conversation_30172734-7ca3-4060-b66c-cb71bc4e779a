package vn.vinclub.partner.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigCreateDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigUpdateDto;
import vn.vinclub.partner.domain.entity.Partner;
import vn.vinclub.partner.domain.entity.PartnerPointConfig;
import vn.vinclub.partner.domain.enums.PartnerStatus;
import vn.vinclub.partner.domain.mapper.PartnerPointConfigMapper;
import vn.vinclub.partner.domain.specification.PartnerPointConfigSpecification;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.redis.RedisPublish;
import vn.vinclub.partner.repository.PartnerPointConfigRepository;
import vn.vinclub.partner.service.PartnerPointConfigService;
import vn.vinclub.partner.service.PartnerService;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class PartnerPointConfigServiceImpl implements PartnerPointConfigService {

    private final PartnerService partnerService;
    private final PartnerPointConfigRepository repository;
    private final PartnerPointConfigMapper mapper;

    private final Cache<String, Long> codeCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .maximumSize(20)
            .build();

    private final Cache<Long, PartnerPointConfig> idCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .maximumSize(20)
            .build();
    private final RedisPublish redisPublish;

    @Profiler
    @Transactional
    @Override
    public PartnerPointConfig create(PartnerPointConfigCreateDto createDto) {
        // Verify partner exists
        partnerService.findById(createDto.getPartnerId());

        // Check if a config with the same code already exists
        Optional<PartnerPointConfig> existingConfig = repository.findByPartnerIdAndCode(createDto.getPartnerId(), createDto.getCode());
        if (existingConfig.isPresent()) {
            throw new BusinessLogicException(AppErrorCode.OBJECT_EXISTED, "PartnerPointConfig", PartnerPointConfig.Fields.code, createDto.getCode());
        }

        // Create a new config
        PartnerPointConfig config = mapper.toEntity(createDto);
        
        // If this is set as default, make sure no other default exists
        if (Boolean.TRUE.equals(createDto.getIsDefault())) {
            Optional<PartnerPointConfig> defaultConfig = repository.findByPartnerIdAndIsDefault(createDto.getPartnerId(), true);
            defaultConfig.ifPresent(c -> {
                c.setIsDefault(false);
                save(c);
            });
        }
        
        save(config);
        return config;
    }

    @Profiler
    @Transactional
    @Override
    public PartnerPointConfig update(Long partnerId, Long id, PartnerPointConfigUpdateDto updateDto) {
        Partner partner = partnerService.findById(partnerId);
        PartnerPointConfig config = findById(id);

        if (!config.getPartnerId().equals(partner.getId())) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "PartnerPointConfig", "partnerId", partnerId);
        }

        mapper.partialUpdate(config, updateDto);
        
        // If this is set as default, make sure no other default exists
        if (Boolean.TRUE.equals(updateDto.getIsDefault())) {
            repository.findByPartnerIdAndIsDefault(partnerId, true)
                    .filter(c -> !c.getId().equals(id))
                    .ifPresent(c -> {
                        c.setIsDefault(false);
                        save(c);
                    });
        }
        
        save(config);
        return config;
    }

    @Profiler
    @Transactional
    @Override
    public void delete(Long partnerId, Long id) {
        Partner partner = partnerService.findById(partnerId);
        PartnerPointConfig config = findById(id);

        if (!config.getPartnerId().equals(partner.getId())) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "PartnerPointConfig", "partnerId", partnerId);
        }

        config.setActive(false);
        config.setStatus(PartnerStatus.INACTIVE);
        save(config);
    }

    @Profiler
    @Override
    public PartnerPointConfig findById(Long id) {
        return opt(id).orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, "PartnerPointConfig", "id", id));
    }

    @Profiler
    @Override
    public PartnerPointConfig findByCode(Long partnerId, String code) {
        return optByPartnerIdAndCode(partnerId, code).orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, "PartnerPointConfig", "code", code));
    }

    @Profiler
    @Override
    public Page<PartnerPointConfig> filter(PartnerPointConfigFilterDto filter, Pageable pageable) {
        return repository.findAll(PartnerPointConfigSpecification.getSpecification(filter), pageable);
    }

    @Profiler
    @Override
    public void invalidateCache(Long id) {
        // Get the module from cache if available
        PartnerPointConfig pointConfig = idCache.getIfPresent(id);

        // Invalidate entity cache
        idCache.invalidate(id);

        // If we have the module in cache, invalidate related caches
        if (pointConfig != null) {
            // Invalidate module cache
            codeCache.invalidate(buildCodeCacheKey(pointConfig.getPartnerId(), pointConfig.getCode()));
        }
    }

    private Optional<PartnerPointConfig> opt(long id) {
        PartnerPointConfig cachedPointConfig = idCache.getIfPresent(id);
        if (cachedPointConfig != null) {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "opt.hit")) {
                return Optional.of(cachedPointConfig);
            }
        }
        try (var p = new vn.vinclub.common.model.Profiler(getClass(), "opt.miss")) {
            Optional<PartnerPointConfig> optionalPointConfig = repository.findById(id).filter(PartnerPointConfig::getActive);
            if (optionalPointConfig.isEmpty()) {
                return Optional.empty();
            }
            PartnerPointConfig pointConfig = optionalPointConfig.get();
            putToCache(pointConfig);
            return Optional.of(pointConfig);
        }
    }

    private Optional<PartnerPointConfig> optByPartnerIdAndCode(Long partnerId, String code) {
        String cacheKey = buildCodeCacheKey(partnerId, code);
        Long id = codeCache.getIfPresent(cacheKey);

        if (id != null) {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "optByPartnerIdAndCode.hit")) {
                return opt(id);
            }
        }

        try (var p = new vn.vinclub.common.model.Profiler(getClass(), "optByPartnerIdAndCode.miss")) {
            Optional<PartnerPointConfig> optionalPointConfig = repository.findByPartnerIdAndCode(partnerId, code)
                    .filter(PartnerPointConfig::getActive);
            optionalPointConfig.ifPresent(this::putToCache);
            return optionalPointConfig;
        }
    }

    @Transactional
    protected void save(PartnerPointConfig config) {
        repository.save(config);

        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublish.sendChange(AppConst.RedisMessage.CHANGE_PARTNER_POINT_CONFIG, config.getId());
                }
            });
        } else {
            redisPublish.sendChange(AppConst.RedisMessage.CHANGE_PARTNER_POINT_CONFIG, config.getId());
        }
    }

    private void putToCache(PartnerPointConfig config) {
        idCache.put(config.getId(), config);
        codeCache.put(buildCodeCacheKey(config.getPartnerId(), config.getCode()), config.getId());
    }

    private String buildCodeCacheKey(Long partnerId, String code) {
        return partnerId + ":" + code;
    }
}