package vn.vinclub.partner.service.impl;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.client.InternalCoreClient;
import vn.vinclub.partner.client.request.CoreSpendVinClubPointRequest;
import vn.vinclub.partner.client.request.CoreTopUpVinClubPointRequest;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.constant.MetadataKey;
import vn.vinclub.partner.domain.dto.external.point.SpendVinClubPointRequest;
import vn.vinclub.partner.domain.dto.external.point.TopUpVinClubPointRequest;
import vn.vinclub.partner.domain.dto.external.point.VinClubPointTxnResponse;
import vn.vinclub.partner.domain.dto.module.config.SpendPointConfig;
import vn.vinclub.partner.domain.dto.module.config.TopUpPointConfig;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.ModuleType;
import vn.vinclub.partner.domain.enums.TransactionType;
import vn.vinclub.partner.domain.mapper.PartnerPointTransactionHistoryMapper;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.exception.RequestTimeOutException;
import vn.vinclub.partner.module.token.registry.PartnerTokenServiceRegistry;
import vn.vinclub.partner.redis.VClubLock;
import vn.vinclub.partner.service.*;
import vn.vinclub.partner.util.ServiceResponse;

import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class IntegrationPointServiceImpl implements IntegrationPointService {

    private final PartnerService partnerService;
    private final PartnerModuleService partnerModuleService;
    private final PartnerUserMappingService partnerUserMappingService;
    private final PartnerPointTransactionHistoryService partnerPointTransactionHistoryService;
    private final TransactionLimitService transactionLimitService;
    private final PartnerTokenServiceRegistry partnerTokenServiceRegistry;

    private final InternalCoreClient internalCoreClient;
    private final RedissonClient redissonClient;
    private final BaseJsonUtils jsonUtils;

    @Value("${default.vinclub.point.code}")
    private String defaultVinclubPointCode;

    @Profiler
    @Override
    public VinClubPointTxnResponse lookupTransaction(String partnerCode, String partnerTransactionId, String partnerUserId) {
        var partner = partnerService.findByCode(partnerCode);
        var transaction = partnerPointTransactionHistoryService.findByPartnerIdAndPartnerTransactionId(partner.getId(), partnerTransactionId);

        if (!transaction.getPartnerUserId().equals(partnerUserId)) {
            log.error("partnerUserId={} does not match with transaction's partnerUserId={}", partnerUserId, transaction.getPartnerUserId());
            throw new BusinessLogicException(AppErrorCode.TRANSACTION_OWNER_MISMATCH);
        }
        return PartnerPointTransactionHistoryMapper.INSTANCE.toExternalResponseDto(transaction);
    }

    @Profiler
    @Override
    public VinClubPointTxnResponse spendPoint(SpendVinClubPointRequest request) {
        try (var customerLock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_USER_MAPPING_CUD + request.getCustomerLockKey())) {
            // Validate mapping
            var partner = partnerService.findByCode(request.getPartnerCode());
            var isMapped = partnerUserMappingService.isMapped(partner.getId(), request.getPartnerUserId(), request.getVclubUserId());

            if (!isMapped) {
                log.warn("partner_code={}, partner_user_id={}, vclub_user_id={} is not mapped", request.getPartnerCode(), request.getPartnerUserId(), request.getVclubUserId());
                throw new BusinessLogicException(AppErrorCode.ACCOUNT_NOT_LINKED);
            }

            // Validate request
            validateSpendPointRequest(partner.getId(), request);

            // validate txnToken
            var isValidTxnToken = partnerTokenServiceRegistry.getService(partner.getCode())
                    .verifyTxnToken(request.getTxnToken(), request.getPartnerUserId(), request.getVclubUserId(), request.getPartnerTransactionId(), request.getSpendPoint(), request.getRequestTime());

            if (!isValidTxnToken) {
                log.error("Invalid txnToken: {}", request.getTxnToken());
                throw new BusinessLogicException(AppErrorCode.INVALID_SIGNED_TOKEN);
            }

            // create metadata
            ObjectNode metadata = JsonNodeFactory.instance.objectNode();
            metadata.putPOJO(MetadataKey.PartnerTransaction.ORDER_INFO, request.getOrderInfo());
            metadata.putPOJO(MetadataKey.PartnerTransaction.REQUEST, request);

            // Init transaction
            var transaction = partnerPointTransactionHistoryService.initSpendPointTxnFromPartner(
                    partner.getId(),
                    request.getPartnerUserId(),
                    request.getVclubUserId(),
                    request.getPartnerTransactionId(),
                    request.getSpendPoint(),
                    request.getDescription(),
                    metadata
            );
            try (var txnLock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_POINT_TRANSACTION_CUD + transaction.getTransactionId())) {
                // Call to core service to update balance
                var processResult = internalCoreClient.doSpendVinClubPoint(partner.getId(), CoreSpendVinClubPointRequest.builder()
                        .partnerCode(request.getPartnerCode())
                        .partnerUserId(request.getPartnerUserId())
                        .vclubUserId(request.getVclubUserId())
                        .transactionId(transaction.getTransactionId())
                        .partnerTransactionId(transaction.getPartnerTransactionId())
                        .description(transaction.getDescription())
                        .spendPoint(request.getSpendPoint())
                        .timestamp(request.getRequestTime())
                        .orderInfo(request.getOrderInfo())
                        .build());

                // mark transaction success
                transaction = partnerPointTransactionHistoryService.markTxnFromPartnerSuccess(transaction.getTransactionId(), processResult.getProcessedSessionId());
                var result = PartnerPointTransactionHistoryMapper.INSTANCE.toExternalResponseDto(transaction);
                storeResponse(transaction.getTransactionId(), ServiceResponse.success(result));

                // Return result
                return result;
            } catch (RequestTimeOutException e) {
                log.error("Request timeout when call spendPoint({})\n{}", request, e.getMessage());
                var result = PartnerPointTransactionHistoryMapper.INSTANCE.toExternalResponseDto(transaction);
                storeResponse(transaction.getTransactionId(), ServiceResponse.success(result));
                return result;
            } catch (Exception e) {
                transaction = partnerPointTransactionHistoryService.markTxnFailed(transaction.getTransactionId(), e.getMessage());
                var failedResponse = PartnerPointTransactionHistoryMapper.INSTANCE.toExternalResponseDto(transaction);
                if (e instanceof BusinessLogicException ex) {
                    @SuppressWarnings("unchecked")
                    ServiceResponse<VinClubPointTxnResponse> payload = (ServiceResponse<VinClubPointTxnResponse>) ex.getPayload();
                    payload.setData(failedResponse);
                    storeResponse(transaction.getTransactionId(), payload);
                    throw new BusinessLogicException(payload);
                }
                log.error("Exception occurs when call spendPoint({})\n{}", request, e.getMessage(), e);
                var exception = new BusinessLogicException(failedResponse, AppErrorCode.SPEND_POINT_UNEXPECTED_ERROR, e.getMessage());
                storeResponse(transaction.getTransactionId(), exception.getPayload());
                throw exception;
            }
        }
    }

    @Profiler
    @Override
    public VinClubPointTxnResponse topUpPoint(TopUpVinClubPointRequest request) {
        try (var customerLock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_USER_MAPPING_CUD + request.getCustomerLockKey())) {
            // Validate mapping
            var partner = partnerService.findByCode(request.getPartnerCode());
            var isMapped = partnerUserMappingService.isMapped(partner.getId(), request.getPartnerUserId(), request.getVclubUserId());

            if (!isMapped) {
                log.warn("partner_code={}, partner_user_id={}, vclub_user_id={} is not mapped", request.getPartnerCode(), request.getPartnerUserId(), request.getVclubUserId());
                throw new BusinessLogicException(AppErrorCode.ACCOUNT_NOT_LINKED);
            }

            // Validate request
            validateTopUpPointRequest(partner.getId(), request);

            // validate txnToken
            var isValidTxnToken = partnerTokenServiceRegistry.getService(partner.getCode())
                    .verifyTxnToken(request.getTxnToken(), request.getPartnerUserId(), request.getVclubUserId(), request.getPartnerTransactionId(), request.getTopUpPoint(), request.getRequestTime());

            if (!isValidTxnToken) {
                log.error("Invalid txnToken: {}", request.getTxnToken());
                throw new BusinessLogicException(AppErrorCode.INVALID_SIGNED_TOKEN);
            }

            // create metadata
            ObjectNode metadata = JsonNodeFactory.instance.objectNode();
            if (request.getMetadata() != null) {
                metadata.set(MetadataKey.PartnerTransaction.TOPUP_EXTRA_INFO, request.getMetadata());
            }

            // store request info
            metadata.putPOJO(MetadataKey.PartnerTransaction.REQUEST, request);

            // Init transaction
            var transaction = partnerPointTransactionHistoryService.initTopUpPointTxnFromPartner(
                    partner.getId(),
                    request.getPartnerUserId(),
                    request.getVclubUserId(),
                    request.getPartnerTransactionId(),
                    request.getTopUpPoint(),
                    request.getDescription(),
                    metadata
            );
            try (var txnLock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_POINT_TRANSACTION_CUD + transaction.getTransactionId())) {
                // Call to core service to update balance
                var processResult = internalCoreClient.doTopUpVinClubPoint(partner.getId(), CoreTopUpVinClubPointRequest.builder()
                        .partnerCode(request.getPartnerCode())
                        .partnerUserId(request.getPartnerUserId())
                        .vclubUserId(request.getVclubUserId())
                        .transactionId(transaction.getTransactionId())
                        .partnerTransactionId(transaction.getPartnerTransactionId())
                        .description(transaction.getDescription())
                        .topUpPoint(request.getTopUpPoint())
                        .timestamp(request.getRequestTime())
                        .topUpExtraInfo(request.getMetadata())
                        .build());

                // Update transaction status
                transaction = partnerPointTransactionHistoryService.markTxnFromPartnerSuccess(transaction.getTransactionId(), processResult.getProcessedSessionId());

                var result = PartnerPointTransactionHistoryMapper.INSTANCE.toExternalResponseDto(transaction);
                storeResponse(transaction.getTransactionId(), ServiceResponse.success(result));

                // Return result
                return result;
            } catch (RequestTimeOutException e) {
                log.error("Request timeout when call topUpPoint({})\n{}", request, e.getMessage());
                var result = PartnerPointTransactionHistoryMapper.INSTANCE.toExternalResponseDto(transaction);
                storeResponse(transaction.getTransactionId(), ServiceResponse.success(result));
                return result;
            } catch (Exception e) {
                transaction = partnerPointTransactionHistoryService.markTxnFailed(transaction.getTransactionId(), e.getMessage());
                var failedResponse = PartnerPointTransactionHistoryMapper.INSTANCE.toExternalResponseDto(transaction);
                if (e instanceof BusinessLogicException ex) {
                    @SuppressWarnings("unchecked")
                    ServiceResponse<VinClubPointTxnResponse> payload = (ServiceResponse<VinClubPointTxnResponse>) ex.getPayload();
                    payload.setData(failedResponse);
                    storeResponse(transaction.getTransactionId(), payload);
                    throw new BusinessLogicException(payload);
                }
                log.error("Exception occurs when call topUpPoint({})\n{}", request, e.getMessage(), e);
                var exception = new BusinessLogicException(
                        failedResponse,
                        AppErrorCode.TOPUP_POINT_UNEXPECTED_ERROR, e.getMessage()
                );
                storeResponse(transaction.getTransactionId(), exception.getPayload());
                throw exception;
            }
        }
    }

    @Profiler
    @Override
    public void recheckProcessingTransactionFromPartner(PartnerPointTransactionHistory transaction) {
        var transactionId = transaction.getTransactionId();
        var partnerId = transaction.getPartnerId();
        try (var txnLock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_POINT_TRANSACTION_CUD + transactionId)) {
            // Call to core service to update balance
            try {
                var processResult = internalCoreClient.lookupTransaction(partnerId, transactionId);
                if (processResult.isFound()) {
                    if (processResult.isSuccess()) {
                        partnerPointTransactionHistoryService.markTxnFromPartnerSuccess(transactionId, processResult.getProcessedSessionId());
                    } else {
                        partnerPointTransactionHistoryService.markTxnFailed(transactionId, "Recheck transaction failed");
                    }
                } else {
                    partnerPointTransactionHistoryService.markTxnFailed(transactionId, "Recheck transaction not found");
                }
            } catch (Exception e) {
                // Skip
            }
        }
    }

    @Profiler
    private void validateSpendPointRequest(Long partnerId, SpendVinClubPointRequest request) {
        var spendPointModule = partnerModuleService.optByPartnerAndModule(partnerId, ModuleType.SPEND_POINT);
        if (spendPointModule.isEmpty() || !spendPointModule.get().isEnabled()) {
            throw new BusinessLogicException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION);
        }
        var config = jsonUtils.treeToValue(spendPointModule.get().getConfig(), SpendPointConfig.class);

        if (Objects.isNull(config) | !config.isVclubEnabled()) {
            log.error("Partner does not support spend point");
            throw new BusinessLogicException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION);
        }

        if (request.getSpendPoint() <= 0) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "spendPoint", "spendPoint phải lớn hơn 0");
        }

        // check limit
        transactionLimitService.validateLimits(partnerId, request.getPartnerUserId(), request.getSpendPoint(), defaultVinclubPointCode, TransactionType.SPEND_POINT, ActorSystem.PARTNER);
    }

    @Profiler
    private void validateTopUpPointRequest(Long partnerId, TopUpVinClubPointRequest request) {
        var topUpPointModule = partnerModuleService.optByPartnerAndModule(partnerId, ModuleType.TOPUP_POINT);
        if (topUpPointModule.isEmpty() || !topUpPointModule.get().isEnabled()) {
            throw new BusinessLogicException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION);
        }

        var config = jsonUtils.treeToValue(topUpPointModule.get().getConfig(), TopUpPointConfig.class);

        if (Objects.isNull(config) | !config.isVclubEnabled()) {
            log.error("Partner does not support topup point");
            throw new BusinessLogicException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION);
        }

        if (request.getTopUpPoint() <= 0) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "topUpPoint", "topUpPoint phải lớn hơn 0");
        }

        // check limit
        transactionLimitService.validateLimits(partnerId, request.getPartnerUserId(), request.getTopUpPoint(), defaultVinclubPointCode, TransactionType.TOP_UP_POINT, ActorSystem.PARTNER);
    }

    @Profiler
    private void storeResponse(String transactionId, ServiceResponse<?> success) {
        partnerPointTransactionHistoryService.logResponse(transactionId, jsonUtils.toNode(success));
    }
}
