package vn.vinclub.partner.service.impl;

import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.jwt.*;
import org.springframework.stereotype.Component;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.util.KeyUtil;

import java.security.Key;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.text.ParseException;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
@Component
public class JwtCommonImpl {

    @Value("${jwt.vclub_customer_public_key_rs256:}")
    private String rs256PublicKey;

    @Value("${jwt.issuer:vclub-partner-service}")
    private String issuer;

    @Value("${jwt.expiration_time_in_ms:300000}")
    private Long expirationTimeInMs;

    @Value("${partner.integration.max_token_alive_time_in_ms:300000}")
    private Long maxPartnerTokenAliveTimeInMs;

    private NimbusJwtDecoder jwtDecoder;
    private volatile Key publicKey;

    private final OAuth2TokenValidator<Jwt> validators = JwtValidators.createDefault();

    public JwtDecoder getCustomerJwtDecoder() {
        if (jwtDecoder != null) {
            return jwtDecoder;
        }

        jwtDecoder = NimbusJwtDecoder
                .withPublicKey((RSAPublicKey) getCustomerPublicKey())
                .build();
        return jwtDecoder;
    }

    private Key getCustomerPublicKey() {
        if (publicKey != null) {
            return publicKey;
        }
        this.publicKey = KeyUtil.decodePublicKey(rs256PublicKey);
        return publicKey;
    }

    public Jwt decodeCustomerJwt(String token) {
        try {
            return getCustomerJwtDecoder().decode(token);
        } catch (Exception e) {
            log.error("Failed to decode customer jwt: {}", e.getMessage());
            return null;
        }
    }

    private JwtDecoder getCmsJwtDecoder() {
        // Without any verification
        return token -> {
            try {

                // Parse the token
                SignedJWT signedJWT = SignedJWT.parse(token);

                // Extract claims without validation
                var claims = signedJWT.getJWTClaimsSet();
                Jwt jwt = new Jwt(
                        token,
                        claims.getIssueTime() != null ? claims.getIssueTime().toInstant() : Instant.now(),
                        claims.getExpirationTime() != null ? claims.getExpirationTime().toInstant() : Instant.MAX,
                        signedJWT.getHeader().toJSONObject(),
                        claims.getClaims()
                );
                validators.validate(jwt);
                return jwt;
            } catch (ParseException e) {
                throw new IllegalArgumentException("Invalid JWT token", e);
            }
        };
    }

    public Jwt decodeCmsJwt(String token) {
        return getCmsJwtDecoder().decode(token);
    }

    public String signPartnerData(Map<String, Object> claims, PrivateKey privateKey, String partnerCode) {
        try {
            // Build the JWT claims set
            JWTClaimsSet.Builder claimsSetBuilder = new JWTClaimsSet.Builder();
            claims.forEach(claimsSetBuilder::claim);
            claimsSetBuilder.issuer(issuer);
            claimsSetBuilder.issueTime(new Date());
            claimsSetBuilder.expirationTime(new Date(System.currentTimeMillis() + expirationTimeInMs));
            JWTClaimsSet claimsSet = claimsSetBuilder.build();

            // Create a signer using the private key
            JWSSigner signer = new RSASSASigner(privateKey);

            // Create a signed JWT
            SignedJWT signedJWT = new SignedJWT(
                    new JWSHeader.Builder(JWSAlgorithm.RS256)
                            .type(JOSEObjectType.JWT)
                            .keyID(partnerCode.toLowerCase())
                            .build(),
                    claimsSet
            );

            // Sign the JWT
            signedJWT.sign(signer);

            // Serialize the JWT into a string
            return signedJWT.serialize();

        } catch (Exception e) {
            throw new RuntimeException("Failed to generate JWT token", e);
        }
    }

    public JwtDecoder getPartnerDecoder(String partnerPublicKeyStr) {
        PublicKey partnerPublicKey = KeyUtil.decodePublicKey(partnerPublicKeyStr);
        return NimbusJwtDecoder
                .withPublicKey((RSAPublicKey) partnerPublicKey)
                .build();
    }

    public Jwt decodePartnerJwt(String partnerPublicKeyStr, String token) {
        try {
            Jwt jwt = getPartnerDecoder(partnerPublicKeyStr).decode(token);

            // special case for partner jwt
            if (Objects.isNull(jwt.getIssuedAt())) {
                throw new BadJwtException("IssuedAt is required");
            }
            if (jwt.getIssuedAt().isAfter(Instant.now())) {
                throw new BadJwtException("IssuedAt is in the future");
            }
            if (jwt.getIssuedAt().plusMillis(maxPartnerTokenAliveTimeInMs).isBefore(Instant.now())) {
                throw new BadJwtException("Token expired. Max allowed alive time is " + maxPartnerTokenAliveTimeInMs + "ms");
            }

            return jwt;
        } catch (Exception e) {
            log.error("[decodePartnerJwt] decode failed with error {}", e.getMessage());
            throw new BusinessLogicException(AppErrorCode.INVALID_SIGNED_TOKEN, e.getMessage());
        }
    }
}
