package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;
import org.redisson.codec.SerializationCodec;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.mapping.PartnerUserMappingCreateDto;
import vn.vinclub.partner.domain.dto.mapping.PartnerUserMappingUpdateDto;
import vn.vinclub.partner.domain.entity.PartnerUserMapping;
import vn.vinclub.partner.domain.mapper.PartnerUserMappingMapper;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.repository.PartnerUserMappingRepository;
import vn.vinclub.partner.service.LogHistoricalService;
import vn.vinclub.partner.service.PartnerUserMappingService;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class PartnerUserMappingServiceImpl implements PartnerUserMappingService {
    private final LogHistoricalService logHistoricalService;
    private final PartnerUserMappingRepository repository;
    private final PartnerUserMappingMapper mapper;
    private final RedissonClient redissonClient;

    private static final String CACHE_BY_ID_KEY = "partner_svc_user_mapping:by_id";
    private static final String PARTNER_USER_ID_CACHE_KEY = "partner_svc_user_mapping:by_partner_user_id";
    private static final String VCLUB_USER_ID_CACHE_KEY = "partner_svc_user_mapping:by_vclub_user_id";
    private static final int CACHE_TTL_MINUTES = 60;

    @Override
    @Transactional
    @Profiler
    public PartnerUserMapping create(PartnerUserMappingCreateDto createDto) {
        if (Objects.isNull(createDto.getPartnerId())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "partnerId");
        }
        if (!StringUtils.hasText(createDto.getPartnerUserId())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "partnerUserId");
        }
        if (Objects.isNull(createDto.getVclubUserId())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "vclubUserId");
        }

        // Check if mapping already exists
        if (repository.existsByPartnerUserIdOrVClubUserId(
                createDto.getPartnerId(), createDto.getPartnerUserId(), createDto.getVclubUserId())) {
            throw new BusinessLogicException(AppErrorCode.OBJECT_EXISTED, "Liên kết tài khoản", "partnerId", createDto.getPartnerId());
        }

        PartnerUserMapping mapping = mapper.toEntity(createDto);
        if (Objects.isNull(createDto.getMappingAt())) {
            mapping.setMappingAt(System.currentTimeMillis());
        }
        mapping.setActive(true);
        save(null, mapping);

        return mapping;
    }

    @Override
    @Transactional
    @Profiler
    public PartnerUserMapping update(Long id, PartnerUserMappingUpdateDto updateDto) {
        PartnerUserMapping mapping = findById(id);
        PartnerUserMapping oldMapping = SerializationUtils.clone(mapping);

        mapper.partialUpdate(mapping, updateDto);
        save(oldMapping, mapping);

        return mapping;
    }

    @Override
    @Transactional
    @Profiler
    public void delete(Long id) {
        PartnerUserMapping mapping = findById(id);
        PartnerUserMapping oldMapping = SerializationUtils.clone(mapping);
        mapping.setActive(false);
        mapping.setDeleteAt(System.currentTimeMillis());
        save(oldMapping, mapping);
    }

    @Override
    @Profiler
    public PartnerUserMapping findById(Long id) {
        if (Objects.isNull(id)) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "id");
        }

        Optional<PartnerUserMapping> mapping = opt(id);
        if (mapping.isEmpty() || !mapping.get().getActive()) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "Liên kết tài khoản", "id", id);
        }
        return mapping.get();
    }

    @Override
    @Profiler
    public PartnerUserMapping findByPartnerIdAndPartnerUserId(Long partnerId, String partnerUserId) {
        if (Objects.isNull(partnerId)) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "partnerId");
        }
        if (!StringUtils.hasText(partnerUserId)) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "partnerUserId");
        }

        Optional<PartnerUserMapping> mapping = opt(partnerId, partnerUserId);
        if (mapping.isEmpty()) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "Liên kết tài khoản", "partnerUserId", partnerUserId);
        }
        return mapping.get();
    }

    @Override
    @Profiler
    public Optional<PartnerUserMapping> optByPartnerIdAndPartnerUserId(Long partnerId, String partnerUserId) {
        return opt(partnerId, partnerUserId);
    }

    @Override
    @Profiler
    public PartnerUserMapping findByPartnerIdAndVClubUserId(Long partnerId, Long vclubUserId) {
        if (Objects.isNull(partnerId)) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "partnerId");
        }
        if (Objects.isNull(vclubUserId)) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "vclubUserId");
        }

        Optional<PartnerUserMapping> mapping = opt(partnerId, vclubUserId);
        if (mapping.isEmpty()) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "Liên kết tài khoản", "vclubUserId", vclubUserId);
        }
        return mapping.get();
    }

    @Override
    @Profiler
    public Optional<PartnerUserMapping> optByPartnerIdAndVClubUserId(Long partnerId, Long vclubUserId) {
        return opt(partnerId, vclubUserId);
    }

    @Override
    @Profiler
    public boolean isMapped(Long partnerId, String partnerUserId, Long vclubUserId) {
        var mapping = opt(partnerId, partnerUserId);
        return mapping.map(partnerUserMapping -> partnerUserMapping.getVclubUserId().equals(vclubUserId)).orElse(false);
    }

    @Override
    @Profiler
    public boolean isHasMapping(Long partnerId, String partnerUserId) {
        return opt(partnerId, partnerUserId).isPresent();
    }

    @Override
    @Profiler
    public boolean isHasMapping(Long partnerId, Long vclubUserId) {
        return opt(partnerId, vclubUserId).isPresent();
    }

    @Profiler
    @Override
    public List<PartnerUserMapping> findByVclubUserIdAndActiveTrue(Long userId) {
        return repository.findByVclubUserIdAndActiveTrue(userId);
    }

    @Profiler
    @Override
    public void invalidateCache(Long id) {
        if (Objects.isNull(id)) {
            return;
        }
        PartnerUserMapping mapping = findById(id);
        getCacheById().remove(id);
        String partnerUserKey = buildPartnerUserMappingCacheKey(mapping.getPartnerId(), mapping.getPartnerUserId());
        String vclubUserKey = buildPartnerUserMappingCacheKey(mapping.getPartnerId(), mapping.getVclubUserId());
        getPartnerUserIdCache().remove(partnerUserKey);
        getVclubUserIdCache().remove(vclubUserKey);
        log.info("Invalidated cache for mapping with id: {}", id);
    }

    @Profiler
    @Override
    public void invalidateCache(Long partnerId, String partnerUserId) {
        if (Objects.isNull(partnerId) || !StringUtils.hasText(partnerUserId)) {
            return;
        }
        String key = buildPartnerUserMappingCacheKey(partnerId, partnerUserId);
        Long id = getPartnerUserIdCache().get(key);
        if (id != null) {
            invalidateCache(id);
            log.info("Invalidated cache for mapping with partnerId: {} and partnerUserId: {}", partnerId, partnerUserId);
        }
    }

    @Profiler
    @Override
    public void invalidateCache(Long partnerId, Long vclubUserId) {
        if (Objects.isNull(partnerId) || Objects.isNull(vclubUserId)) {
            return;
        }
        String key = buildPartnerUserMappingCacheKey(partnerId, vclubUserId);
        Long id = getVclubUserIdCache().get(key);
        if (id != null) {
            invalidateCache(id);
            log.info("Invalidated cache for mapping with partnerId: {} and vclubUserId: {}", partnerId, vclubUserId);
        }
    }

    private Optional<PartnerUserMapping> opt(long id) {
        PartnerUserMapping cachedMapping = getCacheById().get(id);
        if (cachedMapping != null) {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "opt.hit")){
                return Optional.of(cachedMapping);
            }
        }
        try (var p = new vn.vinclub.common.model.Profiler(getClass(), "opt.miss")){
            Optional<PartnerUserMapping> optionalMapping = repository.findById(id).filter(PartnerUserMapping::getActive);
            if (optionalMapping.isEmpty()) {
                return Optional.empty();
            }
            PartnerUserMapping mapping = optionalMapping.get();
            getCacheById().put(id, mapping, CACHE_TTL_MINUTES, TimeUnit.MINUTES);
            String partnerUserKey = buildPartnerUserMappingCacheKey(mapping.getPartnerId(), mapping.getPartnerUserId());
            String vclubUserKey = buildPartnerUserMappingCacheKey(mapping.getPartnerId(), mapping.getVclubUserId());
            getPartnerUserIdCache().put(partnerUserKey, mapping.getId(), CACHE_TTL_MINUTES, TimeUnit.MINUTES);
            getVclubUserIdCache().put(vclubUserKey, mapping.getId(), CACHE_TTL_MINUTES, TimeUnit.MINUTES);
            return Optional.of(mapping);
        }
    }

    private Optional<PartnerUserMapping> opt(Long partnerId, String partnerUserId) {
        if (Objects.isNull(partnerId) || !StringUtils.hasText(partnerUserId)) {
            return Optional.empty();
        }

        String key = buildPartnerUserMappingCacheKey(partnerId, partnerUserId);
        Long id = getPartnerUserIdCache().get(key);
        if (id != null) {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "byPartnerUserId.hit")){
                return opt(id);
            }
        }
        try (var p = new vn.vinclub.common.model.Profiler(getClass(), "byPartnerUserId.miss")){
            Optional<PartnerUserMapping> optionalMapping = repository.findByPartnerIdAndPartnerUserIdAndActive(partnerId, partnerUserId, true);
            if (optionalMapping.isEmpty()) {
                return Optional.empty();
            }
            PartnerUserMapping mapping = optionalMapping.get();
            getCacheById().put(mapping.getId(), mapping, CACHE_TTL_MINUTES, TimeUnit.MINUTES);
            getPartnerUserIdCache().put(key, mapping.getId(), CACHE_TTL_MINUTES, TimeUnit.MINUTES);
            String vclubUserKey = buildPartnerUserMappingCacheKey(mapping.getPartnerId(), mapping.getVclubUserId());
            getVclubUserIdCache().put(vclubUserKey, mapping.getId(), CACHE_TTL_MINUTES, TimeUnit.MINUTES);
            return Optional.of(mapping);
        }
    }

    private Optional<PartnerUserMapping> opt(Long partnerId, Long vclubUserId) {
        if (Objects.isNull(partnerId) || vclubUserId == null) {
            return Optional.empty();
        }

        String key = buildPartnerUserMappingCacheKey(partnerId, vclubUserId);
        Long id = getVclubUserIdCache().get(key);
        if (id != null) {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "byVClubUserId.hit")){
                return opt(id);
            }
        }
        try (var p = new vn.vinclub.common.model.Profiler(getClass(), "byVClubUserId.miss")){
            Optional<PartnerUserMapping> optionalMapping = repository.findByPartnerIdAndVclubUserIdAndActive(partnerId, vclubUserId, true);
            if (optionalMapping.isEmpty()) {
                return Optional.empty();
            }
            PartnerUserMapping mapping = optionalMapping.get();
            getCacheById().put(mapping.getId(), mapping, CACHE_TTL_MINUTES, TimeUnit.MINUTES);
            getVclubUserIdCache().put(key, mapping.getId(), CACHE_TTL_MINUTES, TimeUnit.MINUTES);
            String partnerUserKey = buildPartnerUserMappingCacheKey(mapping.getPartnerId(), mapping.getPartnerUserId());
            getPartnerUserIdCache().put(partnerUserKey, mapping.getId(), CACHE_TTL_MINUTES, TimeUnit.MINUTES);
            return Optional.of(mapping);
        }
    }

    private void save(PartnerUserMapping oldMapping, PartnerUserMapping mapping) {
        repository.save(mapping);

        // Send historical event
        logHistoricalService.logHistoricalEvent(oldMapping, mapping);
    }

    private String buildPartnerUserMappingCacheKey(Long partnerId, String partnerUserId) {
        return String.format("%s_%s", partnerId, partnerUserId);
    }

    private String buildPartnerUserMappingCacheKey(Long partnerId, Long vclubUserId) {
        return String.format("%s_%s", partnerId, vclubUserId);
    }

    private RMapCache<Long, PartnerUserMapping> getCacheById() {
        return redissonClient.getMapCache(CACHE_BY_ID_KEY, new SerializationCodec());
    }

    private RMapCache<String, Long> getPartnerUserIdCache() {
        return redissonClient.getMapCache(PARTNER_USER_ID_CACHE_KEY, new LongCodec());
    }

    private RMapCache<String, Long> getVclubUserIdCache() {
        return redissonClient.getMapCache(VCLUB_USER_ID_CACHE_KEY, new LongCodec());
    }
}
