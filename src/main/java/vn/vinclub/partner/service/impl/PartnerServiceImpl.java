package vn.vinclub.partner.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.constant.MetadataKey;
import vn.vinclub.partner.domain.dto.encryption.EncryptKeyData;
import vn.vinclub.partner.domain.dto.partner.PartnerCreateDto;
import vn.vinclub.partner.domain.dto.partner.PartnerFilterDto;
import vn.vinclub.partner.domain.dto.partner.PartnerUpdateDto;
import vn.vinclub.partner.domain.entity.BaseEntity;
import vn.vinclub.partner.domain.entity.Partner;
import vn.vinclub.partner.domain.enums.PartnerInteractionLinkType;
import vn.vinclub.partner.domain.enums.PartnerStatus;
import vn.vinclub.partner.domain.mapper.PartnerMapper;
import vn.vinclub.partner.domain.specification.PartnerSpecification;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.redis.RedisPublish;
import vn.vinclub.partner.repository.PartnerRepository;
import vn.vinclub.partner.service.PartnerService;
import vn.vinclub.partner.util.KeyUtil;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PartnerServiceImpl implements PartnerService {

    private final PartnerRepository repository;
    private final PartnerMapper mapper;
    private final BaseJsonUtils jsonUtils;

    private final Cache<Long, Partner> cacheById = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .maximumSize(10)
            .build();

    private final Cache<String, Long> codeToIdCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .maximumSize(10)
            .build();
    private final RedisPublish redisPublish;

    @Override
    @Transactional
    @Profiler
    public Partner create(PartnerCreateDto createDto) throws BusinessLogicException {
        if (repository.findByCode(createDto.getCode()).filter(Partner::isActive).isPresent()) {
            throw new BusinessLogicException(AppErrorCode.OBJECT_EXISTED, "Partner", Partner.Fields.code, createDto.getCode());
        }

        Partner partner = mapper.toEntity(createDto);
        validateMetadata(partner);
        partner.setEncryptionKey(KeyUtil.generateEncryptKeyData(createDto.getEncryptKeySetting()));
        partner.setActive(true);
        partner.setStatus(PartnerStatus.ACTIVE);
        save(partner);

        return partner;
    }

    @Override
    @Transactional
    @Profiler
    public Partner update(Long id, PartnerUpdateDto updateDto) throws BusinessLogicException {
        Partner partner = repository.findById(id).filter(Partner::isActive)
                .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, "Partner", BaseEntity.Fields.id, id));

        mapper.partialUpdate(partner, updateDto);
        validateMetadata(partner);
        save(partner);

        return partner;
    }

    @Override
    @Transactional
    @Profiler
    public void delete(Long id) {
        Partner partner = findById(id);
        partner.setActive(false);
        save(partner);
    }

    @Override
    @Profiler
    public Partner findById(Long id) {
        Optional<Partner> partner = opt(id);
        if (partner.isEmpty() || !partner.get().isActive()) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "Partner", BaseEntity.Fields.id, id);
        }
        return partner.get();
    }

    @Override
    @Profiler
    public Partner findByCode(String code) {
        Optional<Partner> partner = opt(code);
        if (partner.isEmpty() || !partner.get().isActive()) {
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, "Partner", Partner.Fields.code, code);
        }
        return partner.get();
    }

    @Override
    @Profiler
    public Page<Partner> filter(PartnerFilterDto filter, Pageable pageable) {
        return repository.findAll(PartnerSpecification.getSpecification(filter), pageable);
    }

    @Profiler
    @Override
    public Optional<Partner> optById(Long id) {
        return opt(id);
    }

    @Profiler
    @Override
    public Optional<Partner> optByCode(String code) {
        return opt(code);
    }

    @Override
    @Profiler
    public EncryptKeyData getEncryptKey(String partnerCode) {
        Partner partner = findByCode(partnerCode);
        if (partner != null) {
            return partner.getEncryptionKey();
        }
        return null;
    }

    @Override
    @Profiler
    public void invalidateCache(Long id, String code) {
        cacheById.invalidate(id);
        codeToIdCache.invalidate(code);
    }

    @Override
    @Profiler
    @Transactional
    public void addTag(Long partnerId, Set<String> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return;
        }

        Partner partner = findById(partnerId);
        Set<String> oldTags = Optional.ofNullable(partner.getTags())
                .stream().flatMap(Arrays::stream)
                .collect(Collectors.toSet());
        Set<String> newTags = new HashSet<>(oldTags);

        // Filter out invalid tags
        Set<String> validTags = tags.stream()
                .filter(StringUtils::hasText)
                .collect(Collectors.toSet());

        newTags.addAll(validTags);

        if (newTags.equals(oldTags)) {
            return;
        }


        partner.setTags(newTags.toArray(new String[0]));
        save(partner);
    }

    private Optional<Partner> opt(long id) {
        Partner cachedPartner = cacheById.getIfPresent(id);
        if (cachedPartner != null) {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "opt.hit")){
                return Optional.of(cachedPartner);
            }
        }
        try (var p = new vn.vinclub.common.model.Profiler(getClass(), "opt.miss")){
            Optional<Partner> optionalPartner = repository.findById(id).filter(Partner::isActive);
            if (optionalPartner.isEmpty()) {
                return Optional.empty();
            }
            Partner partner = optionalPartner.get();
            putToCache(partner);
            return Optional.of(partner);
        }
    }

    private Optional<Partner> opt(String code) {
        Long id = codeToIdCache.getIfPresent(code);
        if (id != null) {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "code_to_id.hit")){
                return Optional.of(findById(id));
            }
        }
        try (var p = new vn.vinclub.common.model.Profiler(getClass(), "code_to_id.miss")){
            Optional<Partner> optionalPartner = repository.findByCode(code).filter(Partner::isActive);
            if (optionalPartner.isEmpty()) {
                return Optional.empty();
            }
            Partner partner = optionalPartner.get();
            putToCache(partner);
            return Optional.of(partner);
        }
    }

    private void validateMetadata(Partner partner) {
        if (partner.getMetadata() != null) {
            if (partner.getMetadata().get(MetadataKey.Partner.INTERACTION_LINKS) != null) {
                Map<String, Object> interactionLinks = jsonUtils.readMapFromJson(partner.getMetadata().get(MetadataKey.Partner.INTERACTION_LINKS));
                interactionLinks.keySet().forEach(PartnerInteractionLinkType::fromValue);
            }
        }
    }

    private void save(Partner partner) {
        repository.save(partner);
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublish.sendChange(AppConst.RedisMessage.CHANGE_PARTNER, partner.getId(), partner.getCode());
                }
            });
        } else {
            redisPublish.sendChange(AppConst.RedisMessage.CHANGE_PARTNER, partner.getId(), partner.getCode());
        }
    }

    private void putToCache(Partner partner) {
        codeToIdCache.put(partner.getCode(), partner.getId());
        cacheById.put(partner.getId(), partner);
    }
}
