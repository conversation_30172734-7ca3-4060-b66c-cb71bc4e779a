package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.domain.dto.partner.PartnerCreateDto;
import vn.vinclub.partner.domain.dto.partner.PartnerDto;
import vn.vinclub.partner.domain.dto.partner.PartnerFilterDto;
import vn.vinclub.partner.domain.dto.partner.PartnerUpdateDto;
import vn.vinclub.partner.domain.entity.Partner;
import vn.vinclub.partner.domain.mapper.PartnerMapper;
import vn.vinclub.partner.service.AdminPartnerService;
import vn.vinclub.partner.service.PartnerService;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdminPartnerServiceImpl implements AdminPartnerService {
    private final PartnerService partnerService;
    private final PartnerMapper partnerMapper;

    @Profiler
    @Override
    public PartnerDto registerPartner(PartnerCreateDto createDto) {
        Partner partner = partnerService.create(createDto);
        return partnerMapper.toDto(partner);
    }

    @Profiler
    @Override
    public PartnerDto updatePartner(Long id, PartnerUpdateDto updateDto) {
        Partner partner = partnerService.update(id, updateDto);
        return partnerMapper.toDto(partner);
    }

    @Profiler
    @Override
    public void deletePartner(Long id) {
        partnerService.delete(id);
    }

    @Profiler
    @Override
    public PartnerDto getPartnerDetails(Long id) {
        Partner partner = partnerService.findById(id);
        return partnerMapper.toDto(partner);
    }

    @Profiler
    @Override
    public Page<PartnerDto> searchPartners(PartnerFilterDto filter, Pageable pageable) {
        Page<Partner> partnerPage = partnerService.filter(filter, pageable);
        return partnerPage.map(partnerMapper::toDto);
    }
}
