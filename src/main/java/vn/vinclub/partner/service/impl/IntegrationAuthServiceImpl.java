package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.client.KeyCloakClient;
import vn.vinclub.partner.client.request.AuthenticationRequest;
import vn.vinclub.partner.client.response.AuthenticationResponse;
import vn.vinclub.partner.service.IntegrationAuthService;

@Service
@RequiredArgsConstructor
public class IntegrationAuthServiceImpl implements IntegrationAuthService {
    private final KeyCloakClient keyCloakClient;

    @Profiler
    @Override
    public AuthenticationResponse authenticate(AuthenticationRequest authenticationRequest) {
        return keyCloakClient.authenticate(authenticationRequest);
    }
}
