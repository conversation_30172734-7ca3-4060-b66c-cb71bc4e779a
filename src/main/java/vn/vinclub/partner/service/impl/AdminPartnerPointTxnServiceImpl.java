package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.client.InternalCoreClient;
import vn.vinclub.partner.client.request.CoreCustomerIdentifyRequest;
import vn.vinclub.partner.client.request.CoreRollbackVinClubPointRequest;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.point.PartnerPointTransactionHistoryDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnRollbackDto;
import vn.vinclub.partner.domain.entity.BaseEntity;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;
import vn.vinclub.partner.domain.mapper.PartnerPointTransactionHistoryMapper;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.redis.VClubLock;
import vn.vinclub.partner.service.AdminPartnerPointTxnService;
import vn.vinclub.partner.service.PartnerPointTransactionHistoryService;
import vn.vinclub.partner.service.PartnerService;
import vn.vinclub.partner.util.I18nUtil;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminPartnerPointTxnServiceImpl implements AdminPartnerPointTxnService {
    private final PartnerPointTransactionHistoryService partnerPointTransactionHistoryService;
    private final RedissonClient redissonClient;
    private final InternalCoreClient internalCoreClient;
    private final PartnerService partnerService;

    @Override
    @Profiler
    public Page<PartnerPointTransactionHistoryDto> searchPartnerPointTransactions(PartnerPointTxnFilterDto filter, Pageable pageable) {
        // set the default sort
        if (pageable.getSort().isUnsorted()) {
            pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by(BaseEntity.Fields.id).descending());
        }
        if (StringUtils.hasText(filter.getPhone()) || StringUtils.hasText(filter.getEmail())) {
            var customer = internalCoreClient.optCustomerByIdentityRequest(CoreCustomerIdentifyRequest.builder()
                    .phone(filter.getPhone())
                    .email(filter.getEmail())
                    .build());

            if (customer.isEmpty()) {
                return Page.empty();
            }

            filter.setVclubUserId(customer.get().getId());
        }
        var results = partnerPointTransactionHistoryService.filter(filter, pageable);
        var content = results.getContent().stream()
                .map(PartnerPointTransactionHistoryMapper.INSTANCE::toDto)
                .peek(this::enhanceResponse)
                .toList();
        return PageableExecutionUtils.getPage(content, pageable, results::getTotalElements);
    }

    @Override
    @Profiler
    public PartnerPointTransactionHistoryDto getPartnerPointTransactionHistory(Long id) {
        var transaction = partnerPointTransactionHistoryService.findById(id);
        return PartnerPointTransactionHistoryMapper.INSTANCE.toDto(transaction);
    }

    @Override
    @Profiler
    public PartnerPointTransactionHistoryDto rollbackPartnerPointTransaction(Long id, PartnerPointTxnRollbackDto rollbackDto) {
        var transaction = partnerPointTransactionHistoryService.findById(id);

        if (!transaction.getTransactionId().equals(rollbackDto.getTransactionId())) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "transactionId", "transactionId không khớp với transactionId trong giao dịch");
        }

        // call core service to rollback transaction
        try (var txnLock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_POINT_TRANSACTION_CUD + transaction.getTransactionId())) {

            if (PointHistoryStatus.ROLLBACK.equals(transaction.getStatus())) {
                throw new BusinessLogicException(AppErrorCode.POINT_TXN_ALREADY_ROLLED_BACK);
            }

            if (!PointHistoryStatus.SUCCESS.equals(transaction.getStatus())) {
                throw new BusinessLogicException(AppErrorCode.INVALID_TRANSACTION_STATUS);
            }

            // TODO: implement all rollback transaction types
            if (!TransactionType.TOP_UP_POINT.equals(transaction.getTransactionType())) {
                throw new BusinessLogicException(AppErrorCode.UNSUPPORTED);
            }

            // TODO: implement rollback transaction from Vinclub
            if (ActorSystem.VINCLUB.equals(transaction.getActorSystem())) {
                throw new BusinessLogicException(AppErrorCode.UNSUPPORTED);
            }

            // Call to core service to update balance
            var processResult = internalCoreClient.doRollbackVinClubPointTxn(transaction.getPartnerId(), CoreRollbackVinClubPointRequest.builder()
                    .transactionId(transaction.getTransactionId())
                    .reason(rollbackDto.getReason())
                    .vclubUserId(transaction.getVclubUserId())
                    .build());

            // Update transaction status
            transaction = partnerPointTransactionHistoryService.rollbackTxnFromPartner(transaction.getTransactionId(), processResult.getProcessedSessionId(), rollbackDto.getReason());

            // Return response
            return PartnerPointTransactionHistoryMapper.INSTANCE.toDto(transaction);
        } catch (Exception e) {
            log.error("Error while rollback transaction: {}", e.getMessage());
            throw e;
        }
    }

    private void enhanceResponse(PartnerPointTransactionHistoryDto partnerPointTransactionHistoryDto) {
        var partner = partnerService.findById(partnerPointTransactionHistoryDto.getPartnerId());
        partnerPointTransactionHistoryDto.setPartnerName(partner.getDisplayNames().getOrDefault(I18nUtil.getLanguage(), partner.getDisplayNames().get(AppConst.DEFAULT_LANGUAGE)));
    }
}
