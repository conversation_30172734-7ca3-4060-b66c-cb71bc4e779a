package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.client.InternalCoreClient;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.module.config.LinkAccountConfig;
import vn.vinclub.partner.domain.dto.module.config.TermAndCondition;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigFilterDto;
import vn.vinclub.partner.domain.dto.request.LinkAccountRequest;
import vn.vinclub.partner.domain.dto.response.LinkAccountResponse;
import vn.vinclub.partner.domain.dto.response.LinkablePartnerResponse;
import vn.vinclub.partner.domain.dto.response.LinkedAccountResponse;
import vn.vinclub.partner.domain.dto.response.PartnerPointTxnHistoryResponse;
import vn.vinclub.partner.domain.entity.Partner;
import vn.vinclub.partner.domain.entity.PartnerUserMapping;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.LinkAccountStep;
import vn.vinclub.partner.domain.enums.ModuleType;
import vn.vinclub.partner.domain.enums.TransactionType;
import vn.vinclub.partner.domain.mapper.PartnerPointTransactionHistoryMapper;
import vn.vinclub.partner.domain.mapper.PartnerUserMappingMapper;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.module.account.registry.PartnerLinkAccountServiceRegistry;
import vn.vinclub.partner.service.*;
import vn.vinclub.partner.util.I18nUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerPartnerServiceImpl implements CustomerPartnerService {

    private final static Map<String, String> PARTNER_TOPUP_DESCRIPTION_TEMPLATE = Map.of(
            "vi", "Đổi điểm %s sang VPoint",
            "en", "Converted %s to VPoint"
    );

    private final PartnerService partnerService;
    private final PartnerModuleService partnerModuleService;
    private final PartnerUserMappingService partnerUserMappingService;
    private final PartnerLinkAccountServiceRegistry partnerLinkAccountServiceRegistry;
    private final BaseJsonUtils jsonUtils;
    private final InternalCoreClient internalCoreClient;
    private final PartnerPointTransactionHistoryService partnerPointTransactionHistoryService;
    private final PartnerPointConfigService partnerPointConfigService;

    @Override
    @Profiler
    public List<LinkablePartnerResponse> getLinkablePartner(Long vclubUserId) {
        // Get all partner module Link account
        var partnerEnabledLinkAccountModule = partnerModuleService.findEnabledByModule(ModuleType.LINK_ACCOUNT);

        if (partnerEnabledLinkAccountModule.isEmpty()) {
            return Collections.emptyList();
        }

        // Get linked accounts for each partner
        List<LinkablePartnerResponse> linkablePartners = new ArrayList<>();

        partnerEnabledLinkAccountModule.forEach(p -> {
            Partner partner = partnerService.findById(p.getPartnerId());
            var mapping = partnerUserMappingService.optByPartnerIdAndVClubUserId(partner.getId(), vclubUserId);

            var isLinked = mapping.isPresent();
            var nextStep = isLinked ? LinkAccountStep.COMPLETE : LinkAccountStep.INITIATE;
            var linkedAt = mapping.map(PartnerUserMapping::getMappingAt).orElse(null);
            var config = jsonUtils.treeToValue(p.getConfig(), LinkAccountConfig.class);

            // check eKYC, phone, email required
            var customer = internalCoreClient.getCustomerById(vclubUserId, false, false);
            if (config.isPhoneRequired() && !StringUtils.hasText(customer.getPhoneNumber())) {
                nextStep = LinkAccountStep.NEED_PHONE;
            } else if (config.isEmailRequired() && !StringUtils.hasText(customer.getEmail())) {
                nextStep = LinkAccountStep.NEED_EMAIL;
            } else if (config.isEkycRequired() && !customer.isIdentityDocumentVerified()) {
                nextStep = LinkAccountStep.NEED_EKYC;
            }

            // check allow unlink
            var allowUnlink = false;
            TermAndCondition unLinkTermsAndConditions = null;
            if (isLinked && config.isAllowUnlink()) {
                allowUnlink = true;
                unLinkTermsAndConditions = config.getUnLinkTermsAndConditions().getOrDefault(I18nUtil.getLanguage(), config.getUnLinkTermsAndConditions().get(AppConst.DEFAULT_LANGUAGE));
            }

            var flowType = partnerLinkAccountServiceRegistry.getService(partner.getCode()).getFlowType();

            linkablePartners.add(LinkablePartnerResponse.builder()
                    .partnerCode(partner.getCode())
                    .partnerName(partner.getDisplayNames().getOrDefault(I18nUtil.getLanguage(), partner.getDisplayNames().get(AppConst.DEFAULT_LANGUAGE)))
                    .description(partner.getDescriptions().getOrDefault(I18nUtil.getLanguage(), partner.getDescriptions().get(AppConst.DEFAULT_LANGUAGE)))
                    .logo(partner.getLogos().getOrDefault(I18nUtil.getLanguage(), partner.getLogos().get(AppConst.DEFAULT_LANGUAGE)).getUrl().toString())
                    .termAndCondition(config.getTermsAndConditions().getOrDefault(I18nUtil.getLanguage(), config.getTermsAndConditions().get(AppConst.DEFAULT_LANGUAGE)))
                    .flowType(flowType)
                    .linked(isLinked)
                    .linkedAt(linkedAt)
                    .nextStep(nextStep)
                    .allowUnlink(allowUnlink)
                    .unLinkTermsAndConditions(unLinkTermsAndConditions)
                    .build());
        });

        return linkablePartners;
    }

    @Profiler
    @Override
    public LinkedAccountResponse getLinkedAccount(String partnerCode, Long vclubUserId) {
        var partner = partnerService.findByCode(partnerCode);
        var mapping = partnerUserMappingService.optByPartnerIdAndVClubUserId(partner.getId(), vclubUserId);

        if (mapping.isEmpty()) {
            log.error("partner_code={}, vclub_user_id={} is not linked", partnerCode, vclubUserId);
            throw new BusinessLogicException(AppErrorCode.ACCOUNT_NOT_LINKED);
        }

        var response = PartnerUserMappingMapper.INSTANCE.toLinkedAccountResponse(mapping.get());
        response.setPartnerCode(partnerCode);

        return response;
    }

    @Profiler
    @Override
    public LinkAccountResponse processLinkAccountModule(LinkAccountRequest request) {
        return partnerLinkAccountServiceRegistry.getService(request.getPartnerCode()).linkAccount(request);
    }

    @Profiler
    @Override
    public void unlinkAccount(String partnerCode, Long vclubUserId) {
        partnerLinkAccountServiceRegistry.getService(partnerCode).unlinkAccount(vclubUserId, System.currentTimeMillis());
    }

    @Profiler
    @Override
    public PartnerPointTxnHistoryResponse getPartnerPointTransactions(Long vclubUserId, String transactionId) {
        var transaction = partnerPointTransactionHistoryService.findByTransactionId(transactionId);

        if (!transaction.getVclubUserId().equals(vclubUserId)) {
            log.error("vclub_user_id={} is not the owner of transaction_id={}", vclubUserId, transactionId);
            throw new BusinessLogicException(AppErrorCode.TRANSACTION_OWNER_MISMATCH);
        }

        var response = PartnerPointTransactionHistoryMapper.INSTANCE.toResponseDto(transaction);

        // enhance response
        var partner = partnerService.findById(transaction.getPartnerId());
        response.setPartnerCode(partner.getCode());

        if (TransactionType.TOP_UP_POINT.equals(transaction.getTransactionType()) && ActorSystem.PARTNER.equals(transaction.getActorSystem())) {
            var defaultPointConfigs = partnerPointConfigService.filter(PartnerPointConfigFilterDto.builder()
                    .partnerId(partner.getId())
                    .isDefault(true)
                    .build(), PageRequest.ofSize(1));

            if (!defaultPointConfigs.isEmpty() && defaultPointConfigs.hasContent()) {
                var defaultPointConfig = defaultPointConfigs.getContent().getFirst();
                String template;
                template = PARTNER_TOPUP_DESCRIPTION_TEMPLATE.getOrDefault(I18nUtil.getLanguage(), PARTNER_TOPUP_DESCRIPTION_TEMPLATE.get(AppConst.DEFAULT_LANGUAGE));
                var pointName = defaultPointConfig.getDisplayNames().getOrDefault(I18nUtil.getLanguage(), defaultPointConfig.getDisplayNames().get(AppConst.DEFAULT_LANGUAGE));
                response.setDescription(String.format(template, pointName));
            }
        }

        return response;
    }
}
