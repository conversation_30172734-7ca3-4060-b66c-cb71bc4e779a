package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.vinclub.partner.domain.event.audit.ExternalActivityLogEvent;
import vn.vinclub.partner.domain.event.audit.IntegrationLogEvent;
import vn.vinclub.partner.service.AuditLogService;
import vn.vinclub.partner.service.EventService;

import java.util.Objects;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuditLogServiceImpl implements AuditLogService {
    private final EventService eventService;

    private static final String UNKNOWN = "UNKNOWN";

    private static final Integer DEFAULT_RETRY_COUNT = 0;

    @Value("${default.tenant.id}")
    private Long defaultTenantId;

    @Value("${default.org.id}")
    private Long defaultOrgId;


    @Override
    public void logExternalActivity(ExternalActivityLogEvent event) {
        if (Objects.isNull(event)) {
            log.warn("[ExternalActivityLogEvent] Event is null!");
            return;
        }

        if (StringUtils.isEmpty(event.getExternalSystem())) {
            event.setExternalSystem(UNKNOWN);
        }

        if (StringUtils.isEmpty(event.getUsername())) {
            event.setUsername(UNKNOWN);
        }

        if (StringUtils.isEmpty(event.getIp())) {
            event.setIp(UNKNOWN);
        }

        if (StringUtils.isEmpty(event.getId())) {
            event.setId(UUID.randomUUID().toString());
        }

        // send event
        eventService.sendEvent(event);
    }

    @Override
    public void logIntegrationLog(IntegrationLogEvent event) {
        if (Objects.isNull(event)) {
            log.warn("[IntegrationLogEvent] Event is null!");
            return;
        }

        if (StringUtils.isEmpty(event.getService())) {
            event.setService(UNKNOWN);
        }

        if (StringUtils.isEmpty(event.getId())) {
            event.setId(UUID.randomUUID().toString());
        }

        if (Objects.isNull(event.getTenantId())) {
            event.setTenantId(defaultTenantId);
        }

        if (Objects.isNull(event.getOrgId())) {
            event.setOrgId(defaultOrgId);
        }

        if (Objects.isNull(event.getRetryCount())) {
            event.setRetryCount(DEFAULT_RETRY_COUNT);
        }

        if (Objects.isNull(event.getCreatedAt())) {
            event.setCreatedAt(System.currentTimeMillis());
        }

        // send event
        eventService.sendEvent(event);
    }
}
