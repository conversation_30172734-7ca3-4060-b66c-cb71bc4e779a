package vn.vinclub.partner.service;

import vn.vinclub.partner.domain.entity.OutboxEvent;
import vn.vinclub.partner.domain.event.BaseEvent;

import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
public interface EventService {

    <T extends BaseEvent> void persistOutboxEvent(T event, String outboxEventId);

    void processPendingOutboxEvent();

    void sendOutboxEvent(String outboxEventId, OutboxEvent outboxEvent) throws ExecutionException, InterruptedException;

    <T extends BaseEvent> void sendEvent(T event);
}
