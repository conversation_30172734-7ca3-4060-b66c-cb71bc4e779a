package vn.vinclub.partner.service;

import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.partner.domain.dto.UserQuotaDto;
import vn.vinclub.partner.domain.dto.partner.PartnerFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.dto.request.TopUpPartnerPointIntRequest;
import vn.vinclub.partner.domain.dto.response.LinkedAccountIntResponse;
import vn.vinclub.partner.domain.dto.response.PartnerIntResponse;
import vn.vinclub.partner.domain.dto.response.PartnerPointTxnHistoryIntResponse;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.entity.PartnerUserMapping;
import vn.vinclub.partner.domain.event.historical.CustomerHistoricalEventData;
import vn.vinclub.partner.domain.event.historical.HistoricalEvent;
import vn.vinclub.partner.domain.event.internal.PartnerPointTxnRequestEvent;

import java.util.List;
import java.util.Map;

public interface InternalPartnerService {

    void handleVClubCustomerHistoricalEvent(HistoricalEvent<CustomerHistoricalEventData> event);

    void handlePartnerUserMappingHistoricalEvent(HistoricalEvent<PartnerUserMapping> event);

    void handlePartnerPointTransactionHistoricalEvent(HistoricalEvent<PartnerPointTransactionHistory> event);

    PartnerPointTxnHistoryIntResponse topUpPoint(@Valid TopUpPartnerPointIntRequest request);

    void handlePartnerPointTxnRequestEvent(PartnerPointTxnRequestEvent event);

    Page<PartnerIntResponse> filterPartners(PartnerFilterDto filter, Pageable pageable);

    PartnerIntResponse getPartnerById(String partnerId);

    Map<Long, PartnerIntResponse> multiGetPartners(List<Long> partnerIds, Boolean includeDisabled);

    Map<String, PartnerIntResponse> multiGetPartnersByCode(List<String> partnerCodes, Boolean includeDisabled);

    void recheckProcessingTransaction();

    LinkedAccountIntResponse getLinkedAccount(Long partnerId, Long vclubUserId);

    Page<PartnerPointTxnHistoryIntResponse> filterPartnerPointTransactionHistory(PartnerPointTxnFilterDto filter, Pageable pageable);

    PartnerPointTxnHistoryIntResponse getPartnerPointTransactionHistory(String transactionId);

    Map<String, UserQuotaDto> getPartnerPointQuota(Long partnerId, Long vclubUserId);
}
