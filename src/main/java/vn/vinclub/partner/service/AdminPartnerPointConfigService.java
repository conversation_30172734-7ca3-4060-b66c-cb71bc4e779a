package vn.vinclub.partner.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigCreateDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigUpdateDto;

public interface AdminPartnerPointConfigService {

    PartnerPointConfigDto createPartnerPointConfig(PartnerPointConfigCreateDto createDto);

    PartnerPointConfigDto updatePartnerPointConfig(Long partnerId, Long id, PartnerPointConfigUpdateDto updateDto);

    void deletePartnerPointConfig(Long partnerId, Long id);

    PartnerPointConfigDto getPartnerPointConfigDetails(Long partnerId, Long id);

    Page<PartnerPointConfigDto> searchPartnerPointConfigs(PartnerPointConfigFilterDto filter, Pageable pageable);

    PartnerPointConfigDto getPartnerPointConfigByCode(Long partnerId, String code);
}
