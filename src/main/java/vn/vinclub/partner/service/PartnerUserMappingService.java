package vn.vinclub.partner.service;

import vn.vinclub.partner.domain.dto.mapping.PartnerUserMappingCreateDto;
import vn.vinclub.partner.domain.dto.mapping.PartnerUserMappingUpdateDto;
import vn.vinclub.partner.domain.entity.PartnerUserMapping;

import java.util.List;
import java.util.Optional;

public interface PartnerUserMappingService {

    PartnerUserMapping create(PartnerUserMappingCreateDto createDto);

    PartnerUserMapping update(Long id, PartnerUserMappingUpdateDto updateDto);

    void delete(Long id);

    PartnerUserMapping findById(Long id);

    PartnerUserMapping findByPartnerIdAndPartnerUserId(Long partnerId, String partnerUserId);

    Optional<PartnerUserMapping> optByPartnerIdAndPartnerUserId(Long partnerId, String partnerUserId);

    PartnerUserMapping findByPartnerIdAndVClubUserId(Long partnerId, Long vclubUserId);

    Optional<PartnerUserMapping> optByPartnerIdAndVClubUserId(Long partnerId, Long vclubUserId);

    boolean isMapped(Long partnerId, String partnerUserId, Long vclubUserId);

    boolean isHasMapping(Long partnerId, String partnerUserId);

    boolean isHasMapping(Long partnerId, Long vclubUserId);

    List<PartnerUserMapping> findByVclubUserIdAndActiveTrue(Long userId);

    /**
     * Invalidate cache for a specific mapping
     * @param id The ID of the mapping to invalidate
     */
    void invalidateCache(Long id);

    /**
     * Invalidate cache for a mapping by partner ID and partner user ID
     * @param partnerId The partner ID
     * @param partnerUserId The partner user ID
     */
    void invalidateCache(Long partnerId, String partnerUserId);

    /**
     * Invalidate cache for a mapping by partner ID and VClub user ID
     * @param partnerId The partner ID
     * @param vclubUserId The VClub user ID
     */
    void invalidateCache(Long partnerId, Long vclubUserId);
}
