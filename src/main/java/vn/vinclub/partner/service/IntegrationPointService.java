package vn.vinclub.partner.service;

import vn.vinclub.partner.domain.dto.external.point.SpendVinClubPointRequest;
import vn.vinclub.partner.domain.dto.external.point.TopUpVinClubPointRequest;
import vn.vinclub.partner.domain.dto.external.point.VinClubPointTxnResponse;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;

public interface IntegrationPointService {

    VinClubPointTxnResponse lookupTransaction(String partnerCode, String partnerTransactionId, String partnerUserId);

    VinClubPointTxnResponse spendPoint(SpendVinClubPointRequest request);

    VinClubPointTxnResponse topUpPoint(TopUpVinClubPointRequest request);

    void recheckProcessingTransactionFromPartner(PartnerPointTransactionHistory transaction);
}
