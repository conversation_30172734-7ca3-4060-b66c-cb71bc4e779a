package vn.vinclub.partner.service;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing partner point transaction history.
 */
public interface PartnerPointTransactionHistoryService {

    /**
     * Initiates an accrual point transaction from partner to VinClub.
     * Sets the initial status to PROCESSING.
     *
     * @param partnerId            Partner ID
     * @param partnerUserId        Partner user ID
     * @param vclubUserId          VinClub user ID
     * @param partnerTransactionId Partner transaction ID
     * @param pointAmount          Point amount
     * @param description          Transaction description
     * @param metadata             Additional metadata for the transaction
     * @return Created transaction history
     */
    PartnerPointTransactionHistory initTopUpPointTxnFromPartner(
            Long partnerId,
            String partnerUserId,
            Long vclubUserId,
            String partnerTransactionId,
            Long pointAmount,
            String description,
            ObjectNode metadata);

    /**
     * Initiates a redeem point transaction from partner to VinClub.
     * Sets the initial status to PROCESSING.
     *
     * @param partnerId            Partner ID
     * @param partnerUserId        Partner user ID
     * @param vclubUserId          VinClub user ID
     * @param partnerTransactionId Partner transaction ID
     * @param pointAmount          Point amount
     * @param description          Transaction description
     * @param metadata             Additional metadata for the transaction
     * @return Created transaction history
     */
    PartnerPointTransactionHistory initSpendPointTxnFromPartner(
            Long partnerId,
            String partnerUserId,
            Long vclubUserId,
            String partnerTransactionId,
            Long pointAmount,
            String description,
            ObjectNode metadata);

    /**
     * Initiates a top-up point transaction from VinClub to partner.
     * Sets the initial status to PROCESSING.
     * Transaction ID is auto-generated.
     *
     * @param partnerId       Partner ID
     * @param partnerUserId   Partner user ID
     * @param vclubUserId     VinClub user ID
     * @param pointAmount     Point amount
     * @param pointCode       Point code
     * @param description     Transaction description
     * @param metadata        Additional metadata for the transaction
     * @param internalRefCode Internal reference code
     * @return Created transaction history
     */
    PartnerPointTransactionHistory initTopUpPointTxnToPartner(
            Long partnerId,
            String partnerUserId,
            Long vclubUserId,
            Long pointAmount,
            String pointCode,
            String description,
            ObjectNode metadata,
            String internalRefCode);

    /**
     * Initiates a spend point transaction from VinClub to partner.
     * Sets the initial status to PROCESSING.
     * Transaction ID is auto-generated.
     *
     * @param partnerId       Partner ID
     * @param partnerUserId   Partner user ID
     * @param vclubUserId     VinClub user ID
     * @param pointAmount     Point amount
     * @param pointCode       Point code
     * @param description     Transaction description
     * @param metadata        Additional metadata for the transaction
     * @param internalRefCode Internal reference code
     * @return Created transaction history
     */
    PartnerPointTransactionHistory initSpendPointTxnToPartner(
            Long partnerId,
            String partnerUserId,
            Long vclubUserId,
            Long pointAmount,
            String pointCode,
            String description,
            ObjectNode metadata,
            String internalRefCode);

    /**
     * Changes the status of a transaction from PROCESSING to SUCCESS.
     * Used for transactions initiated from partner to VinClub.
     *
     * @param transactionId   Transaction ID
     * @param internalRefCode Internal reference code
     * @return Updated transaction history
     */
    PartnerPointTransactionHistory markTxnFromPartnerSuccess(String transactionId, String internalRefCode);

    /**
     * Changes the status of a transaction from PROCESSING to SUCCESS and updates the partnerTransactionId.
     * Used for transactions initiated from VinClub to partner.
     *
     * @param transactionId        Transaction ID
     * @param partnerTransactionId Partner transaction ID
     * @param request              Transaction request
     * @param response             Transaction response
     * @param processedTime
     * @return Updated transaction history
     */
    PartnerPointTransactionHistory makeTxnToPartnerSuccess(String transactionId, String partnerTransactionId, String request, String response, Long processedTime);

    /**
     * Changes the status of a transaction from PROCESSING to FAILED.
     *
     * @param transactionId Transaction ID
     * @return Updated transaction history
     */
    PartnerPointTransactionHistory markTxnFailed(String transactionId, String failedReason);

    /**
     * Changes the status of a transaction from PROCESSING to FAILED and updates the request and response.
     *
     * @param transactionId Transaction ID
     * @param failedReason  Reason for failure
     * @param request       Transaction request
     * @param response      Transaction response
     * @return Updated transaction history
     */
    PartnerPointTransactionHistory markTxnFailed(String transactionId, String failedReason, String request, String response);


    /**
     * Updates the request of a transaction that is in PROCESSING status.
     *
     * @param transactionId Transaction ID
     * @param request       Transaction request
     */
    void logRequest(String transactionId, ObjectNode request);

    /**
     * Updates the response of a transaction that is in PROCESSING status.
     *
     * @param transactionId Transaction ID
     * @param response      Transaction response
     */
    void logResponse(String transactionId, ObjectNode response);

    /**
     * Cancels a transaction by changing its status from PROCESSING to CANCELLED.
     *
     * @param transactionId Transaction ID
     * @return Updated transaction history
     */
    PartnerPointTransactionHistory cancelTxn(String transactionId);

    PartnerPointTransactionHistory rollbackTxnFromPartner(String transactionId, String internalRefCode, String reason);

    PartnerPointTransactionHistory findById(Long id);
    Optional<PartnerPointTransactionHistory> optById(Long id);

    PartnerPointTransactionHistory findByTransactionId(String transactionId);
    Optional<PartnerPointTransactionHistory> optByTransactionId(String transactionId);

    Page<PartnerPointTransactionHistory> filter(PartnerPointTxnFilterDto filter, Pageable pageable);

    PartnerPointTransactionHistory findByPartnerIdAndPartnerTransactionId(Long partnerId, String partnerTransactionId);

    List<PartnerPointTransactionHistory> findProcessingTransactions(Pageable pageable);

    void invalidateCache(String transactionId);
}
