package vn.vinclub.partner.service;

import vn.vinclub.partner.domain.dto.UserQuotaDto;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.TransactionType;

/**
 * Service for managing transaction limits and caching daily counters
 */
public interface TransactionLimitService {

    void validateLimits(Long partnerId, String partnerUserId, Long pointAmount, String pointCode, TransactionType transactionType, ActorSystem actorSystem);

    UserQuotaDto getDailyUserQuota(Long partnerId, String partnerUserId);

    UserQuotaDto getMonthlyUserQuota(Long partnerId, String partnerUserId);

    void updateQuota(PartnerPointTransactionHistory oldTransaction, PartnerPointTransactionHistory newTransaction);
}
