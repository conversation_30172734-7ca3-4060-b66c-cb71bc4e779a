package vn.vinclub.partner.service;

import vn.vinclub.partner.domain.dto.external.customer.UnlinkAccountRequestDto;
import vn.vinclub.partner.domain.dto.external.customer.VClubCustomerBalanceDto;
import vn.vinclub.partner.domain.dto.external.customer.VClubCustomerProfileDto;

public interface IntegrationCustomerService {

    VClubCustomerProfileDto getCustomerProfile(String partnerCode, String partnerUserId);

    VClubCustomerBalanceDto getCustomerPointBalance(String partnerCode, String partnerUserId);

    Boolean unlinkAccount(String partnerCode, UnlinkAccountRequestDto request);

}
