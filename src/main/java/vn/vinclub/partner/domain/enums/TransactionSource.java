package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.util.ServiceResponse;

public enum TransactionSource {
    ORDER,
    VOUCHER,
    SWAP_POINT,
    REWARD,
    OTHER
    ;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.name();
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static TransactionSource fromValue(String value) {
        for (TransactionSource transactionSource : TransactionSource.values()) {
            if (transactionSource.name().equalsIgnoreCase(value)) {
                return transactionSource;
            }
        }
        throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.NOT_FOUND, TransactionSource.class.getSimpleName(), "value", value));
    }
}
