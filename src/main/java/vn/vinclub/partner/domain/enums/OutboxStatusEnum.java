package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum OutboxStatusEnum {
    WAITING("WAITING"),
    SENT("SENT");

    private final String code;

    @JsonValue
    public String getCode() {
        return code;
    }

    @JsonCreator
    public static OutboxStatusEnum getValue(String code) {
        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (OutboxStatusEnum e : OutboxStatusEnum.values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }

}
