package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;

public enum AuthMethod {
    NO_AUTH,
    BASIC_AUTH,
    BASIC_TOKEN,
    API_KEY,
    BEARER_TOKEN,
    OAUTH2;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.name();
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static AuthMethod fromValue(String value) {
        for (AuthMethod authMethod : AuthMethod.values()) {
            if (authMethod.name().equalsIgnoreCase(value)) {
                return authMethod;
            }
        }
        throw new BusinessLogicException(AppErrorCode.NOT_FOUND, AuthMethod.class.getSimpleName(), "value", value);
    }
}
