package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

@Getter
@AllArgsConstructor
public enum FileTypeEnum {

    THUMBNAIL,
    BANNER,
    AVATAR,
    IMAGE,
    FILE,
    PHOTO_CARD_LATER,
    PREVIOUS_PHOTO_CARD,

    CUSTOMER_IDENTITY_CARD,
    CUSTOMER_FACE,
    LOGO
    ;

    @JsonValue
    public String toValue() {
        return this.name();
    }

    @JsonCreator
    public static FileTypeEnum fromValue(String name) {

        if (ObjectUtils.isEmpty(name)) {
            return null;
        }

        for (FileTypeEnum e : FileTypeEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }

        return null;
    }

}
