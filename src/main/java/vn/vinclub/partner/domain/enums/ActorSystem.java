package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;

public enum ActorSystem {
    VINCLUB,
    PARTNER;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.name();
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static ActorSystem fromValue(String value) {
        for (ActorSystem actor : ActorSystem.values()) {
            if (actor.name().equalsIgnoreCase(value)) {
                return actor;
            }
        }
        throw new BusinessLogicException(AppErrorCode.NOT_FOUND, ActorSystem.class.getSimpleName(), "value", value);
    }
}
