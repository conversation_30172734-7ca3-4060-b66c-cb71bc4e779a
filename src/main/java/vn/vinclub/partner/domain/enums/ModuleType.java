package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;

@Getter
@AllArgsConstructor
public enum ModuleType {
    LINK_ACCOUNT("Liên kết tài khoản"),
    TOPUP_POINT("Nạp điểm"),
    SPEND_POINT("Tiêu điểm"),
    SWAP_POINT("Đổi điểm"),
    TIER_SYNC("Đồng bộ hạng thành viên")
    ;

    private final String displayName;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.name();
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static ModuleType fromValue(String value) {
        for (ModuleType module : ModuleType.values()) {
            if (module.name().equalsIgnoreCase(value)) {
                return module;
            }
        }
        throw new BusinessLogicException(AppErrorCode.NOT_FOUND, ModuleType.class.getSimpleName(), "value", value);
    }

}
