package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;

public enum LinkAccountFlowType {
    OAUTH2,
    OTP_BINDING,
    ;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.name();
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static LinkAccountFlowType fromValue(String value) {
        for (LinkAccountFlowType flowType : LinkAccountFlowType.values()) {
            if (flowType.name().equalsIgnoreCase(value)) {
                return flowType;
            }
        }
        throw new BusinessLogicException(AppErrorCode.NOT_FOUND, LinkAccountFlowType.class.getSimpleName(), "value", value);
    }

    public static boolean isValidValue(String value) {
        for (LinkAccountFlowType flowType : LinkAccountFlowType.values()) {
            if (flowType.name().equalsIgnoreCase(value)) {
                return true;
            }
        }
        return false;
    }

}
