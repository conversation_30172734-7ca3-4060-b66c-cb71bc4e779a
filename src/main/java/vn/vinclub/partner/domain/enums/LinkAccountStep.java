package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;

public enum LinkAccountStep {
    NEED_EKYC,      // Y<PERSON>u cầu eKYC trước khi link account
    NEED_PHONE,     // <PERSON><PERSON><PERSON> cầu update phone trước khi link account
    NEED_EMAIL,     // <PERSON><PERSON><PERSON> cầu update email trước khi link account
    INITIATE,       // bước đầu gọi handle để start flow
    OPEN_WEBVIEW,   // OAuth2: open auth URL
    CALLBACK,       // OAuth2: xử lý callback
    COMPLETE,       // Khi đã link xong
    CALL_LINK_API,  // Gọi API link account
    ;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.name();
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static LinkAccountStep fromValue(String value) {
        for (LinkAccountStep step : LinkAccountStep.values()) {
            if (step.name().equalsIgnoreCase(value)) {
                return step;
            }
        }
        throw new BusinessLogicException(AppErrorCode.NOT_FOUND, LinkAccountStep.class.getSimpleName(), "value", value);
    }

}
