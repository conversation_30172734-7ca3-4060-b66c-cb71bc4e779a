package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;

public enum EncryptKeyType {
    RSA,
    AES,
    ;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.name();
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static EncryptKeyType fromValue(String value) {
        for (EncryptKeyType keyType : EncryptKeyType.values()) {
            if (keyType.name().equalsIgnoreCase(value)) {
                return keyType;
            }
        }
        throw new BusinessLogicException(AppErrorCode.NOT_FOUND, EncryptKeyType.class.getSimpleName(), "value", value);
    }
}
