package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.util.ServiceResponse;

@AllArgsConstructor
public enum SwapPointDirection {
    VCLUB_TO_PARTNER(0),
    PARTNER_TO_VCLUB(1);

    private final int direction;

    // Method to serialize the enum to a string
    @JsonValue
    public int toValue() {
        return this.direction;
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static SwapPointDirection fromValue(int direction) {
        for (SwapPointDirection en : SwapPointDirection.values()) {
            if (en.direction == direction) {
                return en;
            }
        }
        throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVALID_TYPE, "direction", direction));
    }
}