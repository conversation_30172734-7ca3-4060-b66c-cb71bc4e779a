package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.github.jknack.handlebars.internal.lang3.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PointHistoryStatus {

    PROCESSING("PROCESSING", "Đang thực hiện"),
    SUCCESS("SUCCESS", "Thành công"),
    FAILED("FAILED", "Thất bại"),
    ROLLBACK("ROLL<PERSON><PERSON>K", "Đã hoàn tác"),
    CANCELLED("CANCELLED", "Đã hủy");

    private final String code;
    private final String description;

    @JsonValue
    public String toValue() {
        return this.code;
    }

    @JsonCreator
    public static PointHistoryStatus fromValue(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (PointHistoryStatus e : PointHistoryStatus.values()) {
            if (e.code.equalsIgnoreCase(code)) {
                return e;
            }
        }
        return null;
    }

    public static boolean isValidCode(String code) {
        return fromValue(code) != null;
    }
}