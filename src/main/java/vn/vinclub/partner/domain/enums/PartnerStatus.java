package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;

@Getter
@AllArgsConstructor
public enum PartnerStatus {
    ACTIVE("ACTIVE"),
    INACTIVE("INACTIVE"),
    PENDING("PENDING"),
    ;

    private final String code;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.code;
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static PartnerStatus fromValue(String value) {
        for (PartnerStatus status : PartnerStatus.values()) {
            if (status.code.equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new BusinessLogicException(AppErrorCode.NOT_FOUND, PartnerStatus.class.getSimpleName(), "value", value);
    }
}