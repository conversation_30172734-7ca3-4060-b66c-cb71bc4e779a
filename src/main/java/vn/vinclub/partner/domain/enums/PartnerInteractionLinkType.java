package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;

public enum PartnerInteractionLinkType {
    OPEN_PARTNER_APP,
    ;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.name();
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static PartnerInteractionLinkType fromValue(String value) {
        for (PartnerInteractionLinkType type : PartnerInteractionLinkType.values()) {
            if (type.name().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new BusinessLogicException(AppErrorCode.NOT_FOUND, PartnerInteractionLinkType.class.getSimpleName(), "value", value);
    }
}
