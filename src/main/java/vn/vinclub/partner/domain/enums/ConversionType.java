package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.util.ServiceResponse;

public enum ConversionType {
    FIXED,
    CASH_EQUIVALENT,
    AMOUNT_BASED,
    TIME_BASED

    ;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.name();
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static ConversionType fromValue(String value) {
        for (ConversionType conversionType : ConversionType.values()) {
            if (conversionType.name().equalsIgnoreCase(value)) {
                return conversionType;
            }
        }
        throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.NOT_FOUND, ConversionType.class.getSimpleName(), "value", value));
    }
}
