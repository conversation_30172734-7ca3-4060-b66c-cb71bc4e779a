package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.github.jknack.handlebars.internal.lang3.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TransactionType {

    ACCUMULATE_POINT("AP", "Point Accumulation"),
    SPEND_POINT("SP", "Point Redemption"),
    TOP_UP_POINT("TP", "Point Top-up"),
    SWAP_IN("SI", "Point Swap: Partner Point to VPoint"),
    SWAP_OUT("SO", "Point Swap: VPoint to Partner Point");

    private final String code;
    private final String description;

    @JsonValue
    public String toValue() {
        return this.code;
    }

    @JsonCreator
    public static TransactionType fromValue(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (TransactionType e : TransactionType.values()) {
            if (e.code.equalsIgnoreCase(code)) {
                return e;
            }
        }
        return null;
    }

    public static boolean isValidCode(String code) {
        return fromValue(code) != null;
    }
}