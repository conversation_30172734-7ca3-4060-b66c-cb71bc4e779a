package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.util.ServiceResponse;

public enum KeyType {
    RSA,
    AES,
    ;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.name();
    }

    // Method to deserialize JSON string back to enum
    @JsonCreator
    public static KeyType fromValue(String value) {
        for (KeyType keyType : KeyType.values()) {
            if (keyType.name().equalsIgnoreCase(value)) {
                return keyType;
            }
        }
        throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.NOT_FOUND, KeyType.class.getSimpleName(), "value", value));
    }
}
