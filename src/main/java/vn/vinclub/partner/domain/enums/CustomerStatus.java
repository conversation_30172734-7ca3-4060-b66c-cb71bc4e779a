package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

@Getter
@AllArgsConstructor
public enum CustomerStatus {

	ACTIVE("ACTIVE"),
	FROZEN("FROZEN"),
	BLOCKED("BLOCKED"),
	;

	private final String code;

	@JsonValue
	public String toValue() {
		return this.code;
	}

	@JsonCreator
	public static CustomerStatus fromValue(String code) {

		if (ObjectUtils.isEmpty(code)) {
			return null;
		}

		for (CustomerStatus e : CustomerStatus.values()) {
			if (e.code.equalsIgnoreCase(code)) {
				return e;
			}
		}

		return null;
	}

}

