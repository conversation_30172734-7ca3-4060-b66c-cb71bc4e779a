package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;

public enum IntegrationTag {
    LINK_ACCOUNT,
    UNLINK_ACCOUNT,
    SWAP_POINT,
    TOPUP_PARTNER_POINT,
    TOPUP_VCLUB_POINT,
    SPEND_VCLUB_POINT,
    SPEND_PARTNER_POINT,
    ;

    // Method to serialize the enum to a string
    @JsonValue
    public String toValue() {
        return this.name();
    }

    // Method to deserialize JSO<PERSON> string back to enum
    @JsonCreator
    public static IntegrationTag fromValue(String value) {
        for (IntegrationTag tag : IntegrationTag.values()) {
            if (tag.name().equalsIgnoreCase(value)) {
                return tag;
            }
        }
        throw new BusinessLogicException(AppErrorCode.NOT_FOUND, IntegrationTag.class.getSimpleName(), "value", value);
    }
}
