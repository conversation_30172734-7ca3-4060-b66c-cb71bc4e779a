package vn.vinclub.partner.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

@Getter
@AllArgsConstructor
public enum GenderEnum {

	MALE("M"),
	FEMALE("F"),
	OTHER("O"),
	UNKNOWN("U")
	;

	private final String code;

	@JsonValue
	public String toValue() {
		return this.code;
	}

	@JsonCreator
	public static GenderEnum fromValue(String code) {

		if (!StringUtils.hasText(code)) {
			return GenderEnum.UNKNOWN;
		}

		for (GenderEnum e : GenderEnum.values()) {
			if (e.code.equalsIgnoreCase(code)) {
				return e;
			}
		}

		return GenderEnum.UNKNOWN;
	}

}
