package vn.vinclub.partner.domain.specification;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigFilterDto;
import vn.vinclub.partner.domain.entity.PartnerPointConfig;
import vn.vinclub.partner.domain.enums.PartnerStatus;

public class PartnerPointConfigSpecification {

    /**
     * Creates a specification for filtering PartnerPointConfig entities based on the provided filter criteria.
     *
     * @param filter The filter criteria
     * @return A specification that combines all the filter criteria
     */
    public static Specification<PartnerPointConfig> getSpecification(PartnerPointConfigFilterDto filter) {
        if (filter == null) {
            return Specification.where(null);
        }

        Specification<PartnerPointConfig> spec = Specification.where(null);

        if (StringUtils.hasText(filter.getCode())) {
            spec = spec.and(byCode(filter.getCode()));
        }

        if (StringUtils.hasText(filter.getName())) {
            spec = spec.and(byName(filter.getName()));
        }

        if (filter.getStatus() != null) {
            spec = spec.and(byStatus(filter.getStatus()));
        }

        if (filter.getActive() != null) {
            spec = spec.and(byActive(filter.getActive()));
        }

        if (filter.getIsDefault() != null) {
            spec = spec.and(byIsDefault(filter.getIsDefault()));
        }

        return spec;
    }

    /**
     * Creates a specification for filtering PartnerPointConfig entities by code.
     *
     * @param code The code to filter by
     * @return A specification for filtering by code
     */
    private static Specification<PartnerPointConfig> byCode(String code) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(criteriaBuilder.lower(root.get("code")), code.toLowerCase());
    }

    /**
     * Creates a specification for filtering PartnerPointConfig entities by name.
     * This searches in the name JSON field.
     *
     * @param name The name to filter by
     * @return A specification for filtering by name
     */
    private static Specification<PartnerPointConfig> byName(String name) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.like(
                criteriaBuilder.function("jsonb_extract_path_text", String.class, 
                    root.get("name"), 
                    criteriaBuilder.literal(AppConst.DEFAULT_LANGUAGE)),
                "%" + name + "%");
    }

    /**
     * Creates a specification for filtering PartnerPointConfig entities by status.
     *
     * @param status The status to filter by
     * @return A specification for filtering by status
     */
    private static Specification<PartnerPointConfig> byStatus(PartnerStatus status) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("status"), status);
    }

    /**
     * Creates a specification for filtering PartnerPointConfig entities by active status.
     *
     * @param active The active status to filter by
     * @return A specification for filtering by active status
     */
    private static Specification<PartnerPointConfig> byActive(Boolean active) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("active"), active);
    }

    /**
     * Creates a specification for filtering PartnerPointConfig entities by isDefault status.
     *
     * @param isDefault The isDefault status to filter by
     * @return A specification for filtering by isDefault status
     */
    private static Specification<PartnerPointConfig> byIsDefault(Boolean isDefault) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("isDefault"), isDefault);
    }
}