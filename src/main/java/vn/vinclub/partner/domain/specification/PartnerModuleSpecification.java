package vn.vinclub.partner.domain.specification;

import org.springframework.data.jpa.domain.Specification;
import vn.vinclub.partner.domain.dto.module.PartnerModuleFilterDto;
import vn.vinclub.partner.domain.entity.PartnerModule;
import vn.vinclub.partner.domain.enums.ModuleType;

/**
 * Specification class for filtering PartnerModule entities.
 */
public class PartnerModuleSpecification {

    /**
     * Creates a specification for filtering PartnerModule entities based on the provided filter criteria.
     *
     * @param filter The filter criteria
     * @return A specification that combines all the filter criteria
     */
    public static Specification<PartnerModule> getSpecification(PartnerModuleFilterDto filter) {
        if (filter == null) {
            return Specification.where(null);
        }

        Specification<PartnerModule> spec = Specification.where(null);

        if (filter.getPartnerId() != null) {
            spec = spec.and(byPartnerId(filter.getPartnerId()));
        }

        if (filter.getModule() != null) {
            spec = spec.and(byModule(filter.getModule()));
        }

        if (filter.getEnabled() != null) {
            spec = spec.and(byEnabled(filter.getEnabled()));
        }

        return spec;
    }

    /**
     * Creates a specification for filtering PartnerModule entities by partnerId.
     *
     * @param partnerId The partnerId to filter by
     * @return A specification for filtering by partnerId
     */
    private static Specification<PartnerModule> byPartnerId(Long partnerId) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("partnerId"), partnerId);
    }

    /**
     * Creates a specification for filtering PartnerModule entities by module.
     *
     * @param module The module to filter by
     * @return A specification for filtering by module
     */
    private static Specification<PartnerModule> byModule(ModuleType module) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("module"), module);
    }

    /**
     * Creates a specification for filtering PartnerModule entities by enabled status.
     *
     * @param enabled The enabled status to filter by
     * @return A specification for filtering by enabled status
     */
    private static Specification<PartnerModule> byEnabled(Boolean enabled) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("enabled"), enabled);
    }
}