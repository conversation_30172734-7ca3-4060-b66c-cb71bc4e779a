package vn.vinclub.partner.domain.specification;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.domain.dto.partner.PartnerFilterDto;
import vn.vinclub.partner.domain.entity.Partner;
import vn.vinclub.partner.domain.enums.PartnerStatus;

import java.util.List;

public class PartnerSpecification {

    /**
     * Creates a specification for filtering Partner entities based on the provided filter criteria.
     *
     * @param filter The filter criteria
     * @return A specification that combines all the filter criteria
     */
    public static Specification<Partner> getSpecification(PartnerFilterDto filter) {
        if (filter == null) {
            return Specification.where(null);
        }

        Specification<Partner> spec = Specification.where(null);

        if (StringUtils.hasText(filter.getCode())) {
            spec = spec.and(byCode(filter.getCode()));
        }

        if (StringUtils.hasText(filter.getName())) {
            spec = spec.and(byName(filter.getName()));
        }

        if (filter.getStatus() != null) {
            spec = spec.and(byStatus(filter.getStatus()));
        }

        if (filter.getActive() != null) {
            spec = spec.and(byActive(filter.getActive()));
        }

        if (filter.getTags() != null && !filter.getTags().isEmpty()) {
            spec = spec.and(byTags(filter.getTags()));
        }

        return spec;
    }

    /**
     * Creates a specification for filtering Partner entities by tags.
     * This will find partners that have any of the specified tags.
     *
     * @param tags The tags to filter by
     * @return A specification for filtering by tags
     */
    private static Specification<Partner> byTags(List<String> tags) {
        return (root, query, criteriaBuilder) -> {
            if (tags == null || tags.isEmpty()) {
                return criteriaBuilder.conjunction();
            }

            // Create a disjunction (OR) of predicates for each tag
            var disjunction = criteriaBuilder.disjunction();
            for (String tag : tags) {
                // Use array_position to check if the tag is in the array
                // array_position returns the position (1-based) of the first occurrence, or null if not found
                disjunction = criteriaBuilder.or(disjunction, 
                    criteriaBuilder.isNotNull(
                        criteriaBuilder.function("array_position", Integer.class, 
                            root.get("tags"), 
                            criteriaBuilder.literal(tag)
                        )
                    )
                );
            }

            return disjunction;
        };
    }

    /**
     * Creates a specification for filtering Partner entities by code.
     *
     * @param code The code to filter by
     * @return A specification for filtering by code
     */
    private static Specification<Partner> byCode(String code) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(criteriaBuilder.lower(root.get("code")), code.toLowerCase());
    }

    /**
     * Creates a specification for filtering Partner entities by name.
     * This searches in the displayNames JSON field.
     *
     * @param name The name to filter by
     * @return A specification for filtering by name
     */
    private static Specification<Partner> byName(String name) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.like(
                criteriaBuilder.function("jsonb_extract_path_text", String.class, 
                    root.get("displayNames"), 
                    criteriaBuilder.literal(AppConst.DEFAULT_LANGUAGE)),
                "%" + name + "%");
    }

    /**
     * Creates a specification for filtering Partner entities by status.
     *
     * @param status The status to filter by
     * @return A specification for filtering by status
     */
    private static Specification<Partner> byStatus(PartnerStatus status) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("status"), status);
    }

    /**
     * Creates a specification for filtering Partner entities by active status.
     *
     * @param active The active status to filter by
     * @return A specification for filtering by active status
     */
    private static Specification<Partner> byActive(Boolean active) {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("active"), active);
    }
}
