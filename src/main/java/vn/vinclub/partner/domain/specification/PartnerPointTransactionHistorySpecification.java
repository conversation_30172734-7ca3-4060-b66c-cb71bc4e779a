package vn.vinclub.partner.domain.specification;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * Specification class for filtering PartnerPointTransactionHistory entities.
 */
public class PartnerPointTransactionHistorySpecification {

    /**
     * Creates a specification for filtering PartnerPointTransactionHistory entities based on the provided filter criteria.
     *
     * @param filter The filter criteria
     * @return A specification that combines all the filter criteria
     */
    public static Specification<PartnerPointTransactionHistory> getSpecification(PartnerPointTxnFilterDto filter) {
        if (filter == null) {
            return Specification.where(null);
        }

        Specification<PartnerPointTransactionHistory> spec = Specification.where(null);

        // Basic field filters
        if (filter.getPartnerId() != null) {
            spec = spec.and(byPartnerId(filter.getPartnerId()));
        }

        if (StringUtils.hasText(filter.getPartnerUserId())) {
            spec = spec.and(byPartnerUserId(filter.getPartnerUserId()));
        }

        if (filter.getVclubUserId() != null) {
            spec = spec.and(byVclubUserId(filter.getVclubUserId()));
        }

        if (filter.getActorSystem() != null) {
            spec = spec.and(byActorSystem(filter.getActorSystem()));
        }

        if (filter.getTransactionType() != null) {
            spec = spec.and(byTransactionType(filter.getTransactionType()));
        }

        if (StringUtils.hasText(filter.getTransactionId())) {
            spec = spec.and(byTransactionId(filter.getTransactionId()));
        }

        if (StringUtils.hasText(filter.getPartnerTransactionId())) {
            spec = spec.and(byPartnerTransactionId(filter.getPartnerTransactionId()));
        }

        if (StringUtils.hasText(filter.getInternalRefCode())) {
            spec = spec.and(byInternalRefCode(filter.getInternalRefCode()));
        }

        if (filter.getStatus() != null) {
            spec = spec.and(byStatus(filter.getStatus()));
        }

        if (filter.getActive() != null) {
            spec = spec.and(byActive(filter.getActive()));
        }

        // Date range filters
        if (filter.getCreatedOnFrom() != null || filter.getCreatedOnTo() != null) {
            spec = spec.and(byCreatedOnRange(filter.getCreatedOnFrom(), filter.getCreatedOnTo()));
        }

        if (filter.getUpdatedOnFrom() != null || filter.getUpdatedOnTo() != null) {
            spec = spec.and(byUpdatedOnRange(filter.getUpdatedOnFrom(), filter.getUpdatedOnTo()));
        }

        if (filter.getRequestTimeFrom() != null || filter.getRequestTimeTo() != null) {
            spec = spec.and(byRequestTimeRange(filter.getRequestTimeFrom(), filter.getRequestTimeTo()));
        }

        if (filter.getProcessedTimeFrom() != null || filter.getProcessedTimeTo() != null) {
            spec = spec.and(byProcessedTimeRange(filter.getProcessedTimeFrom(), filter.getProcessedTimeTo()));
        }

        // Amount range filters
        if (filter.getPointAmountFrom() != null || filter.getPointAmountTo() != null) {
            spec = spec.and(byPointAmountRange(filter.getPointAmountFrom(), filter.getPointAmountTo()));
        }

        // List filters
        if (filter.getStatuses() != null && !filter.getStatuses().isEmpty()) {
            spec = spec.and(byStatuses(filter.getStatuses()));
        }

        if (filter.getActorSystems() != null && !filter.getActorSystems().isEmpty()) {
            spec = spec.and(byActorSystems(filter.getActorSystems()));
        }

        if (filter.getTransactionTypes() != null && !filter.getTransactionTypes().isEmpty()) {
            spec = spec.and(byTransactionTypes(filter.getTransactionTypes()));
        }

        if (filter.getTransactionIds() != null && !filter.getTransactionIds().isEmpty()) {
            spec = spec.and(byTransactionIds(filter.getTransactionIds()));
        }

        if (filter.getPartnerTransactionIds() != null && !filter.getPartnerTransactionIds().isEmpty()) {
            spec = spec.and(byPartnerTransactionIds(filter.getPartnerTransactionIds()));
        }

        if (filter.getInternalRefCodes() != null && !filter.getInternalRefCodes().isEmpty()) {
            spec = spec.and(byInternalRefCodes(filter.getInternalRefCodes()));
        }

        return spec;
    }

    /**
     * Creates a specification for filtering by partner ID.
     *
     * @param partnerId The partner ID to filter by
     * @return A specification for filtering by partner ID
     */
    private static Specification<PartnerPointTransactionHistory> byPartnerId(Long partnerId) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("partnerId"), partnerId);
    }

    /**
     * Creates a specification for filtering by partner user ID.
     *
     * @param partnerUserId The partner user ID to filter by
     * @return A specification for filtering by partner user ID
     */
    private static Specification<PartnerPointTransactionHistory> byPartnerUserId(String partnerUserId) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("partnerUserId"), partnerUserId);
    }

    /**
     * Creates a specification for filtering by VClub user ID.
     *
     * @param vclubUserId The VClub user ID to filter by
     * @return A specification for filtering by VClub user ID
     */
    private static Specification<PartnerPointTransactionHistory> byVclubUserId(Long vclubUserId) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("vclubUserId"), vclubUserId);
    }

    /**
     * Creates a specification for filtering by actor system.
     *
     * @param actorSystem The actor system to filter by
     * @return A specification for filtering by actor system
     */
    private static Specification<PartnerPointTransactionHistory> byActorSystem(ActorSystem actorSystem) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("actorSystem"), actorSystem);
    }

    /**
     * Creates a specification for filtering by transaction type.
     *
     * @param transactionType The transaction type to filter by
     * @return A specification for filtering by transaction type
     */
    private static Specification<PartnerPointTransactionHistory> byTransactionType(TransactionType transactionType) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("transactionType"), transactionType);
    }

    /**
     * Creates a specification for filtering by transaction ID.
     *
     * @param transactionId The transaction ID to filter by
     * @return A specification for filtering by transaction ID
     */
    private static Specification<PartnerPointTransactionHistory> byTransactionId(String transactionId) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("transactionId"), transactionId);
    }

    /**
     * Creates a specification for filtering by partner transaction ID.
     *
     * @param partnerTransactionId The partner transaction ID to filter by
     * @return A specification for filtering by partner transaction ID
     */
    private static Specification<PartnerPointTransactionHistory> byPartnerTransactionId(String partnerTransactionId) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("partnerTransactionId"), partnerTransactionId);
    }

    /**
     * Creates a specification for filtering by internal reference code.
     *
     * @param internalRefCode The internal reference code to filter by
     * @return A specification for filtering by internal reference code
     */
    private static Specification<PartnerPointTransactionHistory> byInternalRefCode(String internalRefCode) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("internalRefCode"), internalRefCode);
    }

    /**
     * Creates a specification for filtering by status.
     *
     * @param status The status to filter by
     * @return A specification for filtering by status
     */
    private static Specification<PartnerPointTransactionHistory> byStatus(PointHistoryStatus status) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("status"), status);
    }

    /**
     * Creates a specification for filtering by active status.
     *
     * @param active The active status to filter by
     * @return A specification for filtering by active status
     */
    private static Specification<PartnerPointTransactionHistory> byActive(Boolean active) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("active"), active);
    }

    /**
     * Creates a specification for filtering by created date range.
     *
     * @param fromTime The start time (epoch milliseconds)
     * @param toTime   The end time (epoch milliseconds)
     * @return A specification for filtering by created date range
     */
    private static Specification<PartnerPointTransactionHistory> byCreatedOnRange(Long fromTime, Long toTime) {
        return (root, query, criteriaBuilder) -> {
            var predicates = criteriaBuilder.conjunction();

            if (fromTime != null) {
                LocalDateTime fromDateTime = LocalDateTime.ofInstant(
                        Instant.ofEpochMilli(fromTime), ZoneId.systemDefault());
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.greaterThanOrEqualTo(root.get("createdOn"), fromDateTime));
            }

            if (toTime != null) {
                LocalDateTime toDateTime = LocalDateTime.ofInstant(
                        Instant.ofEpochMilli(toTime), ZoneId.systemDefault());
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.lessThanOrEqualTo(root.get("createdOn"), toDateTime));
            }

            return predicates;
        };
    }

    /**
     * Creates a specification for filtering by updated date range.
     *
     * @param fromTime The start time (epoch milliseconds)
     * @param toTime   The end time (epoch milliseconds)
     * @return A specification for filtering by updated date range
     */
    private static Specification<PartnerPointTransactionHistory> byUpdatedOnRange(Long fromTime, Long toTime) {
        return (root, query, criteriaBuilder) -> {
            var predicates = criteriaBuilder.conjunction();

            if (fromTime != null) {
                LocalDateTime fromDateTime = LocalDateTime.ofInstant(
                        Instant.ofEpochMilli(fromTime), ZoneId.systemDefault());
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.greaterThanOrEqualTo(root.get("updatedOn"), fromDateTime));
            }

            if (toTime != null) {
                LocalDateTime toDateTime = LocalDateTime.ofInstant(
                        Instant.ofEpochMilli(toTime), ZoneId.systemDefault());
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.lessThanOrEqualTo(root.get("updatedOn"), toDateTime));
            }

            return predicates;
        };
    }

    /**
     * Creates a specification for filtering by request time range.
     *
     * @param fromTime The start time (epoch milliseconds)
     * @param toTime   The end time (epoch milliseconds)
     * @return A specification for filtering by request time range
     */
    private static Specification<PartnerPointTransactionHistory> byRequestTimeRange(Long fromTime, Long toTime) {
        return (root, query, criteriaBuilder) -> {
            var predicates = criteriaBuilder.conjunction();

            if (fromTime != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.greaterThanOrEqualTo(root.get("requestTime"), fromTime));
            }

            if (toTime != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.lessThanOrEqualTo(root.get("requestTime"), toTime));
            }

            return predicates;
        };
    }

    /**
     * Creates a specification for filtering by processed time range.
     *
     * @param fromTime The start time (epoch milliseconds)
     * @param toTime   The end time (epoch milliseconds)
     * @return A specification for filtering by processed time range
     */
    private static Specification<PartnerPointTransactionHistory> byProcessedTimeRange(Long fromTime, Long toTime) {
        return (root, query, criteriaBuilder) -> {
            var predicates = criteriaBuilder.conjunction();

            if (fromTime != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.greaterThanOrEqualTo(root.get("processedTime"), fromTime));
            }

            if (toTime != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.lessThanOrEqualTo(root.get("processedTime"), toTime));
            }

            return predicates;
        };
    }

    /**
     * Creates a specification for filtering by point amount range.
     *
     * @param fromAmount The minimum point amount
     * @param toAmount   The maximum point amount
     * @return A specification for filtering by point amount range
     */
    private static Specification<PartnerPointTransactionHistory> byPointAmountRange(Long fromAmount, Long toAmount) {
        return (root, query, criteriaBuilder) -> {
            var predicates = criteriaBuilder.conjunction();

            if (fromAmount != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.greaterThanOrEqualTo(root.get("pointAmount"), fromAmount));
            }

            if (toAmount != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.lessThanOrEqualTo(root.get("pointAmount"), toAmount));
            }

            return predicates;
        };
    }

    /**
     * Creates a specification for filtering by multiple statuses.
     *
     * @param statuses The list of statuses to filter by
     * @return A specification for filtering by statuses
     */
    private static Specification<PartnerPointTransactionHistory> byStatuses(List<PointHistoryStatus> statuses) {
        return (root, query, criteriaBuilder) -> {
            if (statuses == null || statuses.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return root.get("status").in(statuses);
        };
    }

    /**
     * Creates a specification for filtering by multiple actor systems.
     *
     * @param actorSystems The list of actor systems to filter by
     * @return A specification for filtering by actor systems
     */
    private static Specification<PartnerPointTransactionHistory> byActorSystems(List<ActorSystem> actorSystems) {
        return (root, query, criteriaBuilder) -> {
            if (actorSystems == null || actorSystems.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return root.get("actorSystem").in(actorSystems);
        };
    }

    /**
     * Creates a specification for filtering by multiple transaction types.
     *
     * @param transactionTypes The list of transaction types to filter by
     * @return A specification for filtering by transaction types
     */
    private static Specification<PartnerPointTransactionHistory> byTransactionTypes(List<TransactionType> transactionTypes) {
        return (root, query, criteriaBuilder) -> {
            if (transactionTypes == null || transactionTypes.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return root.get("transactionType").in(transactionTypes);
        };
    }

    /**
     * Creates a specification for filtering by multiple transaction IDs.
     *
     * @param transactionIds The list of transaction IDs to filter by
     * @return A specification for filtering by transaction IDs
     */
    private static Specification<PartnerPointTransactionHistory> byTransactionIds(List<String> transactionIds) {
        return (root, query, criteriaBuilder) -> {
            if (transactionIds == null || transactionIds.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return root.get("transactionId").in(transactionIds);
        };
    }

    /**
     * Creates a specification for filtering by multiple partner transaction IDs.
     *
     * @param partnerTransactionIds The list of partner transaction IDs to filter by
     * @return A specification for filtering by partner transaction IDs
     */
    private static Specification<PartnerPointTransactionHistory> byPartnerTransactionIds(List<String> partnerTransactionIds) {
        return (root, query, criteriaBuilder) -> {
            if (partnerTransactionIds == null || partnerTransactionIds.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return root.get("partnerTransactionId").in(partnerTransactionIds);
        };
    }

    /**
     * Creates a specification for filtering by multiple internal reference codes.
     *
     * @param internalRefCodes The list of internal reference codes to filter by
     * @return A specification for filtering by internal reference codes
     */
    private static Specification<PartnerPointTransactionHistory> byInternalRefCodes(List<String> internalRefCodes) {
        return (root, query, criteriaBuilder) -> {
            if (internalRefCodes == null || internalRefCodes.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return root.get("internalRefCode").in(internalRefCodes);
        };
    }
}
