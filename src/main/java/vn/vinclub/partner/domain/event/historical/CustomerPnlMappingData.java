package vn.vinclub.partner.domain.event.historical;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> 7/12/24 17:58
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerPnlMappingData implements Serializable {
  private Long id;
  private String pnl;
  private String phone;
  private String email;
  private String pnlProfileId;
  private String pnlUserId;
  private String identityNumberDefault;
  private Integer consentStatus;
  private Long consentAt;
  private Long mappingAt;
  private String initTierCode;
  private Long totalSpendAmount;
  private Long migrateCalcAtTime;
  private Long numberOfTransaction;
}
