package vn.vinclub.partner.domain.event.audit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.domain.event.BaseEvent;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegrationLogEvent implements BaseEvent {
    public static final String EVENT_CODE = "INTEGRATION_LOG";

    private String id;
    private Long tenantId;
    private Long orgId;
    private String service; // SKYJOY, KEYCLOAK, ...
    private String action; // GET_PROFILE, TOPUP_POINT, SPEND_POINT, ...
    private String url;
    private String protocol;
    private String method;

    private Long requestTime;
    private String requestHeader;
    private String requestData;

    private Long responseTime;
    private String responseCode;
    private String responseHeader;
    private String responseData;

    private String status; // SUCCESS, ERROR
    private String message;

    private Integer retryCount;
    private Long createdAt;

    @Builder.Default
    private String createdBy = AppConst.SOURCE_SYSTEM;

    @Override
    public String getEventCode() {
        return EVENT_CODE;
    }
}
