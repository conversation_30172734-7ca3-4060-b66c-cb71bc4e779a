package vn.vinclub.partner.domain.event.internal;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;
import vn.vinclub.partner.domain.event.BaseEvent;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class PartnerPointTxnResponseEvent implements BaseEvent {
    public static final String EVENT_CODE = "PARTNER_POINT_TXN_RESPONSE";

    private Long partnerId;
    private String partnerUserId;
    private Long vclubUserId;
    private String transactionId;
    private ActorSystem actorSystem;
    private TransactionType transactionType;
    private String internalRefCode;
    private Long pointAmount;
    private PointHistoryStatus status;

    @Override
    public String getIdempotentKey() {
        return transactionId;
    }

    @Override
    public String getKafkaMessageKey() {
        return transactionId;
    }

    @Override
    public String getEventCode() {
        return EVENT_CODE;
    }
}
