package vn.vinclub.partner.domain.event;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;

public interface BaseEvent {
    default Long getTimestamp() {
        return System.currentTimeMillis();
    }

    @JsonIgnore
    default JsonNode getOutBoxMetadata() {
        return null;
    }

    @JsonIgnore
    default String getKafkaMessageKey() {
        return "";
    }

    @JsonIgnore
    default String getIdempotentKey() {
        return getTimestamp().toString();
    }

    @JsonIgnore
    String getEventCode();
}
