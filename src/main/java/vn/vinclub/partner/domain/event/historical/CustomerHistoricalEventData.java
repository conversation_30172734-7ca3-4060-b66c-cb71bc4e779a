package vn.vinclub.partner.domain.event.historical;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Optional;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerHistoricalEventData implements Serializable {

    private Optional<Long> id = Optional.empty();
    private Optional<String> userId = Optional.empty();
    private Optional<String> originalId = Optional.empty();

    private Optional<Boolean> active = Optional.empty();

    private Optional<Long> createdTime = Optional.empty();
    private Optional<Long> updatedTime = Optional.empty();
    private Optional<Long> deletedAt = Optional.empty();

    private Optional<String> phone = Optional.empty();
    private Optional<Integer> phoneVerified = Optional.empty();
    private Optional<String> email = Optional.empty();
    private Optional<Integer> emailVerified = Optional.empty();

    private Optional<String> oauthGoogleId = Optional.empty();
    private Optional<String> oauthFacebookId = Optional.empty();
    private Optional<String> oauthAppleId = Optional.empty();
    private Optional<String> oauthAzureId = Optional.empty();
    private Optional<String> authFirebaseId = Optional.empty();
//    private Optional<List<DeviceInfo>> devices = Optional.empty();

    private Optional<String> fullName = Optional.empty();
    private Optional<String> firstName = Optional.empty();
    private Optional<String> lastName = Optional.empty();
    private Optional<String> avatar = Optional.empty();
    private Optional<String> gender = Optional.empty();
    private Optional<String> birthday = Optional.empty();

    private Optional<CustomerPnlMapping> pnlMapping = Optional.empty();

    @JsonIgnore
    public Long getCustomerId() {
        if (id.isPresent()) return id.get();
        if (userId.isPresent()) return Long.parseLong(userId.get());
        if (originalId.isPresent()) return Long.parseLong(originalId.get());
        return null;
    }
}
