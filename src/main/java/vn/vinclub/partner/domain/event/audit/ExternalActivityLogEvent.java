package vn.vinclub.partner.domain.event.audit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.domain.event.BaseEvent;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExternalActivityLogEvent implements BaseEvent {
    public static final String EVENT_CODE = "EXTERNAL_ACTIVITY_LOG";

    private String id;

    // actor
    private String externalSystem; // SKYJOY, KFC, ...
    private String username;
    private String ip;

    // action
    private String action; // GET_PROFILE, TOPUP_POINT, SPEND_POINT, ...
    private String object; // CUSTOMER, POINT, AUTHENTICATION, ...
    private String objectId;

    // time execute
    private Long requestTime;
    private Long responseTime;

    // data
    private String request;
    private String response;

    // status
    private Integer responseCode;
    private String responseMsg;


    @Builder.Default
    private String sourceSystem = AppConst.SOURCE_SYSTEM;

    @Override
    @JsonIgnore
    public String getEventCode() {
        return EVENT_CODE;
    }
}
