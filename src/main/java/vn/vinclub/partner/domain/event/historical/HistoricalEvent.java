package vn.vinclub.partner.domain.event.historical;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import vn.vinclub.partner.domain.enums.HistoricalAction;

/**
 * <AUTHOR> 7/11/24 14:45
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HistoricalEvent<T> {

    private HistoricalAction action;
    private Long timestamp;
    private String objectId;
    private T data;
    private T oldData;

    @JsonIgnore
    public Long objectIdAsLong() {
        return this.objectId != null ? Long.parseLong(this.objectId) : null;
    }
}