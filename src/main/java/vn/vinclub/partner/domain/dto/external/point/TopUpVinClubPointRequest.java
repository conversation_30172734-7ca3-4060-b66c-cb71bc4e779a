package vn.vinclub.partner.domain.dto.external.point;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TopUpVinClubPointRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // customer info
    @JsonIgnore
    private String partnerCode;

    @NotBlank(message = "partnerUserId is required")
    private String partnerUserId;

    @NotNull(message = "vclubUserId is required")
    private Long vclubUserId;

    // Partner transaction info
    @NotBlank(message = "partnerTransactionId is required")
    private String partnerTransactionId;

    @NotBlank(message = "description is required")
    private String description;

    @NotNull(message = "requestTime is required")
    private Long requestTime;

    // point info
    @NotNull(message = "topUpPoint is required")
    private Long topUpPoint; // the Vinclub point amount to be added to Vinclub account

    @NotBlank(message = "txnToken is required")
    private String txnToken; // sign by partner private key

    private ObjectNode metadata;

    @JsonIgnore
    public String getCustomerLockKey() {
        return String.format("%s-%s-%s", partnerCode, partnerUserId, vclubUserId);
    }
}
