package vn.vinclub.partner.domain.dto.point;

import lombok.*;
import vn.vinclub.partner.domain.enums.PartnerStatus;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PartnerPointConfigFilterDto {
    private Long partnerId;
    private String code;
    private String name;
    private Boolean isDefault;

    @Builder.Default
    private PartnerStatus status = PartnerStatus.ACTIVE;

    @Builder.Default
    private Boolean active = true;
}
