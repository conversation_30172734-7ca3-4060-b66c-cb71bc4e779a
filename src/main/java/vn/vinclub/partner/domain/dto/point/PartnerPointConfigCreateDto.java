package vn.vinclub.partner.domain.dto.point;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.dto.SignedUrlInfoDto;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerPointConfigCreateDto {
    private Long partnerId;

    @NotBlank(message = "code cannot be blank")
    @Size(max = 50, message = "code must not exceed 50 characters")
    private String code;

    @NotEmpty(message = "displayNames cannot be empty")
    private Map<String, String> displayNames;

    @NotEmpty(message = "images cannot be empty")
    private Map<String, SignedUrlInfoDto> images;

    @NotNull(message = "exchangeCashRate cannot be null")
    @Valid
    private ExchangeCashRate exchangeCashRate;

    @NotNull(message = "isDefault cannot be null")
    private Boolean isDefault;
}
