package vn.vinclub.partner.domain.dto.mapping;

import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerUserMappingDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long partnerId;
    private Long vclubUserId;
    private String partnerUserId;
    private Long mappingAt;
    private Long deleteAt;
    private ObjectNode partnerUserIdentity;
    private Boolean active;

    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
}
