package vn.vinclub.partner.domain.dto.module.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SwapPointConfig {
    private boolean partnerToVclubEnabled;
    private boolean vclubToPartnerEnabled;
    private PointConversionRule conversionRule;

    // limit
    private boolean limitEnabled;
    private Long minAmount;
    private Long maxAmount;

    // quota
    private boolean quotaEnabled;
    private  Long dailyQuota;
    private  Long monthlyQuota;
}