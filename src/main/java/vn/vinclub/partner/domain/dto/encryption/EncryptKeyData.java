package vn.vinclub.partner.domain.dto.encryption;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import vn.vinclub.partner.domain.enums.EncryptKeyType;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldNameConstants
public class EncryptKeyData {
    private EncryptKeyType encryptKeyType;
    private String publicKey;
    private String privateKey;
    private String secretKey;
}