package vn.vinclub.partner.domain.dto.external.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.client.response.CoreCustomerBalanceDto;

import java.io.Serial;
import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VClubCustomerBalanceDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    
    private Long vclubUserId;
    private String pointCode;
    private Long balance;

    public static VClubCustomerBalanceDto from(CoreCustomerBalanceDto coreCustomerBalanceDto) {
        return VClubCustomerBalanceDto.builder()
                .vclubUserId(coreCustomerBalanceDto.getVclubUserId())
                .pointCode(coreCustomerBalanceDto.getTypeCode())
                .balance(coreCustomerBalanceDto.getPoints())
                .build();
    }
}
