package vn.vinclub.partner.domain.dto.external.point;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import vn.vinclub.partner.domain.enums.SwapPointDirection;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerSwapPointInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private SwapPointDirection direction;
    private Long vclubPointAmount;
    private Long partnerPointAmount;
    private String partnerPointCode;
    private Long appliedFeePointAmount;
    private String appliedSwapRate;
}
