package vn.vinclub.partner.domain.dto.external.point;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;

import java.io.Serial;
import java.io.Serializable;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PartnerPointTxnResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    // customer info
    private String partnerUserId;

    // transaction info
    private String transactionId;
    private String partnerTransactionId;
    private Long processedTime;
    private Long partnerPoint;
    private PointHistoryStatus status;
    private String failedReason;

    // log request/response
    private String request;
    private String response;
}
