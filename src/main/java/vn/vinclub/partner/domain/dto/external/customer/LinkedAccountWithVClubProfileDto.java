package vn.vinclub.partner.domain.dto.external.customer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class LinkedAccountWithVClubProfileDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long vclubUserId;
    private String partnerUserId;
    private Long linkedAt;
    private VClubCustomerProfileDto profile;
}
