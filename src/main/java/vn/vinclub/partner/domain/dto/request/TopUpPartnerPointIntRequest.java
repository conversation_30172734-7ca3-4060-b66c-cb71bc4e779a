package vn.vinclub.partner.domain.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopUpPartnerPointIntRequest {
    @NotNull(message = "vclubUserId is required")
    private Long vclubUserId;

    @NotNull(message = "topUpPoint is required")
    private Long topUpPoint;

    private String pointTypeCode;

    private String description;

    @NotBlank(message = "internalRefCode is required")
    private String internalRefCode; // used to track transaction in internal system

    @NotNull(message = "requestTime is required")
    private Long requestTime;

    private ObjectNode metadata; // extra metadata

    // enhancement
    @JsonIgnore
    private Long partnerId;
    @JsonIgnore
    private String partnerUserId;
}
