package vn.vinclub.partner.domain.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import vn.vinclub.partner.domain.dto.module.config.TermAndCondition;
import vn.vinclub.partner.domain.enums.LinkAccountFlowType;
import vn.vinclub.partner.domain.enums.LinkAccountStep;


@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public record LinkablePartnerResponse(
        String partnerCode,
        String partnerName,
        String description,
        String logo,

        TermAndCondition termAndCondition,
        LinkAccountFlowType flowType,
        boolean linked,
        Long linkedAt,
        LinkAccountStep nextStep,

        boolean allowUnlink,
        TermAndCondition unLinkTermsAndConditions
) {
}
