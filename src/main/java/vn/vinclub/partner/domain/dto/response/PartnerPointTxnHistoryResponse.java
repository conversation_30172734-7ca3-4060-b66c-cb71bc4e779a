package vn.vinclub.partner.domain.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PartnerPointTxnHistoryResponse {
    // customer info
    private String partnerCode;
    private Long vclubUserId;
    private String partnerUserId;

    // transaction info
    private ActorSystem actorSystem;
    private TransactionType transactionType;
    private String partnerTransactionId;
    private String transactionId;
    private Long pointAmount;
    private String pointCode;
    private String description;
    private PointHistoryStatus status;
    private Long requestTime;
    private Long processedTime;
}
