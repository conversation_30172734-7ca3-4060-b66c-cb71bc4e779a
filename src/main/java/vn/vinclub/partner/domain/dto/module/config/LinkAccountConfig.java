package vn.vinclub.partner.domain.dto.module.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LinkAccountConfig {
    private Map<String, TermAndCondition> termsAndConditions;
    private boolean ekycRequired;
    private boolean phoneRequired;
    private boolean emailRequired;
    private boolean allowUnlink;

    private Map<String, TermAndCondition> unLinkTermsAndConditions;
}