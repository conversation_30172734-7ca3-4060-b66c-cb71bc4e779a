package vn.vinclub.partner.domain.dto.partner;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.dto.SignedUrlInfoDto;
import vn.vinclub.partner.domain.dto.encryption.EncryptKeySetting;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerCreateDto {
    @NotBlank(message = "code cannot be blank")
    @Size(max = 50, message = "code must not exceed 50 characters")
    private String code;

    @NotEmpty(message = "displayNames cannot be empty")
    private Map<String, String> displayNames;

    private Map<String, String> descriptions;

    @NotEmpty(message = "logos cannot be empty")
    private Map<String, SignedUrlInfoDto> logos;

    @NotNull(message = "encryptKeySetting cannot be empty")
    private EncryptKeySetting encryptKeySetting;

    private JsonNode metadata;
}
