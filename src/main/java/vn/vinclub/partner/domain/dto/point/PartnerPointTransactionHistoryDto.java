package vn.vinclub.partner.domain.dto.point;

import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.*;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerPointTransactionHistoryDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long partnerId;
    private String partnerUserId;
    private Long vclubUserId;
    private ActorSystem actorSystem;
    private TransactionType transactionType;
    private String transactionId;
    private String partnerTransactionId;
    private String description;
    private Long pointAmount;
    private String pointCode;
    private Long requestTime;
    private String internalRefCode;
    private Long processedTime;
    private ObjectNode metadata;
    private Boolean active;
    private PointHistoryStatus status;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;

    // enhancement
    private String partnerName;
}
