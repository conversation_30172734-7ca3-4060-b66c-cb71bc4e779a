package vn.vinclub.partner.domain.dto.external.point;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerOrderInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "orderId is required")
    private String orderId;

    private String orderDescription;

    private Long amount; // VND

    @NotNull(message = "totalAmount is required")
    private Long totalAmount; // VND

    private Long quantity;
}
