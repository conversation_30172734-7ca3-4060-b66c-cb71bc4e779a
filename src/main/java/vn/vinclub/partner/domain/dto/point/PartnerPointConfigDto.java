package vn.vinclub.partner.domain.dto.point;

import lombok.*;
import vn.vinclub.partner.domain.dto.SignedUrlInfoDto;
import vn.vinclub.partner.domain.enums.PartnerStatus;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerPointConfigDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long partnerId;
    private String code;
    private Map<String, String> displayNames;
    private Map<String, SignedUrlInfoDto> images;
    private ExchangeCashRate exchangeCashRate;
    private Boolean isDefault;
    private Boolean active;
    private PartnerStatus status;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
}
