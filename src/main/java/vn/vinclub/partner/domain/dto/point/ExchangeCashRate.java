package vn.vinclub.partner.domain.dto.point;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.constant.AppConst;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExchangeCashRate {

    @Builder.Default
    private String currency = AppConst.DEFAULT_CURRENCY;

    @Builder.Default
    private Long pointValue = 1L;

    @NotNull(message = "exchangeValue is required")
    private BigDecimal exchangeValue;
}