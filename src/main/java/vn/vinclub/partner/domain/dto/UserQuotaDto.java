package vn.vinclub.partner.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.enums.TransactionType;

import java.math.BigDecimal;
import java.util.Map;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserQuotaDto {
    private Long totalTransaction;
    private BigDecimal totalCashAmount;

    private Map<TransactionType, Map<String, Long>> totalPartnerPointTransactionByTypeAndPointCode;
    private Map<TransactionType, Map<String, Long>> totalPartnerPointAmountByTypeAndPointCode;

    private Map<TransactionType, Long> totalVinclubPointTransactionByType;
    private Map<TransactionType, Long> totalVinclubPointAmountByType;
}
