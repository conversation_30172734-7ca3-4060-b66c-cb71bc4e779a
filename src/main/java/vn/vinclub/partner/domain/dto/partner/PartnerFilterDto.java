package vn.vinclub.partner.domain.dto.partner;

import lombok.*;
import vn.vinclub.partner.domain.enums.PartnerStatus;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PartnerFilterDto {
    private String code;
    private String name;
    private PartnerStatus status;

    @Builder.Default
    private Boolean active = true;

    private List<String> tags;

}
