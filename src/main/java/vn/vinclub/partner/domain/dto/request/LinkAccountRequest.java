package vn.vinclub.partner.domain.dto.request;

import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.enums.LinkAccountStep;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LinkAccountRequest {
    @NotNull(message = "step is required")
    private LinkAccountStep step;
    private ObjectNode data;

    // enhancement
    private String partnerCode;
    private Long vclubUserId;
    private String partnerUserId;
}
