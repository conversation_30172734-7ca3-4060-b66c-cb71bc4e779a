package vn.vinclub.partner.domain.dto.external.point;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SpendVinClubPointRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // customer info
    private String partnerCode;

    @NotBlank(message = "partnerUserId is required")
    private String partnerUserId;

    @NotNull(message = "vclubUserId is required")
    private Long vclubUserId;

    // Partner transaction info
    @NotBlank(message = "partnerTransactionId is required")
    private String partnerTransactionId;

    @NotBlank(message = "description is required")
    private String description;

    @NotNull(message = "requestTime is required")
    private Long requestTime;

    @NotNull(message = "orderInfo is required")
    @Valid
    private PartnerOrderInfo orderInfo;

    // point info
    @NotNull(message = "spendPoint is required")
    private Long spendPoint; // the Vinclub point to be spent

    @NotBlank(message = "txnToken is required")
    private String txnToken; // sign by partner private key

    @JsonIgnore
    public String getCustomerLockKey() {
        return String.format("%s-%s-%s", partnerCode, partnerUserId, vclubUserId);
    }
}
