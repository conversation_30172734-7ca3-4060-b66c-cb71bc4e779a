package vn.vinclub.partner.domain.dto.external.customer;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VClubUserIdentityDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long vclubUserId;
    private String phone;
    private String email;

}
