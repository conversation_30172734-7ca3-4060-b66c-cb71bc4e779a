package vn.vinclub.partner.domain.dto.auth;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import vn.vinclub.partner.domain.enums.AuthMethod;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthorizationConfig {
    @NotNull(message = "method cannot be empty")
    private AuthMethod method;
    private OAuth oauth;
    private TokenAuth token;
    private BasicAuth basic;
    private ApiKeyAuth apiKey;
}