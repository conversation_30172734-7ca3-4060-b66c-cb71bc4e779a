package vn.vinclub.partner.domain.dto.auth;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OAuth {
    private String accessTokenUrl;
    private String grantType;
    private String clientId;
    private String clientSecret;
    private String username;
    private String password;
    private String scope;
}