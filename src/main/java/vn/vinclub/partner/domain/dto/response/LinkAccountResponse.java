package vn.vinclub.partner.domain.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Builder;
import vn.vinclub.partner.domain.enums.LinkAccountStep;

@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public record LinkAccountResponse(
        boolean success,
        LinkAccountStep nextStep,
        ObjectNode data
) {
}
