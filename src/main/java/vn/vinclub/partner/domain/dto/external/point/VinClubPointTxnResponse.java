package vn.vinclub.partner.domain.dto.external.point;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;

import java.io.Serial;
import java.io.Serializable;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VinClubPointTxnResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    // customer info
    private Long vclubUserId;
    private String partnerUserId;

    // transaction info
    private ActorSystem actorSystem;
    private TransactionType transactionType;
    private String partnerTransactionId;
    private String transactionId;
    private Long processedTime;
    private Long vclubPoint;
    private PointHistoryStatus status;
}
