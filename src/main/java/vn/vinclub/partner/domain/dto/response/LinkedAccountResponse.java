package vn.vinclub.partner.domain.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LinkedAccountResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String partnerCode;
    private String partnerUserId;
    private Long vclubUserId;
    private Long linkedAt;
}
