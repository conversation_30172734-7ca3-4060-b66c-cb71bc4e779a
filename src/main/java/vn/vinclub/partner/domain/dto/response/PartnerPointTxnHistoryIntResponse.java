package vn.vinclub.partner.domain.dto.response;

import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerPointTxnHistoryIntResponse {
    // customer info
    private Long partnerId;
    private Long vclubUserId;
    private String partnerUserId;

    // transaction info
    private ActorSystem actorSystem;
    private TransactionType transactionType;
    private String partnerTransactionId;
    private String transactionId;
    private String internalRefCode;
    private Long pointAmount;
    private String pointCode;
    private String description;
    private PointHistoryStatus status;
    private ObjectNode metadata;
    private Long requestTime;
    private Long processedTime;

    // common
    private Long id;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private Boolean active;
}
