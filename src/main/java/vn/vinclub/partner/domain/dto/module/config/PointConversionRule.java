package vn.vinclub.partner.domain.dto.module.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.enums.ConversionType;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PointConversionRule {
    private ConversionType type;

    // fixed
    private BigDecimal toPartnerRate;
    private BigDecimal toVinClubRate;

    // cash equivalent
    private BigDecimal partnerCashValue; // VND (after Tax)

    // amount-based
    private List<RateByRange> toPartnerRateByAmount;
    private List<RateByRange> toVinClubRateByAmount;

    // time-based
    private List<RateByRange> toPartnerRateByTime;
    private List<RateByRange> toVinClubRateByTime;
}