package vn.vinclub.partner.domain.dto.encryption;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import vn.vinclub.partner.domain.enums.EncryptKeyType;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EncryptKeySetting {
    private EncryptKeyType keyType;
    private Integer keySize;
}