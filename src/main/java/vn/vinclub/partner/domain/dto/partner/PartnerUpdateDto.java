package vn.vinclub.partner.domain.dto.partner;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.dto.SignedUrlInfoDto;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerUpdateDto {
    private Map<String, String> displayNames;
    private Map<String, String> descriptions;
    private Map<String, SignedUrlInfoDto> logos;
    private JsonNode metadata;
}
