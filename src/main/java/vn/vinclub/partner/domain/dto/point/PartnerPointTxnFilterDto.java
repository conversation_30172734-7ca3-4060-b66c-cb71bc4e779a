package vn.vinclub.partner.domain.dto.point;

import lombok.*;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PartnerPointTxnFilterDto {
    private Long partnerId;
    private String partnerUserId;
    private Long vclubUserId;
    private String phone;
    private String email;

    private ActorSystem actorSystem;
    private TransactionType transactionType;
    private String transactionId;
    private String partnerTransactionId;
    private String internalRefCode;
    private PointHistoryStatus status;

    // time filter
    private Long createdOnFrom;
    private Long createdOnTo;

    private Long updatedOnFrom;
    private Long updatedOnTo;

    private Long requestTimeFrom;
    private Long requestTimeTo;

    private Long processedTimeFrom;
    private Long processedTimeTo;

    // amount filter
    private Long pointAmountFrom;
    private Long pointAmountTo;

    // list filter
    private List<PointHistoryStatus> statuses;
    private List<ActorSystem> actorSystems;
    private List<TransactionType> transactionTypes;
    private List<String> transactionIds;
    private List<String> partnerTransactionIds;
    private List<String> internalRefCodes;

    @Builder.Default
    private Boolean active = true;
}
