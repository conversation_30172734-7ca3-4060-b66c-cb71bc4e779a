package vn.vinclub.partner.domain.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.dto.SignedUrlInfoDto;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerPointConfigIntResponse {
    private String pointTypeCode;
    private Map<String, String>  pointTypeDisplayNames;
    private Map<String, SignedUrlInfoDto> pointTypeCodeLogos;

    @JsonIgnore
    private Long pointValue;

    @JsonIgnore
    private BigDecimal exchangeValue;

    @JsonIgnore
    private String currency;

    // pointCashValue = exchangeValue of 1 point in VND
    public BigDecimal getPointCashValue() {
        return exchangeValue.divide(BigDecimal.valueOf(pointValue), RoundingMode.HALF_UP);
    }

}
