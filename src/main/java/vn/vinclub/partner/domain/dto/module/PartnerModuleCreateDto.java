package vn.vinclub.partner.domain.dto.module;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.enums.ModuleType;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerModuleCreateDto {
    private Long partnerId;

    @NotNull(message = "module cannot be null")
    private ModuleType module;

    private JsonNode config;

    private boolean enabled;
}