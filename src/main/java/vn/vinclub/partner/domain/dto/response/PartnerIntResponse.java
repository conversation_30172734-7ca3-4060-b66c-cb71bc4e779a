package vn.vinclub.partner.domain.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.dto.SignedUrlInfoDto;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerIntResponse {
    private Long partnerId;
    private String partnerCode;
    private Map<String, String> displayNames;
    private Map<String, String> descriptions;
    private Map<String, SignedUrlInfoDto> logos;

    private Map<String, Object> interactionLinks;

    private Map<String, PartnerPointConfigIntResponse> pointConfigs;
}
