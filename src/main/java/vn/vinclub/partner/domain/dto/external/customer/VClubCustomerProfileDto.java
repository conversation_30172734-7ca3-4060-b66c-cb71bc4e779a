package vn.vinclub.partner.domain.dto.external.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.common.util.DateUtils;
import vn.vinclub.partner.client.response.CoreCustomerDto;

import java.io.Serial;
import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VClubCustomerProfileDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long vclubUserId;
    private String phone;
    private String email;
    private String fullName;
    private String firstName;
    private String lastName;
    private String dateOfBirth;
    private boolean eKycStatus;
    private String tier;
    private String status;

    public static VClubCustomerProfileDto from(CoreCustomerDto customerDto) {
        return VClubCustomerProfileDto.builder()
                .vclubUserId(customerDto.getId())
                .phone(customerDto.getPhoneNumber())
                .email(customerDto.getEmail())
                .firstName(customerDto.getFirstName())
                .lastName(customerDto.getLastName())
                .fullName(customerDto.getFullName())
                .dateOfBirth(DateUtils.convertLocalDateToString(customerDto.getDob(), DateUtils.DATE_PATTERN_YYYY_MM_DD))
                .eKycStatus(customerDto.isIdentityDocumentVerified())
                .tier(customerDto.getTier().getCode())
                .status(customerDto.getStatus().getCode())
                .build();
    }

}
