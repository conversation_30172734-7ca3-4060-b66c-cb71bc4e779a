package vn.vinclub.partner.domain.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.*;
import vn.vinclub.partner.domain.enums.FileTypeEnum;

import java.io.Serial;
import java.io.Serializable;
import java.net.URL;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SignedUrlInfoDto implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	private URL url;

	private LocalDateTime expiredIn;

	@JsonAlias({"fileName", "imageName"})
	private String fileName;

	private FileTypeEnum type;

	private String format;

	private Boolean isPublic;

	private URL preview;

}
