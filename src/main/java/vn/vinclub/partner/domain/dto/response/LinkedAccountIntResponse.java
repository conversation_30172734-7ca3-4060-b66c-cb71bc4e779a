package vn.vinclub.partner.domain.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LinkedAccountIntResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Boolean isLinked;

    private Long partnerId;
    private String partnerCode;
    private Long vclubUserId;
    private String partnerUserId;
    private Long mappingAt;
}
