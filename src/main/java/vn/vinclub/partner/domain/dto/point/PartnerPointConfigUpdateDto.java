package vn.vinclub.partner.domain.dto.point;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.partner.domain.dto.SignedUrlInfoDto;
import vn.vinclub.partner.domain.enums.PartnerStatus;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerPointConfigUpdateDto {
    private Map<String, String> displayNames;
    private Map<String, SignedUrlInfoDto> images;
    private ExchangeCashRate exchangeCashRate;
    private Boolean isDefault;
    private Boolean active;
    private PartnerStatus status;
}
