package vn.vinclub.partner.domain.mapper;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;
import vn.vinclub.partner.domain.dto.module.PartnerModuleCreateDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleUpdateDto;
import vn.vinclub.partner.domain.entity.PartnerModule;

@Mapper(componentModel = "spring")
public interface PartnerModuleMapper {
    PartnerModuleMapper INSTANCE = Mappers.getMapper(PartnerModuleMapper.class);

    PartnerModuleDto toDto(PartnerModule entity);

    PartnerModule toEntity(PartnerModuleCreateDto createDto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(PartnerModuleUpdateDto updateDto, @MappingTarget PartnerModule entity);
}