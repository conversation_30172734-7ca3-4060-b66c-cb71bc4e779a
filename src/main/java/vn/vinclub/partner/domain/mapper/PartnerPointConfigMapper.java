package vn.vinclub.partner.domain.mapper;

import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigCreateDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigUpdateDto;
import vn.vinclub.partner.domain.dto.response.PartnerPointConfigIntResponse;
import vn.vinclub.partner.domain.entity.PartnerPointConfig;

@Mapper(componentModel = "spring")
public interface PartnerPointConfigMapper {
    PartnerPointConfigMapper INSTANCE = Mappers.getMapper(PartnerPointConfigMapper.class);

    PartnerPointConfigDto toDto(PartnerPointConfig partnerPointConfig);

    PartnerPointConfig toEntity(PartnerPointConfigCreateDto dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget PartnerPointConfig entity, PartnerPointConfigUpdateDto dto);

    @Mapping(target = "pointTypeCode", source = "code")
    @Mapping(target = "pointTypeDisplayNames", source = "displayNames")
    @Mapping(target = "pointTypeCodeLogos", source = "images")
    @Mapping(target = "currency", source = "exchangeCashRate.currency")
    @Mapping(target = "exchangeValue", source = "exchangeCashRate.exchangeValue")
    @Mapping(target = "pointValue", source = "exchangeCashRate.pointValue")
    PartnerPointConfigIntResponse toIntResponseDto(PartnerPointConfig partnerPointConfig);
}