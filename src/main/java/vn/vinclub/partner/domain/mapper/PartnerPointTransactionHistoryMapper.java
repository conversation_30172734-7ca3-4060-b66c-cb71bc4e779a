package vn.vinclub.partner.domain.mapper;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import vn.vinclub.partner.domain.dto.external.point.VinClubPointTxnResponse;
import vn.vinclub.partner.domain.dto.point.PartnerPointTransactionHistoryDto;
import vn.vinclub.partner.domain.dto.response.PartnerPointTxnHistoryIntResponse;
import vn.vinclub.partner.domain.dto.response.PartnerPointTxnHistoryResponse;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;
import vn.vinclub.partner.domain.event.internal.PartnerPointTxnRequestEvent;
import vn.vinclub.partner.domain.event.internal.PartnerPointTxnResponseEvent;

import java.time.Instant;

/**
 * Mapper for the entity {@link PartnerPointTransactionHistory}.
 */
@Mapper(componentModel = "spring")
public interface PartnerPointTransactionHistoryMapper {

    PartnerPointTransactionHistoryMapper INSTANCE = Mappers.getMapper(PartnerPointTransactionHistoryMapper.class);

    /**
     * Creates a new transaction history entity with the given parameters.
     *
     * @param partnerId            Partner ID
     * @param partnerUserId        Partner user ID
     * @param vclubUserId          VinClub user ID
     * @param actorSystem          Actor system (PARTNER or VINCLUB)
     * @param transactionType      Transaction type
     * @param transactionId        Transaction ID
     * @param partnerTransactionId Partner transaction ID (can be null for VINCLUB actor)
     * @param description          Transaction description
     * @param pointAmount          Point amount
     * @param pointCode            Point code
     * @param internalRefCode      Internal reference code
     * @param metadata             Additional metadata
     * @return A new transaction history entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "status", constant = "PROCESSING")
    @Mapping(target = "requestTime", expression = "java(java.time.Instant.now().toEpochMilli())")
    @Mapping(target = "processedTime", ignore = true)
    default PartnerPointTransactionHistory createTransaction(
            Long partnerId,
            String partnerUserId,
            Long vclubUserId,
            ActorSystem actorSystem,
            TransactionType transactionType,
            String transactionId,
            String partnerTransactionId,
            String description,
            Long pointAmount,
            String pointCode,
            String internalRefCode,
            ObjectNode metadata) {

        PartnerPointTransactionHistory txn = new PartnerPointTransactionHistory();
        txn.setPartnerId(partnerId);
        txn.setPartnerUserId(partnerUserId);
        txn.setVclubUserId(vclubUserId);
        txn.setActorSystem(actorSystem);
        txn.setTransactionType(transactionType);
        txn.setTransactionId(transactionId);
        txn.setPartnerTransactionId(partnerTransactionId);
        txn.setDescription(description);
        txn.setPointAmount(pointAmount);
        txn.setPointCode(pointCode);
        txn.setRequestTime(Instant.now().toEpochMilli());
        txn.setMetadata(metadata);
        txn.setInternalRefCode(internalRefCode);
        txn.setStatus(PointHistoryStatus.PROCESSING);

        return txn;
    }

    @Mapping(target = "vclubPoint", source = "pointAmount")
    VinClubPointTxnResponse toExternalResponseDto(PartnerPointTransactionHistory entity);

    PartnerPointTxnHistoryIntResponse toInternalResponseDto(PartnerPointTransactionHistory entity);

    PartnerPointTxnHistoryResponse toResponseDto(PartnerPointTransactionHistory transaction);

    PartnerPointTxnRequestEvent toRequestEvent(PartnerPointTransactionHistory entity);

    PartnerPointTxnResponseEvent toResponseEvent(PartnerPointTransactionHistory entity);

    PartnerPointTransactionHistoryDto toDto(PartnerPointTransactionHistory partnerPointTransactionHistory);
}