package vn.vinclub.partner.domain.mapper;

import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import vn.vinclub.partner.domain.dto.mapping.PartnerUserMappingCreateDto;
import vn.vinclub.partner.domain.dto.mapping.PartnerUserMappingDto;
import vn.vinclub.partner.domain.dto.mapping.PartnerUserMappingUpdateDto;
import vn.vinclub.partner.domain.dto.response.LinkedAccountIntResponse;
import vn.vinclub.partner.domain.dto.response.LinkedAccountResponse;
import vn.vinclub.partner.domain.entity.PartnerUserMapping;

@Mapper(componentModel = "spring")
public interface PartnerUserMappingMapper {
    PartnerUserMappingMapper INSTANCE = Mappers.getMapper(PartnerUserMappingMapper.class);

    PartnerUserMappingDto toDto(PartnerUserMapping partnerUserMapping);

    PartnerUserMapping toEntity(PartnerUserMappingCreateDto createDto);

    @Mapping(source = "mappingAt", target = "linkedAt")
    LinkedAccountResponse toLinkedAccountResponse(PartnerUserMapping partnerUserMapping);

    @Mapping(target = "isLinked", constant = "true")
    LinkedAccountIntResponse toLinkedAccountIntResponse(PartnerUserMapping partnerUserMapping);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget PartnerUserMapping entity, PartnerUserMappingUpdateDto dto);
}
