package vn.vinclub.partner.domain.mapper;

import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import vn.vinclub.partner.domain.dto.partner.PartnerCreateDto;
import vn.vinclub.partner.domain.dto.partner.PartnerDto;
import vn.vinclub.partner.domain.dto.partner.PartnerUpdateDto;
import vn.vinclub.partner.domain.dto.response.PartnerIntResponse;
import vn.vinclub.partner.domain.entity.Partner;

@Mapper(componentModel = "spring")
public interface PartnerMapper {
    PartnerMapper INSTANCE = Mappers.getMapper(PartnerMapper.class);

    PartnerDto toDto(Partner partner);

    Partner toEntity(PartnerCreateDto dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget Partner entity, PartnerUpdateDto dto);

    @Mapping(target = "partnerId", source = "id")
    @Mapping(target = "partnerCode", source = "code")
    PartnerIntResponse toIntResponse(Partner partner);
}
