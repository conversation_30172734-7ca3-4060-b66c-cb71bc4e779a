package vn.vinclub.partner.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import vn.vinclub.partner.converter.PointTransactionTypeConverter;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;

@Entity
@Table(name = "partner_point_transaction_history")
@Getter
@Setter
@FieldNameConstants
public class PartnerPointTransactionHistory extends BaseEntity {

    @Column(name = "partner_id", nullable = false)
    private Long partnerId;

    @Column(name = "partner_user_id", nullable = false, length = 100)
    private String partnerUserId;

    @Column(name = "vclub_user_id", nullable = false)
    private Long vclubUserId;

    @Column(name = "actor_system", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private ActorSystem actorSystem;

    @Column(name = "transaction_type", nullable = false, length = 50)
    @Convert(converter = PointTransactionTypeConverter.class)
    private TransactionType transactionType;

    @Column(name = "transaction_id", length = 255)
    private String transactionId;

    @Column(name = "partner_transaction_id", length = 255)
    private String partnerTransactionId;

    @Column(name = "description", length = 255)
    private String description;

    @Column(name = "point_amount", nullable = false)
    private Long pointAmount;

    @Column(name = "point_code", nullable = false)
    private String pointCode;

    @Column(name = "request_time")
    private Long requestTime;

    @Column(name = "internal_ref_code")
    private String internalRefCode; // used to track transaction in internal system

    @Column(name = "processed_time")
    private Long processedTime;

    @Type(JsonBinaryType.class)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private ObjectNode metadata;

    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private PointHistoryStatus status;

    @Column(name = "active", nullable = false, columnDefinition = "boolean default true")
    private Boolean active;

    @PrePersist
    void preInsert() {
        if (this.status == null) {
            this.status = PointHistoryStatus.PROCESSING;
        }
        this.active = true;
    }

    @JsonIgnore
    @Transient
    @Override
    public String getHistoricalTopic() {
        return "historical.partner_point_transaction_history";
    }

    @JsonIgnore
    @Transient
    @Override
    public String getHistoricalMessageKey() {
        return String.valueOf(transactionId);
    }
}
