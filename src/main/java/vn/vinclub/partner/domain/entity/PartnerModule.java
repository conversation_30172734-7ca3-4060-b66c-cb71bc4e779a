package vn.vinclub.partner.domain.entity;

import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import vn.vinclub.partner.domain.enums.ModuleType;

@Entity
@Table(name = "partner_module")
@Getter
@Setter
@FieldNameConstants
public class PartnerModule extends BaseEntity {

    @Column(name = "partner_id", nullable = false)
    private Long partnerId;

    @Column(name = "module", nullable = false)
    @Enumerated(EnumType.STRING)
    private ModuleType module;

    @Type(JsonBinaryType.class)
    @Column(name = "config", columnDefinition = "jsonb")
    private JsonNode config;

    @Column(name = "enabled")
    private boolean enabled;
}