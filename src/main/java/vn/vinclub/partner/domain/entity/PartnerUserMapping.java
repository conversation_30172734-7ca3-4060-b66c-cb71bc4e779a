package vn.vinclub.partner.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "partner_user_mapping")
@Getter
@Setter
public class PartnerUserMapping extends BaseEntity {

    @Column(name = "partner_id", nullable = false)
    private Long partnerId;

    @Column(name = "partner_user_id", nullable = false, length = 50)
    private String partnerUserId;

    @Column(name = "vclub_user_id", nullable = false)
    private Long vclubUserId;

    @Type(JsonBinaryType.class)
    @Column(name = "partner_user_identity", columnDefinition = "jsonb")
    private ObjectNode partnerUserIdentity;

    @Column(name = "mapping_at")
    private Long mappingAt;

    @Column(name = "delete_at")
    private Long deleteAt;

    @Column(name = "active")
    private Boolean active;

    @PrePersist
    void preInsert() {
        this.active = true;
    }

    @JsonIgnore
    @Transient
    @Override
    public String getHistoricalTopic() {
        return "historical.partner_user_mapping";
    }

    @JsonIgnore
    @Transient
    @Override
    public String getHistoricalMessageKey() {
        return String.valueOf(vclubUserId);
    }



}
