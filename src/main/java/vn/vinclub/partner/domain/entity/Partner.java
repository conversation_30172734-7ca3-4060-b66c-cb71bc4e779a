package vn.vinclub.partner.domain.entity;

import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.array.StringArrayType;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import vn.vinclub.partner.converter.PartnerStatusConverter;
import vn.vinclub.partner.domain.dto.SignedUrlInfoDto;
import vn.vinclub.partner.domain.dto.encryption.EncryptKeyData;
import vn.vinclub.partner.domain.enums.PartnerStatus;

import java.util.Map;

@Entity
@Table(name = "partner")
@Getter
@Setter
@FieldNameConstants
public class Partner extends BaseEntity {

    @Column(name = "code", nullable = false, length = 50)
    private String code;

    @Type(JsonBinaryType.class)
    @Column(name = "display_names", columnDefinition = "jsonb", nullable = false)
    private Map<String, String> displayNames;

    @Type(JsonBinaryType.class)
    @Column(name = "descriptions", columnDefinition = "jsonb")
    private Map<String, String> descriptions;

    @Type(JsonBinaryType.class)
    @Column(name = "logos", columnDefinition = "jsonb", nullable = false)
    private Map<String, SignedUrlInfoDto> logos;

    @Type(JsonBinaryType.class)
    @Column(name = "encryption_key", columnDefinition = "jsonb")
    private EncryptKeyData encryptionKey;

    @Type(JsonBinaryType.class)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private JsonNode metadata;

    @Column(name = "active")
    private boolean active;

    @Column(name = "status", length = 50)
    @Convert(converter = PartnerStatusConverter.class)
    private PartnerStatus status;

    @Type(StringArrayType.class)
    @Column(name = "tags", columnDefinition = "text[]")
    private String[] tags;

    @PrePersist
    void preInsert() {
        this.active = true;
        if (this.status == null) {
            this.status = PartnerStatus.PENDING;
        }
    }

}