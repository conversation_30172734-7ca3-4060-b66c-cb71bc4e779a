package vn.vinclub.partner.domain.entity;

import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import vn.vinclub.partner.converter.PartnerStatusConverter;
import vn.vinclub.partner.domain.dto.SignedUrlInfoDto;
import vn.vinclub.partner.domain.dto.point.ExchangeCashRate;
import vn.vinclub.partner.domain.enums.PartnerStatus;

import java.util.Map;

@Entity
@Table(name = "partner_point_config")
@Getter
@Setter
@FieldNameConstants
public class PartnerPointConfig extends BaseEntity {

    @Column(name = "partner_id", nullable = false)
    private Long partnerId;

    @Column(name = "code", nullable = false, length = 50)
    private String code;

    @Type(JsonBinaryType.class)
    @Column(name = "display_names", columnDefinition = "jsonb", nullable = false)
    private Map<String, String> displayNames;

    @Type(JsonBinaryType.class)
    @Column(name = "images", columnDefinition = "jsonb", nullable = false)
    private Map<String, SignedUrlInfoDto> images;

    @Type(JsonBinaryType.class)
    @Column(name = "exchange_cash_rate", columnDefinition = "jsonb")
    private ExchangeCashRate exchangeCashRate;

    @Column(name = "is_default")
    private Boolean isDefault;

    @Column(name = "active")
    private Boolean active;

    @Column(name = "status", length = 50)
    @Convert(converter = PartnerStatusConverter.class)
    private PartnerStatus status;

    @PrePersist
    void preInsert() {
        this.active = true;
        if (this.status == null) {
            this.status = PartnerStatus.ACTIVE;
        }
    }
}
