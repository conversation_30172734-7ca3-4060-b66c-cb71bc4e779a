package vn.vinclub.partner.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

import java.io.Serial;

@MappedSuperclass
@Getter
@Setter
@ToString
@FieldNameConstants
public abstract class BaseEntity extends Auditable {
	
	@Serial
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Version
	private Integer version;

	@Transient
	@JsonIgnore
	public String getHistoricalTopic() {
		return "historical.untracked_entity";
	}

	@Transient
	@JsonIgnore
	public String getHistoricalMessageKey() {
		return String.valueOf(id);
	}

}
