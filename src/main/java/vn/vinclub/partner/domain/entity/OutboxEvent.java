package vn.vinclub.partner.domain.entity;

import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import vn.vinclub.partner.domain.enums.OutboxStatusEnum;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "outbox_event")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@FieldNameConstants
public class OutboxEvent extends BaseEntity {

    @Column(name = "event_id")
    private String eventId;

    @Column(name = "event_code")
    private String eventCode;

    @Column(name = "message_key")
    private String messageKey;

    @Type(JsonBinaryType.class)
    @Column(name = "payload", columnDefinition = "jsonb")
    private JsonNode payload;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private OutboxStatusEnum status;

    @Type(JsonBinaryType.class)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private JsonNode metadata;
}
