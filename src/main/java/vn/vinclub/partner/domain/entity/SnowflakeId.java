package vn.vinclub.partner.domain.entity;

import lombok.extern.slf4j.Slf4j;

import java.net.NetworkInterface;
import java.security.SecureRandom;
import java.time.Instant;
import java.util.Enumeration;

/**
 * <AUTHOR> 12/12/24 11:38
 */
@Slf4j
public class SnowflakeId {

    private final int UNUSED_BITS = 1; // Sign bit, Unused (always set to 0)
    private final int EPOCH_BITS = 41;
    private final int NODE_ID_BITS = 5; // 32 nodes
    private final int SEQUENCE_BITS = 12; // 4096 sequence

    private final long maxNodeId = (1L << NODE_ID_BITS) - 1;
    private final long maxSequence = (1L << SEQUENCE_BITS) - 1;

    // Custom Epoch (June 1, 2024 Midnight UTC = 2024-06-01T00:00:00Z)
    private final long DEFAULT_CUSTOM_EPOCH = 1717200000000L;

    private volatile long lastTimestamp = -1L;
    private volatile long sequence = 0L;

    private final String group;
    private final long customEpoch;
    private final long nodeId;

    public SnowflakeId() {
        this(null, null, null);
    }

    public SnowflakeId(String group, Long nodeId) {
        this(group, nodeId, null);
    }

    public SnowflakeId(String group, Long nodeId, Long customEpoch) {
        if (nodeId != null && nodeId < 0L) {
            throw new IllegalArgumentException("NodeId must be between 0 and " + maxNodeId);
        }

        this.group = group;
        this.customEpoch = customEpoch != null ? customEpoch : DEFAULT_CUSTOM_EPOCH;
        this.nodeId = nodeId != null ? (nodeId <= maxNodeId ? nodeId : nodeId % maxNodeId) : createNodeId();

        log.info(this.toString());
    }

    public synchronized long nextId() {
        long currentTimestamp = timestamp();

        if (currentTimestamp < lastTimestamp) {
            throw new IllegalStateException("Invalid System Clock!");
        }

        if (currentTimestamp == lastTimestamp) {
            sequence = (sequence + 1) & maxSequence;
            if (sequence == 0) {
                currentTimestamp = waitNextMillis(currentTimestamp);
            }
        } else {
            sequence = 0;
        }

        lastTimestamp = currentTimestamp;

        return (currentTimestamp << (NODE_ID_BITS + SEQUENCE_BITS)) | (nodeId << SEQUENCE_BITS) | sequence;
    }

    private long timestamp() {
        return Instant.now().toEpochMilli() - customEpoch;
    }

    private long waitNextMillis(long currentTimestamp) {
        long _currentTimestamp = currentTimestamp;
        while (_currentTimestamp == lastTimestamp) {
            _currentTimestamp = timestamp();
        }
        return _currentTimestamp;
    }

    private long createNodeId() {
        long nodeId;
        try {
            StringBuilder sb = new StringBuilder();
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                byte[] mac = networkInterface.getHardwareAddress();
                if (mac != null) {
                    for (byte macPort : mac) {
                        sb.append(String.format("%02X", macPort));
                    }
                }
            }
            nodeId = new SecureRandom().nextInt() + sb.toString().hashCode();
        } catch (Exception e) {
            nodeId = new SecureRandom().nextInt();
        }
        return nodeId & maxNodeId;
    }

    public long[] parse(long id) {
        long maskNodeId = ((1L << NODE_ID_BITS) - 1) << SEQUENCE_BITS;
        long maskSequence = (1L << SEQUENCE_BITS) - 1;

        long timestamp = (id >> (NODE_ID_BITS + SEQUENCE_BITS)) + customEpoch;
        long nodeId = (id & maskNodeId) >> SEQUENCE_BITS;
        long sequence = id & maskSequence;

        return new long[]{timestamp, nodeId, sequence};
    }

    @Override
    public String toString() {
        return String.format(
                "Snowflake Settings [GROUP=%s, EPOCH_BITS=%s, NODE_ID_BITS=%s, SEQUENCE_BITS=%s, CUSTOM_EPOCH=%s, NodeId=%s]",
                group, EPOCH_BITS, NODE_ID_BITS, SEQUENCE_BITS, customEpoch, nodeId
        );
    }
}
