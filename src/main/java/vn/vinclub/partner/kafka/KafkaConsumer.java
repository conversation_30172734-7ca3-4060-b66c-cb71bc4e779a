package vn.vinclub.partner.kafka;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.entity.PartnerUserMapping;
import vn.vinclub.partner.domain.event.historical.CustomerHistoricalEventData;
import vn.vinclub.partner.domain.event.historical.HistoricalEvent;
import vn.vinclub.partner.domain.event.internal.PartnerPointTxnRequestEvent;
import vn.vinclub.partner.exception.IgnoreProcessingException;
import vn.vinclub.partner.service.InternalPartnerService;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaConsumer {

    private final BaseJsonUtils jsonUtils;
    private final InternalPartnerService internalPartnerService;

    @KafkaListener(topics = "${kafka.historical.vclub_customer.topic.name}",
            groupId = "${kafka.historical.vclub_customer.group.name}",
            containerFactory = "kafkaIntListenerContainerFactory",
            autoStartup = "${kafka.historical.vclub_customer.enabled:true}"
    )
    public void consumeVClubCustomerHistorical(ConsumerRecord<String, String> record, Acknowledgment ack) {
        String message = record.value();

        try (Profiler p = new Profiler(getClass(), "consumeVclubCustomerHistorical")) {
            final HistoricalEvent<CustomerHistoricalEventData> historicalEvent = jsonUtils.toObjectOrThrow(message, new TypeReference<HistoricalEvent<CustomerHistoricalEventData>>() {
            });

            // handle customer historical event
            internalPartnerService.handleVClubCustomerHistoricalEvent(historicalEvent);

            ack.acknowledge();
        } catch (Exception ex) {
            log.error("[Kafka] process message error {} - topic={}, partition={}, Offset={}", ex.getMessage(), record.topic(), record.partition(), record.offset(), ex);
        }
    }

    @KafkaListener(topics = "${kafka.historical.partner_user_mapping.topic.name}",
            groupId = "${kafka.historical.partner_user_mapping.group.name}",
            containerFactory = "kafkaIntListenerContainerFactory",
            autoStartup = "${kafka.historical.partner_user_mapping.enabled:true}"
    )
    public void consumePartnerUserMappingHistorical(ConsumerRecord<String, String> record, Acknowledgment ack) {
        String message = record.value();

        try (Profiler p = new Profiler(getClass(), "consumePartnerUserMappingHistorical")) {
            final HistoricalEvent<PartnerUserMapping> historicalEvent = jsonUtils.toObjectOrThrow(message, new TypeReference<HistoricalEvent<PartnerUserMapping>>() {
            });

            // handle partner user mapping historical event
            internalPartnerService.handlePartnerUserMappingHistoricalEvent(historicalEvent);

            ack.acknowledge();
        } catch (Exception ex) {
            log.error("[Kafka] process message error {} - topic={}, partition={}, Offset={}", ex.getMessage(), record.topic(), record.partition(), record.offset(), ex);
        }
    }

    @KafkaListener(topics = "${kafka.historical.partner_point_transaction_history.topic.name}",
            groupId = "${kafka.historical.partner_point_transaction_history.group.name}",
            containerFactory = "kafkaIntListenerContainerFactory",
            autoStartup = "${kafka.historical.partner_point_transaction_history.enabled:true}"
    )
    public void consumePartnerPointTransactionHistorical(ConsumerRecord<String, String> record, Acknowledgment ack) {
        String message = record.value();

        try (Profiler p = new Profiler(getClass(), "consumePartnerPointTransactionHistorical")) {
            final HistoricalEvent<PartnerPointTransactionHistory> historicalEvent = jsonUtils.toObjectOrThrow(message, 
                    new TypeReference<HistoricalEvent<PartnerPointTransactionHistory>>() {});

            // Handle transaction historical event
            internalPartnerService.handlePartnerPointTransactionHistoricalEvent(historicalEvent);

            ack.acknowledge();
        } catch (Exception ex) {
            log.error("[Kafka] process message error {} - topic={}, partition={}, Offset={}", ex.getMessage(), record.topic(), record.partition(), record.offset(), ex);
        }
    }

    @KafkaListener(topics = "${kafka.partner_point_txn_request.topic.name}",
            groupId = "${kafka.partner_point_txn_request.group.name}",
            containerFactory = "kafkaIntListenerContainerFactory",
            autoStartup = "${kafka.partner_point_txn_request.enabled:true}"
    )
    public void consumePartnerPointTxnRequestEvent(ConsumerRecord<String, String> record, Acknowledgment ack) {
        try (Profiler p = new Profiler(getClass(), "consumeVoucherPurchaseRequestEvent")) {
            log.info("[Kafka] Consume message - topic={} - partition={}, Offset={} ", record.topic(), record.partition(), record.offset());
            String message = record.value();

            var event = jsonUtils.toObjectOrThrow(message, PartnerPointTxnRequestEvent.class);

            internalPartnerService.handlePartnerPointTxnRequestEvent(event);

            ack.acknowledge();
            log.info("[Kafka] Committed message - topic={}, partition={}, Offset={} ", record.topic(), record.partition(), record.offset());
        } catch (IgnoreProcessingException ex) {
            ack.acknowledge();
            log.warn("[Kafka] Ignore message - topic={}, partition={}, Offset={} - {}", record.topic(), record.partition(), record.offset(), ex.getMessage());
        } catch (Exception ex) {
            log.error("[Kafka] process message error {} - topic={}, partition={}, Offset={}", ex.getMessage(), record.topic(), record.partition(), record.offset(), ex);
        }
    }
}
