package vn.vinclub.partner.kafka;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import vn.vinclub.common.model.Profiler;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaProducer {

    private final KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(String topic, String key, String message) throws ExecutionException, InterruptedException {

        try (Profiler p = new Profiler(getClass(), "sendMessage")) {

            CompletableFuture<SendResult<String, String>> resultFuture = kafkaTemplate.send(topic, key, message);

            SendResult<String, String> sendResult = resultFuture.get();

            log.debug(
                    "sendMessage({}) success: topic {}, partition {}, offset {}",
                    message,
                    sendResult.getRecordMetadata().topic(),
                    sendResult.getRecordMetadata().partition(),
                    sendResult.getRecordMetadata().offset()
            );
        } catch (Exception ex) {
            log.error("Failed when sendMessage({}, {}, {}): {}", topic, key, message, ex.getMessage(), ex);
            throw ex;
        }
    }

}
