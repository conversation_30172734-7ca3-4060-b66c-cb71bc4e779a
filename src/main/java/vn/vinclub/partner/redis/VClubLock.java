package vn.vinclub.partner.redis;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import vn.vinclub.partner.constant.AppConst;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 11/29/24 17:20
 */
@Slf4j
public class VClubLock implements AutoCloseable {

    private final RLock transLock;

    public VClubLock(RedissonClient redisson, String lockName) {
        this(redisson.getLock(lockName));
    }

    public VClubLock(RLock transLock) {
        this(transLock, AppConst.REDIS_LOCK.WAIT_TIME_IN_MS, AppConst.REDIS_LOCK.LEASE_TIME_IN_MS, TimeUnit.MILLISECONDS);
    }

    public VClubLock(RLock transLock, long waitTime, long leaseTime, TimeUnit unit) {
        this.transLock = transLock;
        try {
            if (!this.transLock.tryLock(waitTime, leaseTime, unit)) {
                log.warn("Could not acquire lock {}", transLock.getName());
                throw new RuntimeException("Lock failed to lock");
            }
        } catch (InterruptedException e) {
            log.error("Failed to lock {}", transLock.getName());
            throw new RuntimeException(e);
        }
    }

    @Override
    public void close() {
        if (this.transLock != null && this.transLock.isLocked() && this.transLock.isHeldByCurrentThread()) {
            this.transLock.unlock();
        }
    }
}
