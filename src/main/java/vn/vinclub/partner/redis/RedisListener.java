package vn.vinclub.partner.redis;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.stereotype.Component;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.domain.dto.ChangeMsgDto;
import vn.vinclub.partner.service.PartnerModuleService;
import vn.vinclub.partner.service.PartnerPointConfigService;
import vn.vinclub.partner.service.PartnerService;

@Component
@Slf4j
@ReadingConverter
@RequiredArgsConstructor
public class RedisListener {

    private final PartnerService partnerService;

    private final PartnerModuleService partnerModuleService;

    private final PartnerPointConfigService partnerPointConfigService;

    @Value("${redis.channel.change}")
    private String channel;

    private final RedissonClient redissonClient;

    private final BaseJsonUtils baseJsonUtils;

    @PostConstruct
    public void init() {
        redissonClient.getTopic(channel).addListener(String.class, (channel, msg) -> {
            var data = baseJsonUtils.toObject(msg, ChangeMsgDto.class);
            String key = data.getMsgKey();
            Long id = data.getId();
            if (AppConst.RedisMessage.CHANGE_PARTNER.equalsIgnoreCase(key)) {
                String code = data.getCode();
                partnerService.invalidateCache(id, code);
            } else if (AppConst.RedisMessage.CHANGE_PARTNER_MODULE.equalsIgnoreCase(key)) {
                partnerModuleService.invalidateCache(id);
            } else if (AppConst.RedisMessage.CHANGE_PARTNER_POINT_CONFIG.equalsIgnoreCase(key)) {
                partnerPointConfigService.invalidateCache(id);
            }
        });
    }
}
