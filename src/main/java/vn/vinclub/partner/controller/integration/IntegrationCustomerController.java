package vn.vinclub.partner.controller.integration;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.annotation.ExternalActivityLog;
import vn.vinclub.partner.domain.dto.external.customer.UnlinkAccountRequestDto;
import vn.vinclub.partner.domain.dto.external.customer.VClubCustomerBalanceDto;
import vn.vinclub.partner.domain.dto.external.customer.VClubCustomerProfileDto;
import vn.vinclub.partner.security.impl.IntegrationAuthenticationFacadeImpl;
import vn.vinclub.partner.service.IntegrationCustomerService;
import vn.vinclub.partner.util.ServiceResponse;

@RestController
@RequestMapping("/integration/v1/customer")
@RequiredArgsConstructor
public class IntegrationCustomerController {
    private final IntegrationAuthenticationFacadeImpl auth;
    private final IntegrationCustomerService service;

    @Profiler
    @ExternalActivityLog(action = "GET_PROFILE", object = "CUSTOMER", objectId = "#partnerUserId")
    @GetMapping("/profile")
    public ServiceResponse<VClubCustomerProfileDto> getCustomerProfile(@RequestParam("partnerUserId") String partnerUserId) {
        return ServiceResponse.success(service.getCustomerProfile(auth.getPartnerCode(), partnerUserId));
    }

    @Profiler
    @ExternalActivityLog(action = "GET_POINT_BALANCE", object = "CUSTOMER", objectId = "#partnerUserId")
    @GetMapping("/point")
    public ServiceResponse<VClubCustomerBalanceDto> getCustomerPointBalance(@RequestParam("partnerUserId") String partnerUserId) {
        return ServiceResponse.success(service.getCustomerPointBalance(auth.getPartnerCode(), partnerUserId));
    }

    @Profiler
    @ExternalActivityLog(action = "UNLINK_ACCOUNT", object = "CUSTOMER", objectId = "#request.partnerUserId")
    @PostMapping("/unlink-account")
    public ServiceResponse<Boolean> unlinkAccount(@RequestBody @Valid UnlinkAccountRequestDto request) {
        return ServiceResponse.success(service.unlinkAccount(auth.getPartnerCode(), request));
    }
}
