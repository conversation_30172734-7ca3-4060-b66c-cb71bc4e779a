package vn.vinclub.partner.controller.integration;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.annotation.ExternalActivityLog;
import vn.vinclub.partner.client.request.AuthenticationWithPasswordRequest;
import vn.vinclub.partner.client.request.AuthenticationWithRefreshTokenRequest;
import vn.vinclub.partner.client.response.AuthenticationResponse;
import vn.vinclub.partner.service.impl.IntegrationAuthServiceImpl;
import vn.vinclub.partner.util.ServiceResponse;

@RestController
@RequestMapping("/integration/v1/auth")
@RequiredArgsConstructor
public class IntegrationAuthController {

    public final IntegrationAuthServiceImpl authService;

    @Profiler
    @ExternalActivityLog(action = "ISSUE_TOKEN", object = "AUTHENTICATION", objectId = "#authenticationRequest.username")
    @PostMapping("/token")
    public ServiceResponse<AuthenticationResponse> authenticate(@RequestBody AuthenticationWithPasswordRequest authenticationRequest) {
        return ServiceResponse.success(authService.authenticate(authenticationRequest.toAuthenticationRequest()));
    }

    @Profiler
    @ExternalActivityLog(action = "REFRESH_TOKEN", object = "AUTHENTICATION")
    @PostMapping("/refresh-token")
    public ServiceResponse<AuthenticationResponse> authenticate(@RequestBody AuthenticationWithRefreshTokenRequest authenticationRequest) {
        return ServiceResponse.success(authService.authenticate(authenticationRequest.toAuthenticationRequest()));
    }

}
