package vn.vinclub.partner.controller.integration;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.annotation.ExternalActivityLog;
import vn.vinclub.partner.domain.dto.external.point.SpendVinClubPointRequest;
import vn.vinclub.partner.domain.dto.external.point.TopUpVinClubPointRequest;
import vn.vinclub.partner.domain.dto.external.point.VinClubPointTxnResponse;
import vn.vinclub.partner.security.impl.IntegrationAuthenticationFacadeImpl;
import vn.vinclub.partner.service.IntegrationPointService;
import vn.vinclub.partner.util.ServiceResponse;

@RestController
@RequestMapping("/integration/v1/point")
@RequiredArgsConstructor
public class IntegrationPointController {

    private final IntegrationAuthenticationFacadeImpl authenticationFacade;
    private final IntegrationPointService integrationPointService;

    // region: point transaction
    @Profiler
    @ExternalActivityLog(action = "LOOKUP_TRANSACTION", object = "POINT", objectId = "#partnerTransactionId")
    @GetMapping("/history")
    public ServiceResponse<VinClubPointTxnResponse> checkTransactionConfig(@RequestParam("partnerUserId") String partnerUserId, @RequestParam("partnerTransactionId") String partnerTransactionId) {
        var partnerCode = authenticationFacade.getPartnerCode();
        var response = integrationPointService.lookupTransaction(partnerCode, partnerTransactionId, partnerUserId);
        return ServiceResponse.success(response);
    }

    @Profiler
    @ExternalActivityLog(action = "SPEND_POINT", object = "POINT", objectId = "#request.partnerTransactionId")
    @PostMapping("/spend-point")
    public ServiceResponse<VinClubPointTxnResponse> spendPoint(@RequestBody @Valid SpendVinClubPointRequest request) {
        var partnerCode = authenticationFacade.getPartnerCode();
        request.setPartnerCode(partnerCode);

        var response = integrationPointService.spendPoint(request);
        return ServiceResponse.success(response);
    }


    @Profiler
    @ExternalActivityLog(action = "TOPUP_POINT", object = "POINT", objectId = "#request.partnerTransactionId")
    @PostMapping("/topup-point")
    public ServiceResponse<VinClubPointTxnResponse> topUpPoint(@RequestBody @Valid TopUpVinClubPointRequest request) {
        var partnerCode = authenticationFacade.getPartnerCode();
        request.setPartnerCode(partnerCode);

        var response = integrationPointService.topUpPoint(request);
        return ServiceResponse.success(response);
    }
    // endregion

}
