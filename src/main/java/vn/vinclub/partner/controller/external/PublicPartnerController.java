package vn.vinclub.partner.controller.external;

import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.annotation.ExternalActivityLog;
import vn.vinclub.partner.domain.dto.HandleRedirectResult;
import vn.vinclub.partner.domain.enums.ModuleType;
import vn.vinclub.partner.service.PublicPartnerService;
import vn.vinclub.partner.util.RedirectUriHelper;

import java.net.URI;
import java.util.Map;

@RestController
@RequestMapping("/external")
@RequiredArgsConstructor
public class PublicPartnerController {

    private final PublicPartnerService publicPartnerService;
    private final RedirectUriHelper redirectUriHelper;

    @Profiler
    @GetMapping("/redirect/partner/{partnerCode}/module/{module}")
    @ExternalActivityLog(action = "HANDLE_REDIRECT", object = "PARTNER", objectId = "#partnerCode", externalSystem = "VCLUB_EXTERNAL_API")
    public ResponseEntity<?> handlePartnerModuleCallback(@PathVariable String partnerCode,
                                                         @PathVariable String module,
                                                         @RequestParam Map<String, String> params) {

        HandleRedirectResult result = publicPartnerService.handleRedirect(partnerCode.toUpperCase(), ModuleType.fromValue(module), params);

        String redirectUrl = redirectUriHelper.generateAppDeepLink(result);

        return ResponseEntity.status(HttpStatus.FOUND) // HTTP 302 (Redirect)
                .location(URI.create(redirectUrl))
                .build();
    }
}
