package vn.vinclub.partner.controller.admin;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.domain.dto.module.PartnerModuleCreateDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleFilterDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleUpdateDto;
import vn.vinclub.partner.service.AdminPartnerModuleService;
import vn.vinclub.partner.util.ServiceResponse;

@RestController
@RequestMapping("/admin/partners/{partnerId}/modules")
@RequiredArgsConstructor
public class AdminPartnerModuleController {

    private final AdminPartnerModuleService adminPartnerModuleService;

    @Profiler
    @GetMapping()
    public ServiceResponse<Page<PartnerModuleDto>> searchPartnerModules(@PathVariable Long partnerId, PartnerModuleFilterDto filter, Pageable pageable) {
        filter.setPartnerId(partnerId);
        Page<PartnerModuleDto> results = adminPartnerModuleService.searchPartnerModules(filter, pageable);
        return ServiceResponse.success(results);
    }

    @Profiler
    @PostMapping
    public ServiceResponse<PartnerModuleDto> createPartnerModule(@PathVariable Long partnerId, @RequestBody PartnerModuleCreateDto createDto) {
        createDto.setPartnerId(partnerId);
        PartnerModuleDto partnerModuleDto = adminPartnerModuleService.createPartnerModule(createDto);
        return ServiceResponse.success(partnerModuleDto);
    }

    @Profiler
    @GetMapping("/{id}")
    public ServiceResponse<PartnerModuleDto> getPartnerModuleDetails(@PathVariable Long partnerId, @PathVariable Long id) {
        PartnerModuleDto partnerModuleDto = adminPartnerModuleService.getPartnerModuleDetails(partnerId, id);
        return ServiceResponse.success(partnerModuleDto);
    }

    @Profiler
    @PutMapping("/{id}")
    public ServiceResponse<PartnerModuleDto> updatePartnerModule(@PathVariable Long partnerId, @PathVariable Long id, @RequestBody PartnerModuleUpdateDto updateDto) {
        PartnerModuleDto updatedPartnerModule = adminPartnerModuleService.updatePartnerModule(partnerId, id, updateDto);
        return ServiceResponse.success(updatedPartnerModule);
    }

    @Profiler
    @DeleteMapping("/{id}")
    public ServiceResponse<Long> deletePartnerModule(@PathVariable Long partnerId, @PathVariable Long id) {
        adminPartnerModuleService.deletePartnerModule(partnerId, id);
        return ServiceResponse.success(id);
    }
}