package vn.vinclub.partner.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigCreateDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigUpdateDto;
import vn.vinclub.partner.service.AdminPartnerPointConfigService;
import vn.vinclub.partner.util.ServiceResponse;

@RestController
@RequestMapping("/admin/partners/{partnerId}/point-configs")
@RequiredArgsConstructor
public class AdminPartnerPointConfigController {

    private final AdminPartnerPointConfigService adminPartnerPointConfigService;

    @Profiler
    @GetMapping()
    public ServiceResponse<Page<PartnerPointConfigDto>> searchPartnerPointConfigs(@PathVariable Long partnerId, PartnerPointConfigFilterDto filter, Pageable pageable) {
        filter.setPartnerId(partnerId);
        Page<PartnerPointConfigDto> results = adminPartnerPointConfigService.searchPartnerPointConfigs(filter, pageable);
        return ServiceResponse.success(results);
    }

    @Profiler
    @PostMapping
    public ServiceResponse<PartnerPointConfigDto> createPartnerPointConfig(@PathVariable Long partnerId, @RequestBody @Valid PartnerPointConfigCreateDto createDto) {
        createDto.setPartnerId(partnerId);
        PartnerPointConfigDto partnerPointConfigDto = adminPartnerPointConfigService.createPartnerPointConfig(createDto);
        return ServiceResponse.success(partnerPointConfigDto);
    }

    @Profiler
    @GetMapping("/{id}")
    public ServiceResponse<PartnerPointConfigDto> getPartnerPointConfigDetails(@PathVariable Long partnerId, @PathVariable Long id) {
        PartnerPointConfigDto partnerPointConfigDto = adminPartnerPointConfigService.getPartnerPointConfigDetails(partnerId, id);
        return ServiceResponse.success(partnerPointConfigDto);
    }

    @Profiler
    @GetMapping("/code/{code}")
    public ServiceResponse<PartnerPointConfigDto> getPartnerPointConfigByCode(@PathVariable Long partnerId, @PathVariable String code) {
        PartnerPointConfigDto partnerPointConfigDto = adminPartnerPointConfigService.getPartnerPointConfigByCode(partnerId, code);
        return ServiceResponse.success(partnerPointConfigDto);
    }

    @Profiler
    @PutMapping("/{id}")
    public ServiceResponse<PartnerPointConfigDto> updatePartnerPointConfig(@PathVariable Long partnerId, @PathVariable Long id, @RequestBody @Valid PartnerPointConfigUpdateDto updateDto) {
        PartnerPointConfigDto updatedPartnerPointConfig = adminPartnerPointConfigService.updatePartnerPointConfig(partnerId, id, updateDto);
        return ServiceResponse.success(updatedPartnerPointConfig);
    }

    @Profiler
    @DeleteMapping("/{id}")
    public ServiceResponse<Long> deletePartnerPointConfig(@PathVariable Long partnerId, @PathVariable Long id) {
        adminPartnerPointConfigService.deletePartnerPointConfig(partnerId, id);
        return ServiceResponse.success(id);
    }
}