package vn.vinclub.partner.controller.admin;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.domain.dto.partner.PartnerCreateDto;
import vn.vinclub.partner.domain.dto.partner.PartnerDto;
import vn.vinclub.partner.domain.dto.partner.PartnerFilterDto;
import vn.vinclub.partner.domain.dto.partner.PartnerUpdateDto;
import vn.vinclub.partner.service.AdminPartnerService;
import vn.vinclub.partner.util.ServiceResponse;

@RestController
@RequestMapping("/admin/partners")
@RequiredArgsConstructor
public class AdminPartnerController {

    private final AdminPartnerService adminPartnerService;

    @Profiler
    @GetMapping()
    public ServiceResponse<Page<PartnerDto>> searchPartners(PartnerFilterDto filter, Pageable pageable) {
        Page<PartnerDto> results = adminPartnerService.searchPartners(filter, pageable);
        return ServiceResponse.success(results);
    }

    @Profiler
    @PostMapping
    public ServiceResponse<PartnerDto> registerPartner(@RequestBody PartnerCreateDto createDto) {
        PartnerDto partnerDto = adminPartnerService.registerPartner(createDto);
        return ServiceResponse.success(partnerDto);
    }

    @Profiler
    @GetMapping("/{id}")
    public ServiceResponse<PartnerDto> getPartnerDetails(@PathVariable Long id) {
        PartnerDto partnerDto = adminPartnerService.getPartnerDetails(id);
        return ServiceResponse.success(partnerDto);
    }

    @Profiler
    @PutMapping("/{id}")
    public ServiceResponse<PartnerDto> updatePartner(@PathVariable Long id, @RequestBody PartnerUpdateDto updateDto) {
        PartnerDto updatedPartner = adminPartnerService.updatePartner(id, updateDto);
        return ServiceResponse.success(updatedPartner);
    }

    @Profiler
    @DeleteMapping("/{id}")
    public ServiceResponse<Long> deletePartner(@PathVariable Long id) {
        adminPartnerService.deletePartner(id);
        return ServiceResponse.success(id);
    }
}
