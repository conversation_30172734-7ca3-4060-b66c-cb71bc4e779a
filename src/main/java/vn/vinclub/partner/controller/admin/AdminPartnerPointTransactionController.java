package vn.vinclub.partner.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.domain.dto.point.PartnerPointTransactionHistoryDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnRollbackDto;
import vn.vinclub.partner.service.AdminPartnerPointTxnService;
import vn.vinclub.partner.util.ServiceResponse;

@RestController
@RequestMapping("/admin/partner-point-transactions")
@RequiredArgsConstructor
public class AdminPartnerPointTransactionController {

    private final AdminPartnerPointTxnService adminPartnerPointTxnService;

    @Profiler
    @GetMapping()
    public ServiceResponse<Page<PartnerPointTransactionHistoryDto>> searchPartnerPointConfigs(PartnerPointTxnFilterDto filter, Pageable pageable) {
        Page<PartnerPointTransactionHistoryDto> results = adminPartnerPointTxnService.searchPartnerPointTransactions(filter, pageable);
        return ServiceResponse.success(results);
    }

    @Profiler
    @GetMapping("/{id}")
    public ServiceResponse<PartnerPointTransactionHistoryDto> getPartnerPointTransactionHistory(@PathVariable Long id) {
        var response = adminPartnerPointTxnService.getPartnerPointTransactionHistory(id);
        return ServiceResponse.success(response);
    }

    @Profiler
    @PutMapping("/{id}/rollback")
    public ServiceResponse<PartnerPointTransactionHistoryDto> rollbackPartnerPointTransaction(@PathVariable Long id, @RequestBody @Valid PartnerPointTxnRollbackDto rollbackDto) {
        var response = adminPartnerPointTxnService.rollbackPartnerPointTransaction(id, rollbackDto);
        return ServiceResponse.success(response);
    }
}