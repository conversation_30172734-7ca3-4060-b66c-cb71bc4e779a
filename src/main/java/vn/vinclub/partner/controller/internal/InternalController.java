package vn.vinclub.partner.controller.internal;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.domain.dto.UserQuotaDto;
import vn.vinclub.partner.domain.dto.partner.PartnerFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.dto.request.TopUpPartnerPointIntRequest;
import vn.vinclub.partner.domain.dto.response.LinkedAccountIntResponse;
import vn.vinclub.partner.domain.dto.response.PartnerIntResponse;
import vn.vinclub.partner.domain.dto.response.PartnerPointTxnHistoryIntResponse;
import vn.vinclub.partner.service.InternalPartnerService;
import vn.vinclub.partner.util.ServiceResponse;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/internal")
@RequiredArgsConstructor
public class InternalController {
    private final InternalPartnerService internalPartnerService;

    @Profiler
    @GetMapping("/partners")
    public ServiceResponse<Page<PartnerIntResponse>> getPartners(PartnerFilterDto filter, Pageable pageable) {
        var partners = internalPartnerService.filterPartners(filter, pageable);
        return ServiceResponse.success(partners);
    }

    @Profiler
    @GetMapping("/partners/{partnerId}")
    public ServiceResponse<PartnerIntResponse> getPartnerById(@PathVariable String partnerId) {
        var partner = internalPartnerService.getPartnerById(partnerId);
        return ServiceResponse.success(partner);
    }

    @Profiler
    @GetMapping("/partners/mget")
    public ServiceResponse<Map<Long, PartnerIntResponse>> multiGetPartners(@RequestParam(required = false, defaultValue = "false") Boolean includeDisabled,
                                              @RequestParam List<Long> partnerIds) {
        var partners = internalPartnerService.multiGetPartners(partnerIds, includeDisabled);
        return ServiceResponse.success(partners);
    }

    @Profiler
    @GetMapping("/partners/mget_by_code")
    public ServiceResponse<Map<String, PartnerIntResponse>> multiGetPartnersByCode(@RequestParam(required = false, defaultValue = "false") Boolean includeDisabled,
                                                                           @RequestParam List<String> partnerCodes) {
        var partners = internalPartnerService.multiGetPartnersByCode(partnerCodes, includeDisabled);
        return ServiceResponse.success(partners);
    }

    @Profiler
    @GetMapping("/partners/{partnerId}/linked-accounts/{vclubUserId}")
    public ServiceResponse<LinkedAccountIntResponse> getPartnerLinkedAccount(@PathVariable Long partnerId, @PathVariable Long vclubUserId) {
        var linkedAccount = internalPartnerService.getLinkedAccount(partnerId, vclubUserId);
        return ServiceResponse.success(linkedAccount);
    }

    @Profiler
    @PostMapping("/partners/{partnerId}/point/topup")
    public ServiceResponse<PartnerPointTxnHistoryIntResponse> topUpPoint(@PathVariable Long partnerId, @RequestBody @Valid TopUpPartnerPointIntRequest request) {
        request.setPartnerId(partnerId);

        var response = internalPartnerService.topUpPoint(request);

        return ServiceResponse.success(response);
    }

    @Profiler
    @GetMapping("/partners/{partnerId}/point/quota/{vclubUserId}")
    public ServiceResponse<Map<String, UserQuotaDto>> getPartnerPointQuota(@PathVariable Long partnerId, @PathVariable Long vclubUserId) {
        var response = internalPartnerService.getPartnerPointQuota(partnerId, vclubUserId);
        return ServiceResponse.success(response);
    }

    @Profiler
    @GetMapping("/partner-point-transactions")
    public ServiceResponse<Page<PartnerPointTxnHistoryIntResponse>> filterPartnerPointTransactionHistory(PartnerPointTxnFilterDto filter, Pageable pageable) {
        var response = internalPartnerService.filterPartnerPointTransactionHistory(filter, pageable);
        return ServiceResponse.success(response);
    }

    @Profiler
    @GetMapping("/partner-point-transactions/{transactionId}")
    public ServiceResponse<PartnerPointTxnHistoryIntResponse> getPartnerPointTransactionHistory(@PathVariable String transactionId) {
        var response = internalPartnerService.getPartnerPointTransactionHistory(transactionId);
        return ServiceResponse.success(response);
    }
}
