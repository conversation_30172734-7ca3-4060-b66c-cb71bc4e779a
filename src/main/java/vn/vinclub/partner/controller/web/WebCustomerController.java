package vn.vinclub.partner.controller.web;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.annotation.ExternalActivityLog;
import vn.vinclub.partner.domain.dto.request.PartnerSignedTokenRequest;
import vn.vinclub.partner.domain.dto.response.LinkedAccountResponse;
import vn.vinclub.partner.security.impl.VClubCustomerAuthenticationFacadeImpl;
import vn.vinclub.partner.service.WebCustomerService;
import vn.vinclub.partner.util.ServiceResponse;

@RestController
@RequestMapping("/customer")
@RequiredArgsConstructor
public class WebCustomerController {

    private final VClubCustomerAuthenticationFacadeImpl authenticationFacade;
    private final WebCustomerService webCustomerService;

    @Profiler
    @PostMapping("/exchange-partner-token")
    @ExternalActivityLog(action = "EXCHANGE_PARTNER_TOKEN", object = "PARTNER", objectId = "#partnerSignedTokenRequest.partnerCode", externalSystem = "VCLUB_EXTERNAL_WEB")
    public ServiceResponse<?> exchangePartnerToken(@RequestBody PartnerSignedTokenRequest partnerSignedTokenRequest) {
        var token = webCustomerService.exchangePartnerToken(partnerSignedTokenRequest);
        return ServiceResponse.success(token);
    }

    @Profiler
    @GetMapping("/linked-account")
    @ExternalActivityLog(action = "GET_LINKED_ACCOUNT", object = "PARTNER", objectId = "#partnerCode", externalSystem = "VCLUB_EXTERNAL_WEB")
    public ServiceResponse<LinkedAccountResponse> getLinkedAccount(@RequestParam("partner_code") String partnerCode) {
        var vclubUserId = authenticationFacade.getVclubUserId();
        var linkedAccount = webCustomerService.getLinkedAccount(partnerCode, vclubUserId);
        return ServiceResponse.success(linkedAccount);
    }

    @Profiler
    @PostMapping("/link-account")
    @ExternalActivityLog(action = "LINK_ACCOUNT", object = "PARTNER", objectId = "#partnerSignedTokenRequest.partnerCode", externalSystem = "VCLUB_EXTERNAL_WEB")
    public ServiceResponse<LinkedAccountResponse> linkAccount(@RequestBody PartnerSignedTokenRequest partnerSignedTokenRequest) {
        var vclubUserId = authenticationFacade.getVclubUserId();
        var linkedAccount = webCustomerService.linkAccount(vclubUserId, partnerSignedTokenRequest);
        return ServiceResponse.success(linkedAccount);
    }
}
