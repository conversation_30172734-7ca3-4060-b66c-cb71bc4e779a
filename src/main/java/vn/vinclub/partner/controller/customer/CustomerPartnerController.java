package vn.vinclub.partner.controller.customer;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.domain.dto.request.LinkAccountRequest;
import vn.vinclub.partner.domain.dto.response.LinkAccountResponse;
import vn.vinclub.partner.domain.dto.response.LinkablePartnerResponse;
import vn.vinclub.partner.domain.dto.response.LinkedAccountResponse;
import vn.vinclub.partner.domain.dto.response.PartnerPointTxnHistoryResponse;
import vn.vinclub.partner.security.impl.VClubCustomerAuthenticationFacadeImpl;
import vn.vinclub.partner.service.CustomerPartnerService;
import vn.vinclub.partner.util.ServiceResponse;

import java.util.List;

@RestController
@RequestMapping("/customer/partner")
@RequiredArgsConstructor
public class CustomerPartnerController {

    private final VClubCustomerAuthenticationFacadeImpl authenticationFacade;
    private final CustomerPartnerService customerPartnerService;


    // region: link account
    @Profiler
    @GetMapping("/linkable-partners")
    public ServiceResponse<List<LinkablePartnerResponse>> getLinkablePartners() {
        var vclubUserId = authenticationFacade.getVclubUserId();
        var partnerLinkAccounts = customerPartnerService.getLinkablePartner(vclubUserId);
        return ServiceResponse.success(partnerLinkAccounts);
    }

    @Profiler
    @GetMapping("/code/{partnerCode}/linked-account")
    public ServiceResponse<LinkedAccountResponse> getPartnerLinkedAccount(@PathVariable String partnerCode) {
        var vclubUserId = authenticationFacade.getVclubUserId();
        var linkedAccount = customerPartnerService.getLinkedAccount(partnerCode.toUpperCase(), vclubUserId);
        return ServiceResponse.success(linkedAccount);
    }

    @Profiler
    @PostMapping("/code/{partnerCode}/module/link-account")
    public ServiceResponse<LinkAccountResponse> processLinkAccountModule(@PathVariable String partnerCode, @Valid @RequestBody LinkAccountRequest request) {
        var vclubUserId = authenticationFacade.getVclubUserId();
        request.setVclubUserId(vclubUserId);
        request.setPartnerCode(partnerCode.toUpperCase());
        var response = customerPartnerService.processLinkAccountModule(request);
        return ServiceResponse.success(response);
    }

    @Profiler
    @PostMapping("/code/{partnerCode}/unlink-account")
    public ServiceResponse<Boolean> unlinkPartnerAccount(@PathVariable String partnerCode) {
        var vclubUserId = authenticationFacade.getVclubUserId();
        customerPartnerService.unlinkAccount(partnerCode.toUpperCase(), vclubUserId);
        return ServiceResponse.success(true);
    }

    // end region

    // region: point transaction
    @Profiler
    @GetMapping("/point/transactions/{transactionId}")
    public ServiceResponse<PartnerPointTxnHistoryResponse> getPartnerPointTransactions(@PathVariable String transactionId) {
        var vclubUserId = authenticationFacade.getVclubUserId();
        var transactions = customerPartnerService.getPartnerPointTransactions(vclubUserId, transactionId);
        return ServiceResponse.success(transactions);
    }
    // end region

}
