package vn.vinclub.partner.module.account.registry;

import org.springframework.stereotype.Component;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.module.account.service.PartnerLinkAccountService;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class PartnerLinkAccountServiceRegistry {
    private final Map<String, PartnerLinkAccountService> svcMap;

    public PartnerLinkAccountServiceRegistry(List<PartnerLinkAccountService> services) {
        this.svcMap = services.stream()
                .collect(Collectors.toMap(PartnerLinkAccountService::getPartnerCode, s -> s));
    }

    public PartnerLinkAccountService getService(String partnerCode) {
        return Optional.ofNullable(svcMap.get(partnerCode))
                .orElseThrow(() -> new BusinessLogicException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION));
    }
}
