package vn.vinclub.partner.module.account.service.impl;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.client.InternalCoreClient;
import vn.vinclub.partner.client.SkyJoyClient;
import vn.vinclub.partner.client.request.SkyJoyExchangeTokenRequest;
import vn.vinclub.partner.client.request.SkyJoyLinkAccountByOauthRequest;
import vn.vinclub.partner.client.request.SkyJoyLinkAccountRequest;
import vn.vinclub.partner.client.request.SkyJoyUnLinkAccountRequest;
import vn.vinclub.partner.client.response.SkyJoyLinkAccountResponse;
import vn.vinclub.partner.config.PartnerConfig;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.external.customer.VClubUserIdentityDto;
import vn.vinclub.partner.domain.dto.mapping.PartnerUserMappingCreateDto;
import vn.vinclub.partner.domain.dto.mapping.PartnerUserMappingUpdateDto;
import vn.vinclub.partner.domain.dto.module.config.LinkAccountConfig;
import vn.vinclub.partner.domain.dto.request.LinkAccountRequest;
import vn.vinclub.partner.domain.dto.response.LinkAccountResponse;
import vn.vinclub.partner.domain.entity.Partner;
import vn.vinclub.partner.domain.enums.LinkAccountFlowType;
import vn.vinclub.partner.domain.enums.LinkAccountStep;
import vn.vinclub.partner.domain.enums.ModuleType;
import vn.vinclub.partner.domain.mapper.PartnerUserMappingMapper;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.module.account.service.PartnerLinkAccountService;
import vn.vinclub.partner.redis.VClubLock;
import vn.vinclub.partner.service.PartnerModuleService;
import vn.vinclub.partner.service.PartnerService;
import vn.vinclub.partner.service.PartnerUserMappingService;
import vn.vinclub.partner.util.I18nUtil;
import vn.vinclub.partner.util.RedirectUriHelper;

import java.net.URI;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class SkyJoyLinkAccountService implements PartnerLinkAccountService {
    private final PartnerService partnerService;
    private final PartnerModuleService partnerModuleService;
    private final PartnerUserMappingService partnerUserMappingService;

    private final InternalCoreClient internalCoreClient;
    private final SkyJoyClient skyJoyClient;
    private final RedirectUriHelper redirectUriHelper;
    private final PartnerConfig.SkyJoy config;
    private final BaseJsonUtils jsonUtils;
    private final RedissonClient redissonClient;

    @Override
    public String getPartnerCode() {
        return config.getPartnerCode();
    }

    @Override
    public LinkAccountFlowType getFlowType() {
        return LinkAccountFlowType.OAUTH2;
    }

    @Profiler
    @Override
    public LinkAccountResponse linkAccount(LinkAccountRequest req) {
        var partner = partnerService.findByCode(getPartnerCode());
        var module = partnerModuleService.findByPartnerAndModule(partner.getId(), ModuleType.LINK_ACCOUNT);
        if (!module.isEnabled()) {
            log.error("Partner module is not enabled");
            throw new BusinessLogicException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION);
        }

        switch (req.getStep()) {
            case INITIATE -> {
                return generateWebviewUrl(req);
            }
            case CALLBACK -> {
                return makeLinkAccountByCallback(req);
            }
            case CALL_LINK_API -> {
                return makeLinkAccount(req);
            }
            default ->
                    throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "step", "Invalid link account step for SkyJoy: " + req.getStep());
        }
    }

    @Profiler
    @Override
    public boolean unlinkAccount(Long vclubUserId, Long deleteAt) {
        var partner = partnerService.findByCode(getPartnerCode());
        var module = partnerModuleService.findByPartnerAndModule(partner.getId(), ModuleType.LINK_ACCOUNT);
        if (!module.isEnabled()) {
            log.error("Partner module is not enabled");
            throw new BusinessLogicException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION);
        }
        var config = jsonUtils.treeToValue(module.getConfig(), LinkAccountConfig.class);
        if (!config.isAllowUnlink()) {
            log.error("Partner does not allow unlink account");
            throw new BusinessLogicException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION);
        }

        var mapping = partnerUserMappingService.optByPartnerIdAndVClubUserId(partner.getId(), vclubUserId);

        if (mapping.isEmpty()) {
            log.warn("vclub_user_id={} is not linked to any partner user", vclubUserId);
            throw new BusinessLogicException(AppErrorCode.ACCOUNT_NOT_LINKED);
        }
        if (Objects.isNull(deleteAt)) {
            deleteAt = System.currentTimeMillis();
        }

        var lockKey = String.format("%s-%s-%s", getPartnerCode(), mapping.get().getPartnerUserId(), vclubUserId);
        try (var lock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_USER_MAPPING_CUD + lockKey)) {
            // Check if mapping is already deleted
            if (!partnerUserMappingService.isMapped(partner.getId(), mapping.get().getPartnerUserId(), vclubUserId)) {
                return true;
            }

            // Call unlink account api to skyjoy
            var isUnlinked = skyJoyClient.unlinkAccount(SkyJoyUnLinkAccountRequest.builder()
                    .skyjoyId(mapping.get().getPartnerUserId())
                    .partnerMemberId(String.valueOf(vclubUserId))
                    .build());

            // Update mapping
            if (isUnlinked) {
                partnerUserMappingService.update(mapping.get().getId(), PartnerUserMappingUpdateDto.builder()
                        .active(false)
                        .deleteAt(deleteAt)
                        .build());
                return true;
            }

            return false;
        }
    }

    @Profiler
    private LinkAccountResponse generateWebviewUrl(LinkAccountRequest req) {
        var partner = partnerService.findByCode(getPartnerCode());
        var mapping = partnerUserMappingService.optByPartnerIdAndVClubUserId(partner.getId(), req.getVclubUserId());
        if (mapping.isPresent()) {
            log.warn("vclub_user_id={} is already linked to partner_user_id={}", req.getVclubUserId(), mapping.get().getPartnerUserId());
            throw new BusinessLogicException(AppErrorCode.VINCLUB_ACCOUNT_LINKED);
        }

        var executionId = redirectUriHelper.buildRedirectExecutionId(req);
        var redirectUrl = redirectUriHelper.buildRedirectUrl(getPartnerCode(), ModuleType.LINK_ACCOUNT, executionId);
        URI uri = UriComponentsBuilder.fromHttpUrl(config.getLinkAccount().getAuthUrl())
                .queryParam("client_id", config.getLinkAccount().getClientId())
                .queryParam("redirect_uri", redirectUrl)
                .queryParam("response_type", "code")
                .queryParam("ui_locales", I18nUtil.getLanguage())
                .build().toUri();

        var responseData = JsonNodeFactory.instance.objectNode();
        responseData.put("webview_url", uri.toString());

        return LinkAccountResponse.builder()
                .success(true)
                .nextStep(LinkAccountStep.OPEN_WEBVIEW)
                .data(responseData)
                .build();
    }

    @Profiler
    private LinkAccountResponse makeLinkAccountByCallback(LinkAccountRequest req) {
        var partner = partnerService.findByCode(getPartnerCode());
        var mapping = partnerUserMappingService.optByPartnerIdAndVClubUserId(partner.getId(), req.getVclubUserId());
        if (mapping.isPresent()) {
            log.warn("vclub_user_id={} is already linked to partner_user_id={}", req.getVclubUserId(), mapping.get().getPartnerUserId());
            throw new BusinessLogicException(AppErrorCode.VINCLUB_ACCOUNT_LINKED);
        }

        var executionId = req.getData().at("/execution_id").asText();
        if (!StringUtils.hasText(executionId)) {
            log.error("Missing execution_id in request: {}", jsonUtils.toString(req));
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "execution_id");
        }

        var authCode = req.getData().at("/code").asText();
        if (!StringUtils.hasText(authCode)) {
            log.error("Missing code in request: {}", jsonUtils.toString(req));
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "code");
        }

        var previousRequest = redirectUriHelper.getRedirectExecutionJsonData(executionId, LinkAccountRequest.class);
        if (!LinkAccountStep.INITIATE.equals(previousRequest.getStep()) || !getPartnerCode().equals(previousRequest.getPartnerCode())) {
            log.error("Invalid execution_id: {}", executionId);
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "execution_id", "Dữ liệu của execution_id không hợp lệ");
        }

        var vclubUserId = previousRequest.getVclubUserId();
        var redirectUrl = redirectUriHelper.buildRedirectUrl(getPartnerCode(), ModuleType.LINK_ACCOUNT, executionId);

        // Exchange code to token
        var tokenResponse = skyJoyClient.exchangeTokenByCode(SkyJoyExchangeTokenRequest.builder()
                .clientId(config.getLinkAccount().getClientId())
                .grantType("authorization_code")
                .code(authCode)
                .redirectUri(redirectUrl)
                .build());

        if (Objects.isNull(tokenResponse) || !StringUtils.hasText(tokenResponse.getAccessToken())) {
            log.error("No data returned from SkyJoy when exchanging token by code");
            throw new BusinessLogicException(AppErrorCode.EXCHANGE_TOKEN_BY_CODE_ERROR, "Không có dữ liệu trả về từ SkyJoy");
        }

        // Get VClub user identity
        var vclubCustomer = internalCoreClient.getCustomerById(vclubUserId, false, false);
        if (Objects.isNull(vclubCustomer)) {
            log.error("vclub_user_id={} does not exist", vclubUserId);
            throw new BusinessLogicException(AppErrorCode.LINKED_VINCLUB_ACCOUNT_NOT_EXIST);
        }
        var vclubUserIdentity = VClubUserIdentityDto.builder()
                .email(vclubCustomer.getEmail())
                .phone(vclubCustomer.getPhoneNumber())
                .build();

        // Call API link account (Oauth)
        var sjToken = tokenResponse.getAccessToken();
        var linkAccountResponse = skyJoyClient.linkAccountByOauthToken(SkyJoyLinkAccountByOauthRequest.builder()
                .sjToken(sjToken)
                .partnerMemberId(String.valueOf(vclubUserId))
                .profile(vclubUserIdentity)
                .build());

        return processLinkAccountResponse(vclubUserId, linkAccountResponse, partner);
    }

    private LinkAccountResponse makeLinkAccount(LinkAccountRequest req) {
        var partnerUserId = req.getPartnerUserId();
        if (!StringUtils.hasText(partnerUserId)) {
            log.error("Missing partner_user_id in request: {}", jsonUtils.toString(req));
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "partner_user_id");
        }
        var vclubUserId = req.getVclubUserId();
        if (Objects.isNull(vclubUserId)) {
            log.error("Missing vclub_user_id in request: {}", jsonUtils.toString(req));
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "vclub_user_id");
        }

        var partner = partnerService.findByCode(getPartnerCode());
        var mapping = partnerUserMappingService.optByPartnerIdAndPartnerUserId(partner.getId(), partnerUserId)
                .orElse(partnerUserMappingService.optByPartnerIdAndVClubUserId(partner.getId(), vclubUserId).orElse(null));

        if (Objects.nonNull(mapping)) {
            if (!mapping.getVclubUserId().equals(vclubUserId)) {
                log.warn("partner_user_id={} is already linked to other vclub_user_id={}", partnerUserId, mapping.getVclubUserId());
                throw new BusinessLogicException(AppErrorCode.PARTNER_ACCOUNT_LINKED);
            }
            if (!mapping.getPartnerUserId().equals(partnerUserId)) {
                log.warn("vclub_user_id={} is already linked to other partner_user_id={}", vclubUserId, mapping.getPartnerUserId());
                throw new BusinessLogicException(AppErrorCode.VINCLUB_ACCOUNT_LINKED);
            }
            log.warn("partner_user_id={} is already linked to vclub_user_id={}", partnerUserId, vclubUserId);
            throw new BusinessLogicException(AppErrorCode.ACCOUNT_LINKED);
        }

        // Get VClub user identity
        var vclubCustomer = internalCoreClient.getCustomerById(vclubUserId, false, false);
        if (Objects.isNull(vclubCustomer)) {
            log.error("vclub_user_id={} does not exist", vclubUserId);
            throw new BusinessLogicException(AppErrorCode.LINKED_VINCLUB_ACCOUNT_NOT_EXIST);
        }

        // Call API link account
        var linkAccountResponse = skyJoyClient.linkAccount(SkyJoyLinkAccountRequest.builder()
                .skyjoyId(partnerUserId)
                .partnerMemberId(String.valueOf(vclubUserId))
                .build());

        return processLinkAccountResponse(vclubUserId, linkAccountResponse, partner);
    }

    private LinkAccountResponse processLinkAccountResponse(Long vclubUserId, SkyJoyLinkAccountResponse linkAccountResponse, Partner partner) {
        if (Objects.isNull(linkAccountResponse) || !StringUtils.hasText(linkAccountResponse.getSkyjoyId())) {
            log.error("No data returned from SkyJoy when linking account");
            throw new BusinessLogicException(AppErrorCode.LINK_ACCOUNT_UNEXPECTED_ERROR, "Không có dữ liệu trả về từ SkyJoy");
        }
        var lockKey = String.format("%s-%s-%s", getPartnerCode(), linkAccountResponse.getSkyjoyId(), vclubUserId);
        try (var lock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_USER_MAPPING_CUD + lockKey)) {
            // Create mapping
            var createdMapping = partnerUserMappingService.create(PartnerUserMappingCreateDto.builder()
                    .partnerId(partner.getId())
                    .vclubUserId(vclubUserId)
                    .partnerUserId(linkAccountResponse.getSkyjoyId())
                    .partnerUserIdentity(linkAccountResponse.toPartnerUserIdentity())
                    .build());

            var linkedAccount = PartnerUserMappingMapper.INSTANCE.toLinkedAccountResponse(createdMapping);
            linkedAccount.setPartnerCode(getPartnerCode());

            return LinkAccountResponse.builder()
                    .success(true)
                    .nextStep(LinkAccountStep.COMPLETE)
                    .data(jsonUtils.toNode(linkedAccount))
                    .build();
        }
    }
}
