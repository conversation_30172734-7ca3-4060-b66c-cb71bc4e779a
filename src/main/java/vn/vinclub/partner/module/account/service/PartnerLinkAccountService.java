package vn.vinclub.partner.module.account.service;

import vn.vinclub.partner.domain.dto.request.LinkAccountRequest;
import vn.vinclub.partner.domain.dto.response.LinkAccountResponse;
import vn.vinclub.partner.domain.enums.LinkAccountFlowType;

public interface PartnerLinkAccountService {
    String getPartnerCode();

    LinkAccountFlowType getFlowType();

    LinkAccountResponse linkAccount(LinkAccountRequest req);

    boolean unlinkAccount(Long vclubUserId, Long deleteAt);
}
