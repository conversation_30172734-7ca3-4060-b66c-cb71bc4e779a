package vn.vinclub.partner.module.token.registry;

import org.springframework.stereotype.Component;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.module.token.service.PartnerTokenService;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class PartnerTokenServiceRegistry {
    private final Map<String, PartnerTokenService> svcMap;

    public PartnerTokenServiceRegistry(List<PartnerTokenService> services) {
        this.svcMap = services.stream()
                .collect(Collectors.toMap(PartnerTokenService::getPartnerCode, s -> s));
    }

    public PartnerTokenService getService(String partnerCode) {
        return Optional.ofNullable(svcMap.get(partnerCode))
                .orElseThrow(() -> new BusinessLogicException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION));
    }
}
