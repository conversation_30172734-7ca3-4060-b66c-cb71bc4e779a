package vn.vinclub.partner.module.token.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;
import vn.vinclub.partner.config.PartnerConfig;
import vn.vinclub.partner.module.token.service.PartnerTokenService;
import vn.vinclub.partner.service.impl.JwtCommonImpl;

import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class SkyJoyTokenService implements PartnerTokenService {
    private final PartnerConfig.SkyJoy config;
    private final JwtCommonImpl jwtCommon;

    @Override
    public String getPartnerCode() {
        return config.getPartnerCode();
    }

    @Override
    public String getPartnerUserId(String token) {
        return getClaims(token).get("partner_user_id") == null ? null : getClaims(token).get("partner_user_id").toString();
    }

    @Override
    public boolean verifyTxnToken(String txnToken, String partnerUserId, Long vclubUserId, String partnerTransactionId, Long pointAmount, Long requestTime) {
        try {
            Jwt partnerJwt = getJwt(txnToken);
            Map<String, Object> claims = partnerJwt.getClaims();
            var matchPartnerUserId = partnerUserId.equals(claims.get("partner_user_id").toString());
            var matchVclubUserId = vclubUserId.equals(Long.valueOf(claims.get("vclub_user_id").toString()));
            var matchPartnerTransactionId = partnerTransactionId.equals(claims.get("partner_transaction_id").toString());
            var matchPointAmount = pointAmount.equals(Long.valueOf(claims.get("point_amount").toString()));
            var matchRequestTime = requestTime.equals(Long.valueOf(claims.get("request_time").toString()));
            if (matchPartnerUserId && matchVclubUserId && matchPartnerTransactionId && matchPointAmount && matchRequestTime) {
                return true;
            }
            log.error("Token payload not match. matchPartnerUserId={}, matchVclubUserId={}, matchPartnerTransactionId={}, matchPointAmount={}, matchRequestTime={}",
                    matchPartnerUserId, matchVclubUserId, matchPartnerTransactionId, matchPointAmount, matchRequestTime
            );
        } catch (Exception e) {
            log.error("Failed when verifyTxnToken({}, {}, {}, {}, {}, {})", txnToken, partnerUserId, vclubUserId, partnerTransactionId, pointAmount, requestTime, e);
        }
        return false;
    }

    private Jwt getJwt(String token) {
        return jwtCommon.decodePartnerJwt(config.getToken().getPublicKey(), token);
    }

    private Map<String, Object> getClaims(String token) {
        Jwt partnerJwt = getJwt(token);
        return partnerJwt.getClaims();
    }
}
