package vn.vinclub.partner.module.point.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.common.util.DateUtils;
import vn.vinclub.partner.client.SkyJoyClient;
import vn.vinclub.partner.client.request.SkyJoyLookupTxnRequest;
import vn.vinclub.partner.client.request.SkyJoyPointAccrualRequest;
import vn.vinclub.partner.config.PartnerConfig;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.constant.MetadataKey;
import vn.vinclub.partner.domain.dto.external.point.PartnerPointTxnResponse;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.exception.RequestTimeOutException;
import vn.vinclub.partner.module.point.service.PartnerPointService;
import vn.vinclub.partner.service.PartnerService;
import vn.vinclub.partner.util.ServiceResponse;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class SkyJoyPointService implements PartnerPointService {
    private static final String DATE_FORMAT = "yyyy-MM-dd";

    private final PartnerService partnerService;

    private final SkyJoyClient skyJoyClient;
    private final PartnerConfig.SkyJoy config;
    private final BaseJsonUtils jsonUtils;

    @Override
    public String getPartnerCode() {
        return config.getPartnerCode();
    }

    @Profiler
    @Override
    public PartnerPointTxnResponse topUpPoint(PartnerPointTransactionHistory transaction) {
        validateTopUpTransaction(transaction);
        var voucherPurchaseDate = transaction.getMetadata().get(MetadataKey.PartnerTransaction.TOPUP_EXTRA_INFO).get(MetadataKey.PartnerTransaction.TopupExtraInfo.VOUCHER_PURCHASED_TIME).asLong();
        var request = SkyJoyPointAccrualRequest.builder()
                .skyjoyId(transaction.getPartnerUserId())
                .transactionId(transaction.getTransactionId())
                .transactionDes(transaction.getDescription())
                .transactionDate(Instant.ofEpochMilli(transaction.getRequestTime()).toString())
                .extraData(SkyJoyPointAccrualRequest.ExtraData.builder()
                        .points(transaction.getPointAmount())
                        .vinclubId(transaction.getVclubUserId().toString())
                        .skyPointVoucherDate(DateUtils.convertDateToString(voucherPurchaseDate, DATE_FORMAT))
                        .build())
                .build();

        try {
            var response = skyJoyClient.pointAccrual(request);
            return PartnerPointTxnResponse.builder()
                    .partnerUserId(response.getSkyjoyId())
                    .transactionId(transaction.getTransactionId())
                    .partnerTransactionId(response.getBitReference())
                    .processedTime(DateUtils.getMilliseconds(response.getTimestamp()))
                    .partnerPoint(transaction.getPointAmount())
                    .status(PointHistoryStatus.SUCCESS)
                    .request(jsonUtils.toString(request))
                    .response(response.getRawResponse())
                    .build();
        } catch (RequestTimeOutException e) {
            return PartnerPointTxnResponse.builder()
                    .transactionId(transaction.getTransactionId())
                    .status(PointHistoryStatus.PROCESSING)
                    .request(jsonUtils.toString(request))
                    .build();
        } catch (Exception e) {
            String response = null;
            String reason = e.getMessage();
            if (e instanceof BusinessLogicException ex) {
                @SuppressWarnings("unchecked")
                var payload = (ServiceResponse<String>) ex.getPayload();
                response = payload.getData();
                reason = payload.getFirstMessage();
            }
            return PartnerPointTxnResponse.builder()
                    .transactionId(transaction.getTransactionId())
                    .status(PointHistoryStatus.FAILED)
                    .failedReason(reason)
                    .request(jsonUtils.toString(request))
                    .response(response)
                    .build();
        }
    }

    @Profiler
    @Override
    public PartnerPointTxnResponse spendPoint(PartnerPointTransactionHistory transaction) {
        throw new UnsupportedOperationException("Spend point is not supported by SkyJoy");
    }

    @Profiler
    @Override
    public PartnerPointTxnResponse swapPoint(PartnerPointTransactionHistory transaction) {
        throw new UnsupportedOperationException("Swap point is not supported by SkyJoy");
    }

    @Override
    public PartnerPointTxnResponse checkTimeoutTxn(PartnerPointTransactionHistory transaction) {
        validateTransactionForCheckTimeout(transaction);
        var request = SkyJoyLookupTxnRequest.builder()
                .transactionId(transaction.getTransactionId())
                .skyjoyId(transaction.getPartnerUserId())
                .build();

        try {
            var response = skyJoyClient.lookupTxn(request);
            var status = switch (response.getStatus()) {
                case "SUCCESS" -> PointHistoryStatus.SUCCESS;
                case "FAILED" -> PointHistoryStatus.FAILED;
                case "NEW" -> PointHistoryStatus.PROCESSING;
                default -> throw new IllegalStateException("Unknown transaction status: " + response.getStatus());
            };
            return PartnerPointTxnResponse.builder()
                    .partnerUserId(transaction.getPartnerUserId())
                    .transactionId(transaction.getTransactionId())
                    .partnerTransactionId(response.getBitReference())
                    .processedTime(Optional.ofNullable(response.getTransactionTime()).map(OffsetDateTime::toInstant).map(Instant::toEpochMilli).orElse(null))
                    .partnerPoint(transaction.getPointAmount())
                    .status(status)
                    .response(response.getRawResponse())
                    .build();
        } catch (Exception e) {
            String response = null;
            String reason = e.getMessage();
            var isNotFound = false;
            if (e instanceof BusinessLogicException ex) {
                @SuppressWarnings("unchecked")
                var payload = (ServiceResponse<String>) ex.getPayload();
                response = payload.getData();
                reason = payload.getFirstMessage();
                isNotFound = AppErrorCode.TRANSACTION_NOT_FOUND.getCode().equals(payload.getCode()) || AppErrorCode.PARTNER_USER_NOT_FOUND.getCode().equals(payload.getCode());
            }
            if (isNotFound) {
                return PartnerPointTxnResponse.builder()
                        .transactionId(transaction.getTransactionId())
                        .status(PointHistoryStatus.FAILED)
                        .failedReason(reason)
                        .response(response)
                        .build();
            }
            throw e;
        }
    }

    private void validateTransactionForCheckTimeout(PartnerPointTransactionHistory transaction) {
        if (!PointHistoryStatus.PROCESSING.equals(transaction.getStatus())) {
            throw new BusinessLogicException(AppErrorCode.INVALID_TRANSACTION_STATUS);
        }
    }

    private void validateTopUpTransaction(PartnerPointTransactionHistory transaction) {
        var partner = partnerService.findByCode(getPartnerCode());
        if (!partner.getId().equals(transaction.getPartnerId())) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "partnerId", "partnerId không phải là đối tác SkyJoy");
        }

        if (!TransactionType.TOP_UP_POINT.equals(transaction.getTransactionType())) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "transactionType", "transactionType phải là TOP_UP_POINT");
        }

        if (transaction.getPointAmount() <= 0) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "pointAmount", "Số điểm phải lớn hơn 0");
        }

        if (!ActorSystem.VINCLUB.equals(transaction.getActorSystem())) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "actorSystem", "actorSystem phải là VINCLUB");
        }

        // validate voucher purchase date
        if (transaction.getMetadata() == null || transaction.getMetadata().get(MetadataKey.PartnerTransaction.TOPUP_EXTRA_INFO) == null || transaction.getMetadata().get(MetadataKey.PartnerTransaction.TOPUP_EXTRA_INFO).get(MetadataKey.PartnerTransaction.TopupExtraInfo.VOUCHER_PURCHASED_TIME) == null) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "metadata", "voucherPurchasedTime không tồn tại trong metadata");
        }
        
        var voucherPurchaseDate = transaction.getMetadata().get(MetadataKey.PartnerTransaction.TOPUP_EXTRA_INFO).get(MetadataKey.PartnerTransaction.TopupExtraInfo.VOUCHER_PURCHASED_TIME).asLong();
        if (voucherPurchaseDate <= 0) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "voucherPurchasedTime", "voucherPurchasedTime phải lớn hơn 0");
        }
    }
}
