package vn.vinclub.partner.module.point.registry;

import org.springframework.stereotype.Component;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.IgnoreProcessingException;
import vn.vinclub.partner.module.point.service.PartnerPointService;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class PartnerPointServiceRegistry {
    private final Map<String, PartnerPointService> svcMap;

    public PartnerPointServiceRegistry(List<PartnerPointService> services) {
        this.svcMap = services.stream()
                .collect(Collectors.toMap(PartnerPointService::getPartnerCode, s -> s));
    }

    public PartnerPointService getService(String partnerCode) {
        return Optional.ofNullable(svcMap.get(partnerCode))
                .orElseThrow(() -> new IgnoreProcessingException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION));
    }
}
