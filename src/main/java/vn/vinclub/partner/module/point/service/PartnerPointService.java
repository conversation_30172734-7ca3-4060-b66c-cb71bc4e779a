package vn.vinclub.partner.module.point.service;


import vn.vinclub.partner.domain.dto.external.point.PartnerPointTxnResponse;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;

public interface PartnerPointService {
    String getPartnerCode();

    PartnerPointTxnResponse topUpPoint(PartnerPointTransactionHistory transaction);

    PartnerPointTxnResponse spendPoint(PartnerPointTransactionHistory transaction);

    PartnerPointTxnResponse swapPoint(PartnerPointTransactionHistory transaction);

    PartnerPointTxnResponse checkTimeoutTxn(PartnerPointTransactionHistory transaction);
}
