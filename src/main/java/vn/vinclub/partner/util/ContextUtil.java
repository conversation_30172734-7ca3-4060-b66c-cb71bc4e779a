package vn.vinclub.partner.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
public class ContextUtil {

	/** The application context */
	private static ApplicationContext applicationContext;

	/**
	 * Constructors
	 * 
	 * @param applicationContext - the application context
	 */
	@Autowired
	protected ContextUtil(ApplicationContext applicationContext) {
		ContextUtil.applicationContext = applicationContext;
	}

	/**
	 * Get a bean by Class from the application context
	 * 
	 * @param clazz - class type of bean
	 * @return bean
	 */
	public static <T> T getBean(Class<T> clazz) {
		return applicationContext.getBean(clazz);
	}

	// 2 instances
	public static <T> T getBean(String name, Class<T> clazz) {
		return applicationContext.getBean(name, clazz);
	}

	/**
	 * Get a bean by bean name from the application context
	 * 
	 * @param name - the name of Bean
	 * @return bean
	 */
	public static Object getBean(String name) {
		return applicationContext.getBean(name);
	}
}
