package vn.vinclub.partner.util;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import vn.vinclub.partner.constant.AppErrorCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ServiceResponse<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private int code;

    @Builder.Default
    private List<String> message = new ArrayList<>();

    private T data;

    public ServiceResponse<?> message(String msg) {
        message.add(msg);
        return this;
    }

    public ServiceResponse<?> message(List<String> msg) {
        message.addAll(msg);
        return this;
    }

    @JsonIgnore
    public String getFirstMessage() {
        if (message.isEmpty()) return null;
        return message.getFirst();
    }

    public static <T> ServiceResponse<T> success(T data) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(AppErrorCode.SUCCESS.getCode()).data(data).build();
        res.message(AppErrorCode.SUCCESS.getMessage());
        return res;
    }

    public static <T> ServiceResponse<T> success(T data, String message) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(AppErrorCode.SUCCESS.getCode()).data(data).build();
        res.message(message);
        return res;
    }

    public static <T> ServiceResponse<T> error(T data, int errorCode, String message) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCode).data(data).build();
        res.message(message);
        return res;
    }

    public static <T> ServiceResponse<T> error(T data, AppErrorCode errorCodeEnum) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCodeEnum.getCode()).data(data).build();
        res.message(errorCodeEnum.getMessage());
        return res;
    }

    public static <T> ServiceResponse<T> error(int errorCode, List<String> message) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCode).build();
        res.message(message);
        return res;
    }

    public static <T> ServiceResponse<T> error(int errorCode, String message) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCode).build();
        res.message(message);
        return res;
    }

    public static <T> ServiceResponse<T> error(AppErrorCode errorCodeEnum) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCodeEnum.getCode()).build();
        res.message(errorCodeEnum.getMessage());
        return res;
    }

    public static <T> ServiceResponse<T> error(AppErrorCode errorCodeEnum, Object... args) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCodeEnum.getCode()).build();
        res.message(String.format(errorCodeEnum.getMessage(), args));
        return res;
    }

    public static <T> ServiceResponse<T> error(int errorCode, String message, Object... args) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCode).build();
        res.message(String.format(message, args));
        return res;
    }

    public static <T> ServiceResponse<T> error(List<String> data, int errorCode) {
        return ServiceResponse.<T>builder().code(errorCode).message(data).build();
    }

    public static <T> ServiceResponse<T> error(T data, AppErrorCode errorCodeEnum, Object... args) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCodeEnum.getCode()).data(data).build();
        res.message(String.format(errorCodeEnum.getMessage(), args));
        return res;
    }
}