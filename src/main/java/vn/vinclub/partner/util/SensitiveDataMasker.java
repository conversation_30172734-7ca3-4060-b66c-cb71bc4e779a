package vn.vinclub.partner.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.JsonUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

/**
 * Utility class for masking sensitive data in JSON objects
 * 
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SensitiveDataMasker {

    private static final String MASK_VALUE = "***MASKED***";
    
    /**
     * Set of sensitive field names that should be masked
     */
    private static final Set<String> SENSITIVE_FIELDS = new HashSet<>(Arrays.asList(
        // Authentication related
        "password",
        "accessToken", "access_token",
        "refreshToken", "refresh_token", 
        "clientSecret", "client_secret",
        "sjToken",
        "partnerSignedToken", "partner_signed_token",

        "apiKey", "api_key",
        "secret", "authorization"
    ));

    /**
     * Mask sensitive data in an object by converting to JSON and masking sensitive fields
     * 
     * @param data the object to mask
     * @return JSON string with sensitive fields masked
     */
    @Profiler
    public static String maskSensitiveData(Object data) {
        if (data == null) {
            return null;
        }
        
        try {
            // Convert object to JSON string first
            String jsonString = JsonUtils.toString(data);
            if (jsonString == null) {
                return null;
            }
            
            // Parse JSON and mask sensitive fields
            JsonNode jsonNode = JsonUtils.readTree(jsonString);
            JsonNode maskedNode = maskJsonNode(jsonNode);
            
            return JsonUtils.toString(maskedNode);
        } catch (Exception e) {
            log.warn("Error masking sensitive data, returning original string representation", e);
            return String.valueOf(data);
        }
    }

    /**
     * Recursively mask sensitive fields in a JsonNode
     * 
     * @param node the JsonNode to process
     * @return JsonNode with sensitive fields masked
     */
    @Profiler
    private static JsonNode maskJsonNode(JsonNode node) {
        if (node == null) {
            return null;
        }
        
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node.deepCopy();
            Iterator<String> fieldNames = objectNode.fieldNames();
            
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                JsonNode fieldValue = objectNode.get(fieldName);
                
                if (isSensitiveField(fieldName)) {
                    // Mask sensitive field
                    objectNode.put(fieldName, MASK_VALUE);
                } else if (fieldValue.isObject() || fieldValue.isArray()) {
                    // Recursively process nested objects/arrays
                    objectNode.set(fieldName, maskJsonNode(fieldValue));
                }
            }
            return objectNode;
            
        } else if (node.isArray()) {
            ArrayNode arrayNode = (ArrayNode) node.deepCopy();
            for (int i = 0; i < arrayNode.size(); i++) {
                JsonNode element = arrayNode.get(i);
                if (element.isObject() || element.isArray()) {
                    arrayNode.set(i, maskJsonNode(element));
                }
            }
            return arrayNode;
        }
        
        return node;
    }

    /**
     * Check if a field name is considered sensitive
     * 
     * @param fieldName the field name to check
     * @return true if the field is sensitive
     */
    private static boolean isSensitiveField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        
        // Direct match
        if (SENSITIVE_FIELDS.contains(lowerFieldName)) {
            return true;
        }
        
        // Check if field name contains sensitive keywords
        return SENSITIVE_FIELDS.stream()
                .anyMatch(sensitiveField -> lowerFieldName.contains(sensitiveField.toLowerCase()));
    }

    /**
     * Add custom sensitive field names
     * 
     * @param fieldNames additional field names to consider sensitive
     */
    public static void addSensitiveFields(String... fieldNames) {
        if (fieldNames != null) {
            for (String fieldName : fieldNames) {
                if (fieldName != null && !fieldName.trim().isEmpty()) {
                    SENSITIVE_FIELDS.add(fieldName.toLowerCase());
                }
            }
        }
    }

    /**
     * Get the current set of sensitive field names (for testing/debugging)
     * 
     * @return copy of sensitive fields set
     */
    public static Set<String> getSensitiveFields() {
        return new HashSet<>(SENSITIVE_FIELDS);
    }
}
