package vn.vinclub.partner.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import vn.vinclub.partner.domain.dto.encryption.EncryptKeyData;
import vn.vinclub.partner.domain.dto.encryption.EncryptKeySetting;
import vn.vinclub.partner.domain.enums.EncryptKeyType;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Objects;

import static java.nio.charset.StandardCharsets.UTF_8;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class KeyUtil {
    private static final int DEFAULT_RSA_KEY_SIZE = 2048;
    private static final int DEFAULT_AES_KEY_SIZE = 256;
    private static final String DEFAULT_ALGORITHM = "RSA";
    private static final String PUBLIC_KEY_START_TAG = "-----BEGIN PUBLIC KEY-----";
    private static final String PUBLIC_KEY_END_TAG = "-----END PUBLIC KEY-----";
    private static final String PRIVATE_KEY_START_TAG = "-----BEGIN PRIVATE KEY-----";
    private static final String PRIVATE_KEY_END_TAG = "-----END PRIVATE KEY-----";


    public static KeyPair generateKeyPair(String algorithm, int keySize) {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(algorithm);
            keyPairGenerator.initialize(keySize);
            return keyPairGenerator.generateKeyPair();
        } catch (NoSuchAlgorithmException e) {
            log.error("Failed to generate key pair: {}", e.getMessage());
            throw new IllegalArgumentException("Invalid algorithm", e);
        }
    }

    public static SecretKey generateSecretKey(String algorithm, int keySize) {
        try {
            KeyGenerator keyGen = KeyGenerator.getInstance(algorithm);
            keyGen.init(keySize);
            return keyGen.generateKey();
        } catch (NoSuchAlgorithmException e) {
            log.error("Failed to generate AES key: {}", e.getMessage());
            throw new IllegalArgumentException("Invalid algorithm", e);
        }
    }

    public static EncryptKeyData generateEncryptKeyData(EncryptKeySetting encryptKeySetting) {
        var algorithm = DEFAULT_ALGORITHM;
        var keySize = DEFAULT_RSA_KEY_SIZE;

        if (Objects.nonNull(encryptKeySetting)) {
            if (StringUtils.hasText(encryptKeySetting.getKeyType().toValue())) {
                algorithm = encryptKeySetting.getKeyType().toValue();
                if (EncryptKeyType.AES.toValue().equals(algorithm)) {
                    keySize = DEFAULT_AES_KEY_SIZE;
                }
            }
            if (encryptKeySetting.getKeySize() > 0) {
                keySize = encryptKeySetting.getKeySize();
            }
        }
        if (EncryptKeyType.AES.toValue().equals(algorithm)) {
            SecretKey key = generateSecretKey(algorithm, keySize);
            return EncryptKeyData.builder()
                    .encryptKeyType(EncryptKeyType.AES)
                    .secretKey(encodeSecretKey(key))
                    .build();
        } else if (EncryptKeyType.RSA.toValue().equals(algorithm)) {
            KeyPair keyPair = generateKeyPair(algorithm, keySize);
            return EncryptKeyData.builder()
                    .encryptKeyType(EncryptKeyType.RSA)
                    .publicKey(encodePublicKey(keyPair.getPublic()))
                    .privateKey(encodePrivateKey(keyPair.getPrivate()))
                    .build();
        } else {
            throw new IllegalArgumentException("Unsupported key type: " + algorithm);
        }
    }

    public static String encodePrivateKey(PrivateKey privateKey) {
        return Base64.getEncoder().encodeToString(privateKey.getEncoded());
    }

    public static String encodePublicKey(PublicKey publicKey) {
        return Base64.getEncoder().encodeToString(publicKey.getEncoded());
    }

    public static String encodeSecretKey(SecretKey secretKey) {
        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }

    public static PrivateKey decodePrivateKey(String base64PrivateKey) {
        if (!StringUtils.hasText(base64PrivateKey)) {
            throw new IllegalArgumentException("Private key is empty");
        }
        try {
            base64PrivateKey = base64PrivateKey
                    .replace(PRIVATE_KEY_START_TAG, "")
                    .replace(PRIVATE_KEY_END_TAG, "")
                    .replaceAll(System.lineSeparator(), "")
                    .trim();
            byte[] keyBytes = Base64.getDecoder().decode(base64PrivateKey);
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(spec);
        } catch (Exception e) {
            log.error("Failed to decode private key", e);
            throw new RuntimeException("Failed to decode private key", e);
        }
    }

    public static PublicKey decodePublicKey(String base64PublicKey) {
        if (!StringUtils.hasText(base64PublicKey)) {
            throw new IllegalArgumentException("Public key is empty");
        }
        try {
            base64PublicKey = base64PublicKey
                    .replace(PUBLIC_KEY_START_TAG, "")
                    .replace(PUBLIC_KEY_END_TAG, "")
                    .replaceAll(System.lineSeparator(), "")
                    .trim();
            byte[] keyBytes = Base64.getDecoder().decode(base64PublicKey);
            X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePublic(spec);
        } catch (Exception e) {
            log.error("Failed to decode public key", e);
            throw new RuntimeException("Failed to decode public key", e);
        }
    }

    public static String printFormatKey(PublicKey publicKey) {
        String key = encodePublicKey(publicKey);
        StringBuilder formattedKey = new StringBuilder();
        formattedKey.append(PUBLIC_KEY_START_TAG).append(System.lineSeparator());
        for (int i = 0; i < key.length(); i += 64) {
            formattedKey.append(key, i, Math.min(i + 64, key.length())).append(System.lineSeparator());
        }
        formattedKey.append(PUBLIC_KEY_END_TAG);
        return formattedKey.toString();
    }

    public static String printFormatKey(PrivateKey privateKey) {
        String key = encodePrivateKey(privateKey);
        StringBuilder formattedKey = new StringBuilder();
        formattedKey.append(PRIVATE_KEY_START_TAG).append(System.lineSeparator());
        for (int i = 0; i < key.length(); i += 64) {
            formattedKey.append(key, i, Math.min(i + 64, key.length())).append(System.lineSeparator());
        }
        formattedKey.append(PRIVATE_KEY_END_TAG);
        return formattedKey.toString();
    }

    public static String signDataWithRSAPrivateKey(String plainText, PrivateKey privateKey) {
        try {
            Signature privateSignature = Signature.getInstance("SHA256withRSA");
            privateSignature.initSign(privateKey);
            privateSignature.update(plainText.getBytes(UTF_8));

            byte[] signature = privateSignature.sign();

            return Base64.getEncoder().encodeToString(signature);
        } catch (Exception e) {
            log.error("Failed to sign data with RSA private key", e);
            throw new IllegalArgumentException("Failed to sign data with RSA private key", e);
        }
    }

    public static boolean verifyDataWithRSAPublicKey(String plainText, String signedData, PublicKey publicKey) {
        try {
            byte[] signatureBytes = Base64.getDecoder().decode(signedData);
            Signature publicSignature = Signature.getInstance("SHA256withRSA");
            publicSignature.initVerify(publicKey);
            publicSignature.update(plainText.getBytes(UTF_8));

            return publicSignature.verify(signatureBytes);
        } catch (Exception e) {
            log.error("Failed to verify data with RSA public key", e);
            return false;
        }
    }
}
