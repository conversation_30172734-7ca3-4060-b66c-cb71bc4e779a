package vn.vinclub.partner.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class I18nUtil {

    public static String getLanguage() {
        return getLocale().getLanguage();
    }

    public static Locale getLocale() {
        return LocaleContextHolder.getLocale();
    }
}

