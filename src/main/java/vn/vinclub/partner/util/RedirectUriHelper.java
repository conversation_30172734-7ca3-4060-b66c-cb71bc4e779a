package vn.vinclub.partner.util;

import lombok.RequiredArgsConstructor;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.HandleRedirectResult;
import vn.vinclub.partner.domain.enums.ModuleType;
import vn.vinclub.partner.exception.BusinessLogicException;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Objects;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class RedirectUriHelper {
    private static final String CALLBACK_EXECUTION_ID_BUCKET_NAME = "partner_svc:callback-execution-id:";
    private final RedissonClient redissonClient;
    private final BaseJsonUtils jsonUtils;

    @Value( "${partner.process-module.redirect-to-server-uri.template}")
    private String redirectUrl;

    @Value("${partner.process-module.redirect-to-app-uri.template:}")
    private String partnerProcessModuleAppDeepLink;

    @Profiler
    public <T> String buildRedirectExecutionId(T data) {
        String executionId = UUID.randomUUID().toString();
        RBucket<String> bucket = redissonClient.getBucket(CALLBACK_EXECUTION_ID_BUCKET_NAME + executionId);
        bucket.set(jsonUtils.toString(data), Duration.ofMinutes(5));
        return executionId;
    }

    @Profiler
    public String buildRedirectUrl(String partnerCode, ModuleType module, String executionId) {
        return redirectUrl.replace("{partner_code}", partnerCode.toLowerCase())
                .replace("{module}", module.toValue().toLowerCase())
                .replace("{execution_id}", executionId);
    }

    @Profiler
    public <T> T getRedirectExecutionJsonData(String executionId, Class<T> clazz) {
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("Class is null");
        }
        RBucket<String> bucket = redissonClient.getBucket(CALLBACK_EXECUTION_ID_BUCKET_NAME + executionId);
        if (!bucket.isExists()) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "execution_id", "execution_id không tồn tại");
        }
        String jsonData = bucket.get();
        bucket.delete();
        return jsonUtils.toObject(jsonData, clazz);
    }

    @Profiler
    public String generateAppDeepLink(HandleRedirectResult result) {
        String deepLink = partnerProcessModuleAppDeepLink
                .replace("{partner_code}", result.partnerCode().toLowerCase())
                .replace("{module}", result.module().name().toLowerCase())
                .replace("{success}", String.valueOf(result.success()))
                .replace("{next_step}", String.valueOf(result.nextStep()));
        if (!result.success()) {
            deepLink = deepLink
                    + "&error_code=" + URLEncoder.encode(String.valueOf(result.errorCode()), StandardCharsets.UTF_8)
                    + "&message=" + URLEncoder.encode(result.errorMessage(), StandardCharsets.UTF_8);
        }
        return deepLink;
    }

}
