package vn.vinclub.partner.config;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaConfig {

    private static final String SASL_MECHANISM = "sasl.mechanism";
    private static final String SASL_JAAS_CONFIG = "sasl.jaas.config";
    private static final String BASIC_AUTH_CREDENTIALS_SOURCE = "basic.auth.credentials.source";
    private static final String SCHEMA_REGISTRY_BASIC_AUTH_USER_INFO = "schema.registry.basic.auth.user.info";
    private static final String SCHEMA_REGISTRY_URL = "schema.registry.url";
    private static final String AUTO_OFFSET_RESET = "auto.offset.reset";
    private static final String SECURITY_PROTOCOL = "security.protocol";
    
    @Value("${kafka.properties.bootstrap.servers}")
    private String bootstrapServer;

    @Value("${kafka.properties.security.protocol}")
    private String securityProtocol;

    @Value("${kafka.properties.sasl.mechanism}")
    private String saslMechanism;

    @Value("${kafka.properties.sasl.jaas.config}")
    private String jaslJaasConfig;

    @Value("${kafka.properties.basic.auth.credentials.source}")
    private String credentialSource;

    @Value("${kafka.properties.schema.registry.basic.auth.user.info}")
    private String registryUserInfo;

    @Value("${kafka.properties.schema.registry.url}")
    private String registryUrl;

    @Value("${kafka.properties.auto.offset.reset}")
    private String offsetReset;

    @Value("${kafka.properties.max-poll-records}")
    private String maxPollRecords;

    @Value("${kafka.properties.main.concurrency}")
    private Integer concurrency;

    @Value("${kafka.properties.main.group.name}")
    private String groupId;

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    @Bean
    public ProducerFactory<String, String> producerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServer);
        configProps.putAll(mapConfig());

        return new DefaultKafkaProducerFactory<>(configProps);
    }

    //Internal Queue
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaIntListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(intConsumerFactory());
        factory.setConcurrency(concurrency); // Number of consumers
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        return factory;
    }


    @Bean
    public ConsumerFactory<String, String> intConsumerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServer);
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        configProps.putAll(mapConfig());
        return new DefaultKafkaConsumerFactory<>(configProps);
    }

    private Map<String, Object> mapConfig() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(SASL_MECHANISM, saslMechanism);
        props.put(SASL_JAAS_CONFIG, jaslJaasConfig);
        props.put(SECURITY_PROTOCOL, securityProtocol);
        props.put(BASIC_AUTH_CREDENTIALS_SOURCE, credentialSource);
        if (!org.apache.commons.lang3.StringUtils.isEmpty(registryUserInfo)) {
            props.put(SCHEMA_REGISTRY_BASIC_AUTH_USER_INFO, registryUserInfo);
        }
        props.put(SCHEMA_REGISTRY_URL, registryUrl);
        if (StringUtils.hasText(offsetReset)) {
            props.put(AUTO_OFFSET_RESET, offsetReset);
        }
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollRecords);
        return props;
    }
}
