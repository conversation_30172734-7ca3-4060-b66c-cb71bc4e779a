package vn.vinclub.partner.config;

import org.springframework.data.domain.AuditorAware;
import org.springframework.lang.NonNull;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import vn.vinclub.partner.constant.AppConst;

import java.util.Optional;

@Component
public class AuditorAwareImpl implements AuditorAware<String> {


	@Override
	@NonNull
    public Optional<String> getCurrentAuditor() {
		if (SecurityContextHolder.getContext().getAuthentication() == null) {
			return Optional.of(AppConst.UPDATED_BY_AUTO);
		}

		// If the user is authenticated by the integration JWT, then return the partnerCode.username
		try {
			JwtAuthenticationToken authentication = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
			Jwt jwt = (Jwt) authentication.getCredentials();
			String username = jwt.getClaim(AppConst.INTEGRATION_JWT_CLAIM.USERNAME);
			if (StringUtils.hasText(username)) {
				return Optional.of(username);
			}
		} catch (Exception e) {
			// Do nothing
		}

		return Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication().getName());
	}

}
