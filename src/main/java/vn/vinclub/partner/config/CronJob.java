package vn.vinclub.partner.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import vn.vinclub.partner.service.EventService;
import vn.vinclub.partner.service.InternalPartnerService;

/**
 * author: SangLNN
 * date: 2025/01/06
 */
@Configuration
@Slf4j
@RequiredArgsConstructor
public class CronJob {

    private final EventService eventService;
    private final InternalPartnerService internalPartnerService;

    @Scheduled(initialDelay = 30000, fixedDelay = 30 * 1000)
    @SchedulerLock(name = "partner_svc:retryProcessPendingOutbox", lockAtLeastFor = "30s", lockAtMostFor = "10m")
    public void retryProcessPendingOutbox() {
        log.info("Start retryProcessPendingOutbox...........");
        eventService.processPendingOutboxEvent();
        log.info("End retryProcessPendingOutbox!");
    }

    @Scheduled(initialDelay = 60000, fixedDelay = 5 * 60 * 1000)
    @SchedulerLock(name = "partner_svc:recheckProcessingTransaction", lockAtLeastFor = "5m", lockAtMostFor = "10m")
    public void recheckProcessingTransaction() {
        log.info("Start recheckProcessingTransaction...........");
        internalPartnerService.recheckProcessingTransaction();
        log.info("End recheckProcessingTransaction!");
    }



}
