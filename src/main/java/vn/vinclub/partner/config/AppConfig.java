package vn.vinclub.partner.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.service.DistributedIdGenerator;
import vn.vinclub.partner.service.impl.SnowflakeNodeIdBasedRedis;

import java.util.Locale;

@Configuration
@RequiredArgsConstructor
public class AppConfig {

    private final RedissonClient redissonClient;

    private final ObjectMapper mapper;

    @Bean("pointTxnIdGenerator")
    public DistributedIdGenerator pointTxnIdGenerator() {
        return new SnowflakeNodeIdBasedRedis(redissonClient, "partner-svc-point-txn-id-gen");
    }

    @Bean
    public BaseJsonUtils baseJsonUtils() {
        return new BaseJsonUtils(mapper);
    }

    @Bean
    public JsonJacksonCodec jsonJacksonCodec() {
        return new JsonJacksonCodec(mapper);
    }

    @Bean
    public LocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver resolver = new AcceptHeaderLocaleResolver();
        resolver.setDefaultLocale(Locale.of(AppConst.DEFAULT_LANGUAGE, AppConst.DEFAULT_COUNTRY));
        return resolver;
    }

    @PostConstruct
    public void init() {
        JsonUtils.injectMapper(mapper);
    }
}
