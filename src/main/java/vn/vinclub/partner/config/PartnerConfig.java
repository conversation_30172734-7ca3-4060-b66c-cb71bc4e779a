package vn.vinclub.partner.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter 
@Slf4j
public class PartnerConfig {

    @Configuration
    @ConfigurationProperties(prefix = "partner.skyjoy")
    @Getter
    @Setter
    public static class SkyJoy {
        private String partnerCode;
        private Api api = new Api();
        private Token token = new Token();
        private LinkAccount linkAccount = new LinkAccount();
        private Auth auth = new Auth();

        @Getter
        @Setter
        public static class Api {
            private String baseUrl;

        }

        @Getter
        @Setter
        public static class Token {
            private String publicKey;
        }

        @Getter
        @Setter
        public static class LinkAccount {
            private String authUrl;
            private String tokenUrl;
            private String clientId;
        }

        @Getter
        @Setter
        public static class Auth {
            private String clientId;
            private String clientSecret;
            private String username;
            private String password;
            private String accessTokenUrl;
        }
    }
}
