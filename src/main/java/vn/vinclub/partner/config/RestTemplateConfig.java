package vn.vinclub.partner.config;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import vn.vinclub.partner.interceptor.RestTemplateLoggingInterceptor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Configuration
@EnableRetry
@RequiredArgsConstructor
public class RestTemplateConfig {


    private final Configuration poolConfiguration;

    @Bean
    public RestTemplate restTemplateWithConnectionPool() {
        RestTemplate restTemplate = new RestTemplate();
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(poolConfiguration.getMaxTotal());
        connectionManager.setDefaultMaxPerRoute(poolConfiguration.getMaxPerRoute());
        RequestConfig requestConfig = RequestConfig.custom()
                .setResponseTimeout(Timeout.ofSeconds(poolConfiguration.getTimeout()))
                .build();

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .evictIdleConnections(TimeValue.of(poolConfiguration.getMaxIdleTime(), TimeUnit.SECONDS))
                .evictExpiredConnections()
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
        if ("DEBUG".equalsIgnoreCase(poolConfiguration.getLoggingLevel())) {
            restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(requestFactory));
            List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();
            if (CollectionUtils.isEmpty(interceptors)) {
                interceptors = new ArrayList<>();
            }
            interceptors.add(new RestTemplateLoggingInterceptor());
            restTemplate.setInterceptors(interceptors);
        } else {
            restTemplate.setRequestFactory(requestFactory);
        }
        return restTemplate;
    }

    @Component
    @ConfigurationProperties(prefix = "httppool")
    @Data
    public static class Configuration {
        private Integer maxPerRoute;
        private Integer maxTotal;
        private Integer timeout;
        private Integer maxIdleTime;
        private String loggingLevel;
    }
}
