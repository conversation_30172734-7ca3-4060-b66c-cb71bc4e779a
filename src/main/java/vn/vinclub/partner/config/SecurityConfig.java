package vn.vinclub.partner.config;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.util.matcher.AndRequestMatcher;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.NegatedRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import vn.vinclub.partner.security.CmsAuthenticationFilter;
import vn.vinclub.partner.security.impl.VClubCustomerAuthenticationFilter;
import vn.vinclub.partner.service.impl.JwtCommonImpl;

@EnableWebSecurity
@Configuration
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtCommonImpl jwtService;

    @Value("${jwt.partner_integration.jwks-uri}")
    private String jwtIntegrationJwksUri;

    @Bean
    public SecurityFilterChain integrationSecurityFilterChain(HttpSecurity http) throws Exception {
        RequestMatcher affectedPaths = new AndRequestMatcher(
                new NegatedRequestMatcher(new AntPathRequestMatcher("/integration/v1/auth/**")),
                new AntPathRequestMatcher("/integration/**")
        );

        http
                .securityMatcher(affectedPaths)
                .csrf(AbstractHttpConfigurer::disable) // API calls, no need csrf
                .cors(AbstractHttpConfigurer::disable) // Handle this in API gateway
                .oauth2ResourceServer(oauth -> oauth.jwt(Customizer.withDefaults())) // Spring support
                .authorizeHttpRequests(authorizeRequests -> authorizeRequests
                        .anyRequest().authenticated());

        return http.build();
    }

    @Bean
    public SecurityFilterChain cmsSecurityFilterChain(HttpSecurity http) throws Exception {
        http
                .securityMatcher(new AntPathRequestMatcher("/admin/**"))
                .csrf(AbstractHttpConfigurer::disable) // API calls, no need csrf
                .cors(AbstractHttpConfigurer::disable) // Handle this in API gateway
                .addFilterBefore(new CmsAuthenticationFilter(jwtService, new JwtAuthenticationConverter()), BasicAuthenticationFilter.class) // Filter
        ;

        return http.build();
    }

    @Bean
    public SecurityFilterChain customerSecurityFilterChain(HttpSecurity http) throws Exception {
        RequestMatcher affectedPaths = new AndRequestMatcher(
                new NegatedRequestMatcher(new AntPathRequestMatcher("/customer/exchange-partner-token")),
                new AntPathRequestMatcher("/customer/**")
        );

        http
                .securityMatcher(affectedPaths)
                .csrf(AbstractHttpConfigurer::disable) // API calls, no need csrf
                .cors(AbstractHttpConfigurer::disable) // Handle this in API gateway
                .addFilterBefore(new VClubCustomerAuthenticationFilter(jwtService, new JwtAuthenticationConverter()), BasicAuthenticationFilter.class) // Filter
        ;

        return http.build();
    }

    @Bean
    public JwtDecoder jwtDecoder() {
        return NimbusJwtDecoder.withJwkSetUri(jwtIntegrationJwksUri).build();
    }

}
