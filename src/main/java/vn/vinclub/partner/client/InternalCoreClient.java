package vn.vinclub.partner.client;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.client.request.*;
import vn.vinclub.partner.client.response.CoreCustomerBalanceDto;
import vn.vinclub.partner.client.response.CoreCustomerDto;
import vn.vinclub.partner.client.response.CorePartnerTxnLookupResponse;
import vn.vinclub.partner.client.response.CoreVinClubPointTxnResponse;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.exception.RequestTimeOutException;
import vn.vinclub.partner.util.ServiceResponse;

import java.util.Collections;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class InternalCoreClient {

    private final RestTemplate restTemplate;

    @Value("${core-service.internal.endpoint}")
    private String internalEndpoint;

    @Value("${core-service.auth}")
    private String coreServiceAuth;


    @Profiler
    public CoreCustomerDto getCustomerById(Long id, boolean withTier, boolean withDemographicMetadata) {
        try {
            HttpEntity<Object> httpEntity = new HttpEntity<>(getDefaultHeaders());
            String url = UriComponentsBuilder.fromHttpUrl(internalEndpoint)
                    .path("/customers/{id}")
                    .queryParam("withTier", withTier)
                    .queryParam("withDemographicMetadata", withDemographicMetadata)
                    .buildAndExpand(id).encode().toUriString();

            ResponseEntity<String> exchangeResult = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<ServiceResponse<CoreCustomerDto>>() {
            }).getData();

        } catch (HttpStatusCodeException ex) {
            log.error("Error response when calling getCustomerById({})\tcode={}, body={}",
                    id,
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString()
            );
            throw new BusinessLogicException(ex.getResponseBodyAs(ServiceResponse.class));
        } catch (Exception e) {
            log.error("Exception occurs when calling getCustomerById({})\tmessage={}",
                    id,
                    e.getMessage(),
                    e
            );
            throw new BusinessLogicException(AppErrorCode.GET_CUSTOMER_PROFILE_UNEXPECTED_ERROR, e.getMessage());
        }
    }

    @Profiler
    public Optional<CoreCustomerDto> optCustomerByIdentityRequest(CoreCustomerIdentifyRequest request) {
        try {
            HttpEntity<CoreCustomerIdentifyRequest> httpEntity = new HttpEntity<>(request, getDefaultHeaders());
            String url = UriComponentsBuilder.fromHttpUrl(internalEndpoint)
                    .path("/customers/by_identify_request")
                    .build().encode().toUriString();

            ResponseEntity<String> exchangeResult = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            var result = JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<ServiceResponse<CoreCustomerDto>>() {
            });


            if (result.getCode() == 4019236) {
                return Optional.empty();
            }

            return Optional.ofNullable(result.getData());
        } catch (HttpStatusCodeException ex) {
            var responseBodyAs = ex.getResponseBodyAs(ServiceResponse.class);
            if (responseBodyAs != null && responseBodyAs.getCode() == 4019236) {
                return Optional.empty();
            }
            log.error("Error response when calling optCustomerByIdentityRequest({})\tcode={}, body={}",
                    JsonUtils.toString(request),
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString()
            );
            throw new BusinessLogicException(ex.getResponseBodyAs(ServiceResponse.class));
        } catch (Exception e) {
            log.error("Exception occurs when calling optCustomerByIdentityRequest({})\tmessage={}",
                    JsonUtils.toString(request),
                    e.getMessage(),
                    e
            );
            throw new BusinessLogicException(AppErrorCode.GET_CUSTOMER_PROFILE_UNEXPECTED_ERROR, e.getMessage());
        }
    }


    @Profiler
    public CoreCustomerBalanceDto getCustomerBalanceById(Long id) {
        ResponseEntity<String> exchangeResult = null;
        try {
            HttpEntity<Object> httpEntity = new HttpEntity<>(getDefaultHeaders());
            String url = UriComponentsBuilder.fromHttpUrl(internalEndpoint)
                    .path("/customers/{id}/point")
                    .buildAndExpand(id).encode().toUriString();

            exchangeResult = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<ServiceResponse<CoreCustomerBalanceDto>>() {
            }).getData();

        } catch (HttpStatusCodeException ex) {
            log.error("Error response when calling getCustomerBalanceById({})\tcode={}, body={}",
                    id,
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString()
            );
            throw new BusinessLogicException(ex.getResponseBodyAs(ServiceResponse.class));
        } catch (Exception e) {
            log.error("Exception occurs when calling getCustomerBalanceById({})\tmessage={}",
                    id,
                    e.getMessage(),
                    e
            );
            throw new BusinessLogicException(AppErrorCode.GET_CUSTOMER_BALANCE_UNEXPECTED_ERROR, e.getMessage());
        }
    }

    @Profiler
    public CoreVinClubPointTxnResponse doSwapVinClubPoint(Long partnerId, CoreSwapVinClubPointRequest request) {
        try {
            HttpEntity<CoreSwapVinClubPointRequest> httpEntity = new HttpEntity<>(request, getDefaultHeaders());
            String url = UriComponentsBuilder.fromHttpUrl(internalEndpoint)
                    .path("/partner/{partnerId}/point/swap")
                    .buildAndExpand(partnerId).encode().toUriString();

            ResponseEntity<String> exchangeResult = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);

            return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<ServiceResponse<CoreVinClubPointTxnResponse>>() {
            }).getData();

        } catch (HttpStatusCodeException ex) {
            String errorMsg = ex.getMessage();
            if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                ServiceResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<ServiceResponse<?>>() {});
                if (errorResponse != null) {
                    handlePointTxnError(errorResponse);
                    errorMsg = errorResponse.getFirstMessage();
                }
            }
            log.error("Error response when calling doSwapPoint({})\tcode={}, body={}",
                    JsonUtils.toString(request),
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString()
            );
            throw new BusinessLogicException(AppErrorCode.SWAP_POINT_UNEXPECTED_ERROR, errorMsg);
        } catch (ResourceAccessException ex) {
            log.error("Request timeout when calling doSwapPoint({})\tmessage={}",
                    JsonUtils.toString(request),
                    ex.getMessage(),
                    ex
            );
            throw new RequestTimeOutException(ex.getMessage());
        } catch (Exception ex) {
            if (ex instanceof BusinessLogicException) {
                throw (BusinessLogicException) ex;
            }
            log.error("Exception occurs when calling doSwapPoint({})\tmessage={}",
                    JsonUtils.toString(request),
                    ex.getMessage(),
                    ex
            );
            throw new BusinessLogicException(AppErrorCode.SWAP_POINT_UNEXPECTED_ERROR, ex.getMessage());
        }
    }

    @Profiler
    public CoreVinClubPointTxnResponse doTopUpVinClubPoint(Long partnerId, CoreTopUpVinClubPointRequest request) {
        try {
            HttpEntity<CoreTopUpVinClubPointRequest> httpEntity = new HttpEntity<>(request, getDefaultHeaders());
            String url = UriComponentsBuilder.fromHttpUrl(internalEndpoint)
                    .path("/partner/{partnerId}/point/topup")
                    .buildAndExpand(partnerId).encode().toUriString();

            ResponseEntity<String> exchangeResult = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);

            return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<ServiceResponse<CoreVinClubPointTxnResponse>>() {
            }).getData();

        } catch (HttpStatusCodeException ex) {
            String errorMsg = ex.getMessage();
            if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                ServiceResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<ServiceResponse<?>>() {});
                if (errorResponse != null) {
                    handlePointTxnError(errorResponse);
                    errorMsg = errorResponse.getFirstMessage();
                }
            }
            log.error("Error response when calling doTopUpVinClubPoint({})\tcode={}, body={}",
                    JsonUtils.toString(request),
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString()
            );
            throw new BusinessLogicException(AppErrorCode.TOPUP_POINT_UNEXPECTED_ERROR, errorMsg);
        } catch (ResourceAccessException ex) {
            log.error("Request timeout when calling doTopUpVinClubPoint({})\tmessage={}",
                    JsonUtils.toString(request),
                    ex.getMessage(),
                    ex
            );
            throw new RequestTimeOutException(ex.getMessage());
        } catch (Exception ex) {
            if (ex instanceof BusinessLogicException) {
                throw (BusinessLogicException) ex;
            }
            log.error("Exception occurs when calling doTopUpVinClubPoint({})\tmessage={}",
                    JsonUtils.toString(request),
                    ex.getMessage(),
                    ex
            );
            throw new BusinessLogicException(AppErrorCode.TOPUP_POINT_UNEXPECTED_ERROR, ex.getMessage());
        }
    }

    @Profiler
    public CoreVinClubPointTxnResponse doSpendVinClubPoint(Long partnerId, CoreSpendVinClubPointRequest request) {
        try {
            HttpEntity<CoreSpendVinClubPointRequest> httpEntity = new HttpEntity<>(request, getDefaultHeaders());
            String url = UriComponentsBuilder.fromHttpUrl(internalEndpoint)
                    .path("/partner/{partnerId}/point/spend")
                    .buildAndExpand(partnerId).encode().toUriString();

            ResponseEntity<String> exchangeResult = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);

            return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<ServiceResponse<CoreVinClubPointTxnResponse>>() {
            }).getData();

        } catch (HttpStatusCodeException ex) {
            String errorMsg = ex.getMessage();
            if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                ServiceResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<ServiceResponse<?>>() {});
                if (errorResponse != null) {
                    handlePointTxnError(errorResponse);
                    errorMsg = errorResponse.getFirstMessage();
                }
            }
            log.error("Error response when calling doSpendVinClubPoint({})\tcode={}, body={}",
                    JsonUtils.toString(request),
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString()
            );
            throw new BusinessLogicException(AppErrorCode.SPEND_POINT_UNEXPECTED_ERROR, errorMsg);
        } catch (ResourceAccessException ex) {
            log.error("Request timeout when calling doSpendVinClubPoint({})\tmessage={}",
                    JsonUtils.toString(request),
                    ex.getMessage(),
                    ex
            );
            throw new RequestTimeOutException(ex.getMessage());
        } catch (Exception ex) {
            if (ex instanceof BusinessLogicException) {
                throw (BusinessLogicException) ex;
            }
            log.error("Exception occurs when calling doSpendVinClubPoint({})\tmessage={}",
                    JsonUtils.toString(request),
                    ex.getMessage(),
                    ex
            );
            throw new BusinessLogicException(AppErrorCode.SPEND_POINT_UNEXPECTED_ERROR, ex.getMessage());
        }
    }

    @Profiler
    public CoreVinClubPointTxnResponse doRollbackVinClubPointTxn(Long partnerId, CoreRollbackVinClubPointRequest request) {
        try {
            HttpEntity<CoreRollbackVinClubPointRequest> httpEntity = new HttpEntity<>(request, getDefaultHeaders());
            String url = UriComponentsBuilder.fromHttpUrl(internalEndpoint)
                    .path("/partner/{partnerId}/point/rollback")
                    .buildAndExpand(partnerId).encode().toUriString();

            ResponseEntity<String> exchangeResult = restTemplate.exchange(url, HttpMethod.PUT, httpEntity, String.class);

            return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<ServiceResponse<CoreVinClubPointTxnResponse>>() {
            }).getData();

        } catch (HttpStatusCodeException ex) {
            String errorMsg = ex.getMessage();
            if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                ServiceResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<ServiceResponse<?>>() {});
                if (errorResponse != null) {
                    handlePointTxnError(errorResponse);
                    errorMsg = errorResponse.getFirstMessage();
                }
            }
            log.error("Error response when calling doRollbackVinClubPointTxn({})\tcode={}, body={}",
                    JsonUtils.toString(request),
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString()
            );
            throw new BusinessLogicException(AppErrorCode.ROLLBACK_POINT_TXN_UNEXPECTED_ERROR, errorMsg);
        } catch (ResourceAccessException ex) {
            log.error("Request timeout when calling doRollbackVinClubPointTxn({})\tmessage={}",
                    JsonUtils.toString(request),
                    ex.getMessage(),
                    ex
            );
            throw new RequestTimeOutException(ex.getMessage());
        } catch (Exception ex) {
            if (ex instanceof BusinessLogicException) {
                throw (BusinessLogicException) ex;
            }
            log.error("Exception occurs when calling doRollbackVinClubPointTxn({})\tmessage={}",
                    JsonUtils.toString(request),
                    ex.getMessage(),
                    ex
            );
            throw new BusinessLogicException(AppErrorCode.ROLLBACK_POINT_TXN_UNEXPECTED_ERROR, ex.getMessage());
        }
    }

    @Profiler
    public CorePartnerTxnLookupResponse lookupTransaction(Long partnerId, String transactionId) {
        try {
            HttpEntity<Object> httpEntity = new HttpEntity<>(getDefaultHeaders());
            String url = UriComponentsBuilder.fromHttpUrl(internalEndpoint)
                    .path("/partner/{partnerId}/point/transaction/{transactionId}")
                    .buildAndExpand(partnerId, transactionId).encode().toUriString();

            ResponseEntity<String> exchangeResult = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<ServiceResponse<CorePartnerTxnLookupResponse>>() {
            }).getData();

        } catch (HttpStatusCodeException ex) {
            if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                ServiceResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<ServiceResponse<?>>() {});
                if (errorResponse != null) {
                    handlePointTxnError(errorResponse);
                }
            }
            log.error("Error response when calling lookupTransaction({})\tcode={}, body={}",
                    transactionId,
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString()
            );
            throw new BusinessLogicException(ex.getResponseBodyAs(ServiceResponse.class));
        } catch (ResourceAccessException ex) {
            log.error("Request timeout when calling lookupTransaction({})\tmessage={}",
                    transactionId,
                    ex.getMessage(),
                    ex
            );
            throw new RequestTimeOutException(ex.getMessage());
        } catch (Exception e) {
            log.error("Exception occurs when calling lookupTransaction({})\tmessage={}",
                    transactionId,
                    e.getMessage(),
                    e
            );
            throw e;
        }
    }

    private void handlePointTxnError(ServiceResponse<?> errorResponse) {
        int errorCode = errorResponse.getCode();

        switch (errorCode) {
            case 4019236: // Customer with the specified ID does not exist
                throw new BusinessLogicException(AppErrorCode.CUSTOMER_NOT_EXIST);

            case 4019388: // Customer is invalid
                throw new BusinessLogicException(AppErrorCode.TRANSACTION_OWNER_MISMATCH);

            case 4014365: // Customer doesn't have enough points
            case 4040376: // Customer doesn't have a balance record
                throw new BusinessLogicException(AppErrorCode.INSUFFICIENT_POINTS);

            case 4040372: // The balance history record is invalid or not found
                throw new BusinessLogicException(AppErrorCode.INVALID_BALANCE_HISTORY);

            case 4017789: // The partner point transaction has already been processed
                throw new BusinessLogicException(AppErrorCode.TRANSACTION_ID_PROCESSED);

            case 4017790: // The partner point transaction does not exist
                throw new BusinessLogicException(AppErrorCode.TRANSACTION_NOT_FOUND);

            case 4017791: // The partner point transaction has already been rolled back
                throw new BusinessLogicException(AppErrorCode.POINT_TXN_ALREADY_ROLLED_BACK);

            case 4017792: // The partner point transaction cannot be rolled back
                throw new BusinessLogicException(AppErrorCode.POINT_TXN_CANNOT_ROLLBACK);

            default:
                // For any other error codes, throw a generic error outside the method
        }
    }

    private HttpHeaders getDefaultHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, AppConst.CONTENT_TYPE.JSON);
        headers.put(HttpHeaders.AUTHORIZATION, Collections.singletonList(coreServiceAuth));
        return headers;
    }
}
