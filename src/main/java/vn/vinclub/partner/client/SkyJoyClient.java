package vn.vinclub.partner.client;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.client.request.*;
import vn.vinclub.partner.client.response.*;
import vn.vinclub.partner.config.PartnerConfig;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.exception.RequestTimeOutException;
import vn.vinclub.partner.exception.UnauthorizedException;
import vn.vinclub.partner.util.ServiceResponse;

import java.time.Duration;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;

@Component
@RequiredArgsConstructor
@Slf4j
public class SkyJoyClient {

    private static final String SERVICE_NAME = "SKYJOY";

    private static final int DELAY_TOKEN_EXPIRED_TIME_IN_SECONDS = 60;

    private static final String API_LINK_ACCOUNT_V1 = "/api-user/partner/v1/user/link-member";

    private static final String API_LINK_ACCOUNT_V2 = "/api-user/partner/v2/user/link-member";

    private static final String API_UNLINK_ACCOUNT = "/api-user/partner/v1/user/unlink-member";

    private static final String API_GET_CUSTOMER_BALANCE = "/api-point/partner/v1/user-balance/{partnerUserId}";

    private static final String API_GET_CUSTOMER_PROFILE = "/api-user/partner/v1/user-profile/{partnerUserId}";

    private static final String API_POINT_ACCRUAL = "/api-point/partner/v1/point-accrual";

    private static final String API_LOOKUP_TRANSACTION = "/api-point/partner/v1/lookup-transaction-id";

    private final PartnerConfig.SkyJoy config;
    private final ExternalHttpClient externalHttpClient;
    private final RedissonClient redissonClient;

    @Profiler
    public SkyJoyExchangeTokenResponse exchangeTokenByCode(SkyJoyExchangeTokenRequest requestBody) {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, AppConst.CONTENT_TYPE.FORM_URLENCODED);
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("client_id", requestBody.getClientId());
        formData.add("grant_type", requestBody.getGrantType());
        formData.add("code", requestBody.getCode());
        formData.add("redirect_uri", requestBody.getRedirectUri());

        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);

        String url = config.getLinkAccount().getTokenUrl();

        try {
            return externalHttpClient.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    new ParameterizedTypeReference<SkyJoyExchangeTokenResponse>() {
                    },
                    SERVICE_NAME,
                    "EXCHANGE_TOKEN_BY_CODE"
            ).getBody();
        } catch (HttpStatusCodeException ex) {
            log.error("Error response when calling exchangeTokenByCode({})\tcode={}, body={}",
                    JsonUtils.toString(requestBody),
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString()
            );
            throw new BusinessLogicException(AppErrorCode.EXCHANGE_TOKEN_BY_CODE_ERROR, ex.getMessage());
        } catch (Exception e) {
            log.error("Exception occurs when calling exchangeTokenByCode({})\tmessage={}", JsonUtils.toString(requestBody), e.getMessage(), e);
            throw new BusinessLogicException(AppErrorCode.PARTNER_API_FAILURE, e.getMessage());
        }

    }


    @Profiler
    public SkyJoyLinkAccountResponse linkAccountByOauthToken(SkyJoyLinkAccountByOauthRequest requestBody) {
        return executeWithRetryOnUnauthorized(() -> {
            HttpHeaders headers = getDefaultHeaders();
            String url = UriComponentsBuilder.fromHttpUrl(getBaseUrl())
                    .path(API_LINK_ACCOUNT_V2)
                    .build().encode().toUriString();

            HttpEntity<SkyJoyLinkAccountByOauthRequest> requestEntity = new HttpEntity<>(requestBody, headers);

            try {
                // Execute POST request
                ResponseEntity<String> exchangeResult = externalHttpClient.exchange(
                        url,
                        HttpMethod.POST,
                        requestEntity,
                        String.class,
                        SERVICE_NAME,
                        "LINK_ACCOUNT_BY_OAUTH_TOKEN"
                );
                return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<SkyJoyBaseResponse<SkyJoyLinkAccountResponse>>() {
                }).getData();
            } catch (HttpStatusCodeException ex) {
                if (ex.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                    throw new UnauthorizedException();
                }

                log.error("Error response when calling linkAccountByOauthToken({})\tcode={}, body={}",
                        JsonUtils.toString(requestBody),
                        ex.getStatusCode(),
                        ex.getResponseBodyAsString()
                );
                if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                    SkyJoyBaseResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<SkyJoyBaseResponse<?>>() {
                    });
                    String errorCode = Optional.ofNullable(errorResponse).map(SkyJoyBaseResponse::getErrors)
                            .map(SkyJoyBaseResponse.Error::getCode)
                            .orElse("UNKNOWN_ERROR");

                    handleErrorCode(errorCode, ex.getResponseBodyAsString());

                    throw new BusinessLogicException(AppErrorCode.LINK_ACCOUNT_UNEXPECTED_ERROR, errorCode);
                }
                throw new BusinessLogicException(AppErrorCode.LINK_ACCOUNT_UNEXPECTED_ERROR, ex.getMessage());
            } catch (Exception ex) {
                log.error("Exception occurs when calling linkAccountByOauthToken({})\tmessage={}", JsonUtils.toString(requestBody), ex.getMessage(), ex);
                throw new BusinessLogicException(AppErrorCode.PARTNER_API_FAILURE, ex.getMessage());
            }
        });
    }

    @Profiler
    public SkyJoyLinkAccountResponse linkAccount(SkyJoyLinkAccountRequest requestBody) {
        return executeWithRetryOnUnauthorized(() -> {
            HttpHeaders headers = getDefaultHeaders();
            String url = UriComponentsBuilder.fromHttpUrl(getBaseUrl())
                    .path(API_LINK_ACCOUNT_V1)
                    .build().encode().toUriString();

            HttpEntity<SkyJoyLinkAccountRequest> requestEntity = new HttpEntity<>(requestBody, headers);

            try {
                // Execute POST request
                ResponseEntity<String> exchangeResult = externalHttpClient.exchange(
                        url,
                        HttpMethod.POST,
                        requestEntity,
                        String.class,
                        SERVICE_NAME,
                        "LINK_ACCOUNT"
                );
                return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<SkyJoyBaseResponse<SkyJoyLinkAccountResponse>>() {
                }).getData();
            } catch (HttpStatusCodeException ex) {
                if (ex.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                    throw new UnauthorizedException();
                }

                log.error("Error response when calling linkAccount({})\tcode={}, body={}",
                        JsonUtils.toString(requestBody),
                        ex.getStatusCode(),
                        ex.getResponseBodyAsString()
                );
                if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                    SkyJoyBaseResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<SkyJoyBaseResponse<?>>() {
                    });
                    String errorCode = Optional.ofNullable(errorResponse).map(SkyJoyBaseResponse::getErrors)
                            .map(SkyJoyBaseResponse.Error::getCode)
                            .orElse("UNKNOWN_ERROR");

                    handleErrorCode(errorCode, ex.getResponseBodyAsString());

                    throw new BusinessLogicException(AppErrorCode.LINK_ACCOUNT_UNEXPECTED_ERROR, errorCode);
                }
                throw new BusinessLogicException(AppErrorCode.LINK_ACCOUNT_UNEXPECTED_ERROR, ex.getMessage());
            } catch (Exception ex) {
                log.error("Exception occurs when calling linkAccount({})\tmessage={}", JsonUtils.toString(requestBody), ex.getMessage(), ex);
                throw new BusinessLogicException(AppErrorCode.PARTNER_API_FAILURE, ex.getMessage());
            }
        });
    }

    @Profiler
    public boolean unlinkAccount(SkyJoyUnLinkAccountRequest requestBody) {
        return executeWithRetryOnUnauthorized(() -> {
            HttpHeaders headers = getDefaultHeaders();
            String url = UriComponentsBuilder.fromHttpUrl(getBaseUrl())
                    .path(API_UNLINK_ACCOUNT)
                    .build().encode().toUriString();

            HttpEntity<SkyJoyUnLinkAccountRequest> requestEntity = new HttpEntity<>(requestBody, headers);

            try {
                // Execute POST request
                ResponseEntity<String> exchangeResult = externalHttpClient.exchange(
                        url,
                        HttpMethod.POST,
                        requestEntity,
                        String.class,
                        SERVICE_NAME,
                        "UNLINK_ACCOUNT"
                );
                return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<SkyJoyBaseResponse<Boolean>>() {
                }).getData();
            } catch (HttpStatusCodeException ex) {
                if (ex.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                    throw new UnauthorizedException();
                }

                log.error("Error response when calling unlinkAccount({})\tcode={}, body={}",
                        JsonUtils.toString(requestBody),
                        ex.getStatusCode(),
                        ex.getResponseBodyAsString()
                );
                if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                    SkyJoyBaseResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<SkyJoyBaseResponse<?>>() {
                    });
                    String errorCode = Optional.ofNullable(errorResponse).map(SkyJoyBaseResponse::getErrors)
                            .map(SkyJoyBaseResponse.Error::getCode)
                            .orElse("UNKNOWN_ERROR");

                    handleErrorCode(errorCode, ex.getResponseBodyAsString());

                    throw new BusinessLogicException(AppErrorCode.UNLINK_ACCOUNT_UNEXPECTED_ERROR, errorCode);
                }
                throw new BusinessLogicException(AppErrorCode.UNLINK_ACCOUNT_UNEXPECTED_ERROR, ex.getMessage());
            } catch (Exception ex) {
                log.error("Exception occurs when calling unlinkAccount({})\tmessage={}", JsonUtils.toString(requestBody), ex.getMessage(), ex);
                throw new BusinessLogicException(AppErrorCode.PARTNER_API_FAILURE, ex.getMessage());
            }
        });
    }

    @Profiler
    public SkyJoyCustomerBalanceResponse getCustomerBalance(String skyjoyId) {
        return executeWithRetryOnUnauthorized(() -> {
            HttpHeaders headers = getDefaultHeaders();
            String url = UriComponentsBuilder.fromHttpUrl(getBaseUrl())
                    .path(API_GET_CUSTOMER_BALANCE)
                    .buildAndExpand(skyjoyId).encode().toUriString();

            HttpEntity<?> requestEntity = new HttpEntity<>(headers);

            try {
                // Execute GET request
                ResponseEntity<String> exchangeResult = externalHttpClient.exchange(
                        url,
                        HttpMethod.GET,
                        requestEntity,
                        String.class,
                        SERVICE_NAME,
                        "GET_CUSTOMER_BALANCE"
                );
                return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<SkyJoyBaseResponse<SkyJoyCustomerBalanceResponse>>() {
                }).getData();
            } catch (HttpStatusCodeException ex) {
                if (ex.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                    throw new UnauthorizedException();
                }

                log.error("Error response when calling getCustomerBalance({})\tcode={}, body={}",
                        skyjoyId,
                        ex.getStatusCode(),
                        ex.getResponseBodyAsString()
                );
                if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                    SkyJoyBaseResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<SkyJoyBaseResponse<?>>() {
                    });
                    String errorCode = Optional.ofNullable(errorResponse).map(SkyJoyBaseResponse::getErrors)
                            .map(SkyJoyBaseResponse.Error::getCode)
                            .orElse("UNKNOWN_ERROR");

                    handleErrorCode(errorCode, ex.getResponseBodyAsString());

                    throw new BusinessLogicException(AppErrorCode.GET_PARTNER_CUSTOMER_BALANCE_ERROR, errorCode);
                }
                throw new BusinessLogicException(AppErrorCode.GET_PARTNER_CUSTOMER_BALANCE_ERROR, ex.getMessage());
            } catch (Exception e) {
                log.error("Exception occurs when calling getCustomerBalance({})\tmessage={}", skyjoyId, e.getMessage(), e);
                throw new BusinessLogicException(AppErrorCode.PARTNER_API_FAILURE, e.getMessage());
            }
        });
    }

    @Profiler
    public SkyJoyCustomerProfileResponse getCustomerProfile(String skyjoyId) {
        return executeWithRetryOnUnauthorized(() -> {
            HttpHeaders headers = getDefaultHeaders();
            String url = UriComponentsBuilder.fromHttpUrl(getBaseUrl())
                    .path(API_GET_CUSTOMER_PROFILE)
                    .buildAndExpand(skyjoyId).encode().toUriString();

            HttpEntity<?> requestEntity = new HttpEntity<>(headers);

            try {
                // Execute GET request
                ResponseEntity<String> exchangeResult = externalHttpClient.exchange(
                        url,
                        HttpMethod.GET,
                        requestEntity,
                        String.class,
                        SERVICE_NAME,
                        "GET_CUSTOMER_PROFILE"
                );
                return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<SkyJoyBaseResponse<SkyJoyCustomerProfileResponse>>() {
                }).getData();
            } catch (HttpStatusCodeException ex) {
                if (ex.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                    throw new UnauthorizedException();
                }

                log.error("Error response when calling getCustomerProfile({})\tcode={}, body={}",
                        skyjoyId,
                        ex.getStatusCode(),
                        ex.getResponseBodyAsString()
                );
                if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                    SkyJoyBaseResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<SkyJoyBaseResponse<?>>() {
                    });
                    String errorCode = Optional.ofNullable(errorResponse).map(SkyJoyBaseResponse::getErrors)
                            .map(SkyJoyBaseResponse.Error::getCode)
                            .orElse("UNKNOWN_ERROR");

                    handleErrorCode(errorCode, ex.getResponseBodyAsString());

                    throw new BusinessLogicException(AppErrorCode.GET_PARTNER_CUSTOMER_PROFILE_ERROR, errorCode);
                }
                throw new BusinessLogicException(AppErrorCode.GET_PARTNER_CUSTOMER_PROFILE_ERROR, ex.getMessage());
            } catch (Exception e) {
                log.error("Exception occurs when calling getCustomerProfile({})\tmessage={}", skyjoyId, e.getMessage(), e);
                throw new BusinessLogicException(AppErrorCode.PARTNER_API_FAILURE, e.getMessage());
            }
        });
    }

    @Profiler
    public SkyJoyPointAccrualResponse pointAccrual(SkyJoyPointAccrualRequest requestBody) {
        return executeWithRetryOnUnauthorized(() -> {
            HttpHeaders headers = getDefaultHeaders();
            String url = UriComponentsBuilder.fromHttpUrl(getBaseUrl())
                    .path(API_POINT_ACCRUAL)
                    .build().encode().toUriString();

            HttpEntity<SkyJoyPointAccrualRequest> requestEntity = new HttpEntity<>(requestBody, headers);

            try {
                // Execute POST request
                ResponseEntity<String> exchangeResult = externalHttpClient.exchange(
                        url,
                        HttpMethod.POST,
                        requestEntity,
                        String.class,
                        SERVICE_NAME,
                        "POINT_ACCRUAL"
                );
                SkyJoyPointAccrualResponse response = JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<SkyJoyBaseResponse<SkyJoyPointAccrualResponse>>() {
                }).getData();
                response.setRawResponse(exchangeResult.getBody());
                return response;
            } catch (HttpStatusCodeException ex) {
                if (ex.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                    throw new UnauthorizedException();
                }

                log.error("Error response when calling pointAccrual({})\tcode={}, body={}}",
                        JsonUtils.toString(requestEntity),
                        ex.getStatusCode(),
                        ex.getResponseBodyAsString()
                );
                if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                    SkyJoyBaseResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<SkyJoyBaseResponse<?>>() {
                    });
                    String errorCode = Optional.ofNullable(errorResponse).map(SkyJoyBaseResponse::getErrors)
                            .map(SkyJoyBaseResponse.Error::getCode)
                            .orElse("UNKNOWN_ERROR");

                    handleErrorCode(errorCode, ex.getResponseBodyAsString());

                    throw new BusinessLogicException(ex.getResponseBodyAsString(), AppErrorCode.POINT_TOPUP_UNEXPECTED_ERROR, errorCode);
                }
                throw new BusinessLogicException(ex.getResponseBodyAsString(), AppErrorCode.POINT_TOPUP_UNEXPECTED_ERROR, ex.getMessage());
            } catch (ResourceAccessException e) {
                log.error("Request timeout when calling pointAccrual({})\tmessage={}",
                        JsonUtils.toString(requestBody),
                        e.getMessage(),
                        e
                );
                throw new RequestTimeOutException(e.getMessage());
            } catch (Exception ex) {
                log.error("Exception occurs when calling pointAccrual({})\tmessage={}", JsonUtils.toString(requestBody), ex.getMessage(), ex);
                throw new BusinessLogicException(AppErrorCode.PARTNER_API_FAILURE, ex.getMessage());
            }
        });
    }

    @Profiler
    public SkyJoyLookupTxnResponse lookupTxn(SkyJoyLookupTxnRequest requestBody) {
        return executeWithRetryOnUnauthorized(() -> {
            HttpHeaders headers = getDefaultHeaders();
            String url = UriComponentsBuilder.fromHttpUrl(getBaseUrl())
                    .path(API_LOOKUP_TRANSACTION)
                    .build().encode().toUriString();
            HttpEntity<SkyJoyLookupTxnRequest> requestEntity = new HttpEntity<>(requestBody, headers);

            try {
                // Execute POST request
                ResponseEntity<String> exchangeResult = externalHttpClient.exchange(
                        url,
                        HttpMethod.POST,
                        requestEntity,
                        String.class,
                        SERVICE_NAME,
                        "LOOKUP_TRANSACTION"
                );
                var response = JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<SkyJoyBaseResponse<SkyJoyLookupTxnResponse>>() {
                }).getData();
                response.setRawResponse(exchangeResult.getBody());
                return response;
            } catch (HttpStatusCodeException ex) {
                if (ex.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                    throw new UnauthorizedException();
                }

                log.error("Error response when calling lookupTxn({})\tcode={}, body={}",
                        JsonUtils.toString(requestBody),
                        ex.getStatusCode(),
                        ex.getResponseBodyAsString()
                );
                if (ex.getStatusCode().is4xxClientError() || ex.getStatusCode().is5xxServerError()) {
                    SkyJoyBaseResponse<?> errorResponse = ex.getResponseBodyAs(new ParameterizedTypeReference<SkyJoyBaseResponse<?>>() {
                    });
                    String errorCode = Optional.ofNullable(errorResponse).map(SkyJoyBaseResponse::getErrors)
                            .map(SkyJoyBaseResponse.Error::getCode)
                            .orElse("UNKNOWN_ERROR");

                    handleErrorCode(errorCode, ex.getResponseBodyAsString());

                    throw new BusinessLogicException(AppErrorCode.LOOKUP_TRANSACTION_ERROR, errorCode);
                }
                throw new BusinessLogicException(AppErrorCode.LOOKUP_TRANSACTION_ERROR, ex.getMessage());
            }
        });
    }

    /**
     * Execute an API call with retry logic for 401 Unauthorized responses.
     * If a 401 response is received, the token is refreshed and the API call is retried once.
     *
     * @param apiCall The API call to execute
     * @param <T>     The return type of the API call
     * @return The result of the API call
     */
    private <T> T executeWithRetryOnUnauthorized(Supplier<T> apiCall) {
        try {
            return apiCall.get();
        } catch (UnauthorizedException ex) {
            log.info("Received 401 Unauthorized response, refreshing token and retrying...");
            try {
                refreshToken();
                return apiCall.get();
            } catch (UnauthorizedException e) {
                log.error("Failed to authenticate after retrying, message={}", e.getMessage());
                throw new BusinessLogicException(AppErrorCode.PARTNER_API_FAILURE, e.getMessage());
            }
        }
    }

    private HttpHeaders getDefaultHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, AppConst.CONTENT_TYPE.JSON);
        headers.add(HttpHeaders.AUTHORIZATION, AppConst.TOKEN_PREFIX.BEARER + " " + getAccessToken());
        return headers;
    }

    @Profiler
    private String getAccessToken() {
        String accessToken = getFromCache(getAccessTokenCacheKey());
        if (StringUtils.hasText(accessToken)) {
            return accessToken;
        }

        refreshToken();
        return getFromCache(getAccessTokenCacheKey());
    }

    @Profiler
    private void refreshToken() {
        String refreshToken = getFromCache(getRefreshTokenCacheKey());
        if (StringUtils.hasText(refreshToken)) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add(AppConst.OAUTH2_REQUEST_PARAM.GRANT_TYPE, AppConst.OAUTH2_GRANT_TYPE.REFRESH_TOKEN);
            body.add(AppConst.OAUTH2_REQUEST_PARAM.CLIENT_ID, config.getAuth().getClientId());
            body.add(AppConst.OAUTH2_REQUEST_PARAM.CLIENT_SECRET, config.getAuth().getClientSecret());
            body.add(AppConst.OAUTH2_REQUEST_PARAM.REFRESH_TOKEN, refreshToken);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
            try {
                ResponseEntity<String> response = externalHttpClient.exchange(
                        config.getAuth().getAccessTokenUrl(),
                        HttpMethod.POST,
                        request,
                        String.class,
                        SERVICE_NAME,
                        "REFRESH_TOKEN"
                );
                if (HttpStatus.OK.equals(response.getStatusCode())) {
                    storeTokens(response.getBody());
                    return;
                }
                throw new RuntimeException("Cannot refresh token with http status code = " + response.getStatusCode());
            } catch (Exception e) {
                log.error("[refreshTokens] cannot refresh token, partnerCode={}, error={}", getPartnerCode(), e.getMessage());
                obtainNewTokens();
                return;
            }

        }
        // If refresh token is null or expired, obtain new tokens
        obtainNewTokens();
    }


    @Profiler
    private void obtainNewTokens() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add(AppConst.OAUTH2_REQUEST_PARAM.GRANT_TYPE, AppConst.OAUTH2_GRANT_TYPE.PASSWORD);
        body.add(AppConst.OAUTH2_REQUEST_PARAM.CLIENT_ID, config.getAuth().getClientId());
        body.add(AppConst.OAUTH2_REQUEST_PARAM.CLIENT_SECRET, config.getAuth().getClientSecret());
        body.add(AppConst.OAUTH2_REQUEST_PARAM.USERNAME, config.getAuth().getUsername());
        body.add(AppConst.OAUTH2_REQUEST_PARAM.PASSWORD, config.getAuth().getPassword());

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
        try {
            ResponseEntity<String> response = externalHttpClient.exchange(
                    config.getAuth().getAccessTokenUrl(),
                    HttpMethod.POST,
                    request,
                    String.class,
                    SERVICE_NAME,
                    "ISSUE_TOKEN"
            );
            if (HttpStatus.OK.equals(response.getStatusCode())) {
                storeTokens(response.getBody());
                return;
            }
            throw new RuntimeException("Cannot obtain new tokens with http status code = " + response.getStatusCode());
        } catch (Exception e) {
            log.error("[obtainNewTokens] cannot obtain new tokens, partnerCode={}, error={}", getPartnerCode(), e.getMessage());
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.AUTH_OAUTH2_OBTAIN_TOKEN_ERROR));
        }
    }

    @Profiler
    private void storeTokens(String tokenResponse) {
        Map<String, Object> response = JsonUtils.toObject(tokenResponse, new TypeReference<Map<String, Object>>() {
        });

        if (MapUtils.isEmpty(response)) {
            log.error("[storeTokens] response is null, partnerCode={}", getPartnerCode());
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.AUTH_OAUTH2_ERROR));
        }

        String accessToken = (String) response.get(AppConst.OAUTH2_RESPONSE_PARAM.ACCESS_TOKEN);
        String refreshToken = (String) response.get(AppConst.OAUTH2_RESPONSE_PARAM.REFRESH_TOKEN);
        int expiresIn = (Integer) response.get(AppConst.OAUTH2_RESPONSE_PARAM.EXPIRES_IN);
        int refreshExpiresIn = (Integer) response.get(AppConst.OAUTH2_RESPONSE_PARAM.REFRESH_EXPIRES_IN);

        putToCache(getAccessTokenCacheKey(), accessToken, calculateTtlWithDelay(expiresIn));
        putToCache(getRefreshTokenCacheKey(), refreshToken, calculateTtlWithDelay(refreshExpiresIn));

    }

    private String getAccessTokenCacheKey() {
        return String.format("partner_svc_access_token:%s", getPartnerCode());
    }

    private String getRefreshTokenCacheKey() {
        return String.format("partner_svc_refresh_token:%s", getPartnerCode());
    }

    private String getFromCache(String key) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }

    private void putToCache(String key, String value, int ttlInSeconds) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.set(value, Duration.ofSeconds(ttlInSeconds));
    }

    private int calculateTtlWithDelay(int expiresIn) {
        return expiresIn - DELAY_TOKEN_EXPIRED_TIME_IN_SECONDS;
    }

    private String getPartnerCode() {
        return config.getPartnerCode();
    }

    private String getBaseUrl() {
        return config.getApi().getBaseUrl();
    }

    private void handleErrorCode(String errorCode, String responseBodyAsString) {
        if ("PARTNER_MEMBER_ID_ALREADY_LINKED".equals(errorCode)) {
            throw new BusinessLogicException(responseBodyAsString, AppErrorCode.ACCOUNT_LINKED);
        }

        if ("EXTERNAL_ID_ALREADY_EXIST".equals(errorCode)) {
            throw new BusinessLogicException(responseBodyAsString, AppErrorCode.VINCLUB_ACCOUNT_LINKED);
        }

        if ("MEMBER_IS_LINKED".equals(errorCode)) {
            throw new BusinessLogicException(responseBodyAsString, AppErrorCode.PARTNER_ACCOUNT_LINKED);
        }

        if ("PARTNER_MEMBER_ID_NOT_LINKED".equals(errorCode)) {
            throw new BusinessLogicException(responseBodyAsString, AppErrorCode.ACCOUNT_NOT_LINKED);
        }

        if ("USER_NOT_FOUND".equals(errorCode) || "USER_INFO_NOT_FOUND".equals(errorCode)) {
            throw new BusinessLogicException(responseBodyAsString, AppErrorCode.PARTNER_USER_NOT_FOUND);
        }

        if ("TRANSACTION_NOT_FOUND".equals(errorCode)) {
            throw new BusinessLogicException(responseBodyAsString, AppErrorCode.TRANSACTION_NOT_FOUND);
        }

        if ("RELINK_ACCOUNT_MISMATCH".equals(errorCode)) {
            throw new BusinessLogicException(responseBodyAsString, AppErrorCode.RELINK_ACCOUNT_MISMATCH);
        }

        if ("TRANSACTION_CREATE_FAILED".equals(errorCode)) {
            throw new BusinessLogicException(responseBodyAsString, AppErrorCode.POINT_TOPUP_PARTNER_TRANSACTION_CREATED_FAILED);
        }

        if ("TRANSACTION_ALREADY_PROCESSED".equals(errorCode)) {
            throw new BusinessLogicException(responseBodyAsString, AppErrorCode.TRANSACTION_ID_PROCESSED);
        }
    }
}
