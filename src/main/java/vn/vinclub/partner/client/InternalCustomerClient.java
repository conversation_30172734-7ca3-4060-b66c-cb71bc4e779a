package vn.vinclub.partner.client;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.util.ServiceResponse;

import java.util.Collections;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class InternalCustomerClient {

    private static final String API_KEY_HEADER = "integrationkey";

    private static final String ADDITIONAL_CLAIMS_KEY = "additional_claims";

    private final RestTemplate restTemplate;

    @Value("${customer-service.internal.endpoint}")
    private String internalEndpoint;

    @Value("${customer-service.integration-key}")
    private String integrationKey;

    private HttpHeaders getDefaultHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, AppConst.CONTENT_TYPE.JSON);
        headers.put(API_KEY_HEADER, Collections.singletonList(integrationKey));

        return headers;
    }

    @Profiler
    public ObjectNode internalLoginByCustomerId(Long id, Map<String, Object> additionalClaims) {
        try {
            ObjectNode body = JsonNodeFactory.instance.objectNode();
            body.putPOJO(ADDITIONAL_CLAIMS_KEY, additionalClaims);

            HttpEntity<ObjectNode> httpEntity = new HttpEntity<>(body, getDefaultHeaders());
            String url = UriComponentsBuilder.fromHttpUrl(internalEndpoint)
                    .path("/user/{id}/login")
                    .buildAndExpand(id).encode().toUriString();

            ResponseEntity<ObjectNode> exchangeResult = restTemplate.exchange(url, HttpMethod.POST, httpEntity, ObjectNode.class);
            return exchangeResult.getBody();

        } catch (HttpStatusCodeException ex) {
            log.error("Error response when calling internalLoginByCustomerId({})\tcode={}, body={}",
                    id,
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString()
            );
            throw new BusinessLogicException(ex.getResponseBodyAs(ServiceResponse.class));
        }catch (Exception e) {
            log.error("Exception occurs when calling internalLoginByCustomerId({})\tmessage={}", id, e.getMessage(), e);
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INTERNAL_API_CALL_FAILED, "internalLoginByCustomerId: " + e.getMessage()));
        }
    }
}
