package vn.vinclub.partner.client;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.domain.event.audit.IntegrationLogEvent;
import vn.vinclub.partner.service.AuditLogService;
import vn.vinclub.partner.util.SensitiveDataMasker;

import java.util.Map;

/**
 * Wrapper around RestTemplate that automatically logs integration requests and responses
 * for audit purposes using the IntegrationLogEvent.
 * 
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ExternalHttpClient {
    private static final String UNKNOWN_ACTION = "UNKNOWN_ACTION";

    private final RestTemplate restTemplate;
    private final AuditLogService auditLogService;

    /**
     * Execute HTTP exchange with automatic integration logging
     */
    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, HttpEntity<?> requestEntity,
                                         Class<T> responseType, String serviceName) {
        return exchange(url, method, requestEntity, responseType, serviceName, UNKNOWN_ACTION);
    }

    /**
     * Execute HTTP exchange with ParameterizedTypeReference and automatic integration logging
     */
    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, HttpEntity<?> requestEntity,
                                         ParameterizedTypeReference<T> responseType, String serviceName) {
        return exchange(url, method, requestEntity, responseType, serviceName, UNKNOWN_ACTION);
    }

    /**
     * Execute HTTP exchange with ParameterizedTypeReference and custom action name
     */
    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, HttpEntity<?> requestEntity,
                                         ParameterizedTypeReference<T> responseType, String serviceName, String action) {

        long requestTime = System.currentTimeMillis();

        IntegrationLogEvent.IntegrationLogEventBuilder eventBuilder = IntegrationLogEvent.builder()
                .service(serviceName)
                .action(action)
                .url(url)
                .protocol("HTTP")
                .method(method.name())
                .requestTime(requestTime)
                .status(AppConst.SUCCESS);

        // Capture request data
        try {
            eventBuilder.requestHeader(serializeHeaders(requestEntity.getHeaders()));
            eventBuilder.requestData(serializeRequestBody(requestEntity.getBody()));
        } catch (Exception e) {
            log.warn("Failed to serialize request data for integration logging", e);
        }

        ResponseEntity<T> response = null;
        Exception exception = null;

        try {
            // Execute the actual HTTP request
            response = restTemplate.exchange(url, method, requestEntity, responseType);
            return response;

        } catch (Exception e) {
            exception = e;
            throw e;

        } finally {
            // Log the integration activity
            long responseTime = System.currentTimeMillis();

            try {
                eventBuilder.responseTime(responseTime);

                if (response != null) {
                    // Success case
                    eventBuilder.responseCode(String.valueOf(response.getStatusCode().value()));
                    eventBuilder.responseHeader(serializeHeaders(response.getHeaders()));
                    eventBuilder.responseData(serializeResponseBody(response.getBody()));
                    eventBuilder.message("Request completed successfully");
                } else if (exception != null) {
                    // Error case
                    eventBuilder.status(AppConst.ERROR);
                    eventBuilder.message("Request failed: " + exception.getMessage());

                    if (exception instanceof org.springframework.web.client.HttpStatusCodeException) {
                        org.springframework.web.client.HttpStatusCodeException httpEx =
                            (org.springframework.web.client.HttpStatusCodeException) exception;
                        eventBuilder.responseCode(String.valueOf(httpEx.getStatusCode().value()));
                        eventBuilder.responseData(httpEx.getResponseBodyAsString());
                        eventBuilder.responseHeader(serializeHeaders(httpEx.getResponseHeaders()));
                    }
                }

                IntegrationLogEvent event = eventBuilder.build();
                auditLogService.logIntegrationLog(event);

            } catch (Exception e) {
                log.error("Failed to log integration activity", e);
            }
        }
    }

    /**
     * Execute HTTP exchange with automatic integration logging and custom action name
     */
    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, HttpEntity<?> requestEntity,
                                         Class<T> responseType, String serviceName, String action) {
        
        long requestTime = System.currentTimeMillis();
        
        IntegrationLogEvent.IntegrationLogEventBuilder eventBuilder = IntegrationLogEvent.builder()
                .service(serviceName)
                .action(action)
                .url(url)
                .protocol("HTTP")
                .method(method.name())
                .requestTime(requestTime)
                .status(AppConst.SUCCESS);

        // Capture request data
        try {
            eventBuilder.requestHeader(serializeHeaders(requestEntity.getHeaders()));
            eventBuilder.requestData(serializeRequestBody(requestEntity.getBody()));
        } catch (Exception e) {
            log.warn("Failed to serialize request data for integration logging", e);
        }

        ResponseEntity<T> response = null;
        Exception exception = null;
        
        try {
            // Execute the actual HTTP request
            response = restTemplate.exchange(url, method, requestEntity, responseType);
            return response;
            
        } catch (Exception e) {
            exception = e;
            throw e;
            
        } finally {
            // Log the integration activity
            long responseTime = System.currentTimeMillis();
            
            try {
                eventBuilder.responseTime(responseTime);
                
                if (response != null) {
                    // Success case
                    eventBuilder.responseCode(String.valueOf(response.getStatusCode().value()));
                    eventBuilder.responseHeader(serializeHeaders(response.getHeaders()));
                    eventBuilder.responseData(serializeResponseBody(response.getBody()));
                    eventBuilder.message("Request completed successfully");
                } else if (exception != null) {
                    // Error case
                    eventBuilder.status(AppConst.ERROR);
                    eventBuilder.message("Request failed: " + exception.getMessage());
                    
                    if (exception instanceof HttpStatusCodeException httpEx) {
                        eventBuilder.responseCode(String.valueOf(httpEx.getStatusCode().value()));
                        eventBuilder.responseData(httpEx.getResponseBodyAsString());
                        eventBuilder.responseHeader(serializeHeaders(httpEx.getResponseHeaders()));
                    }
                }
                
                IntegrationLogEvent event = eventBuilder.build();
                auditLogService.logIntegrationLog(event);
                
            } catch (Exception e) {
                log.error("Failed to log integration activity", e);
            }
        }
    }

    /**
     * Execute HTTP exchange with URI and path variables
     */
    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, HttpEntity<?> requestEntity,
                                         Class<T> responseType, String serviceName, Map<String, ?> uriVariables) {
        return exchange(url, method, requestEntity, responseType, serviceName, UNKNOWN_ACTION, uriVariables);
    }

    /**
     * Execute HTTP exchange with URI, path variables and custom action
     */
    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, HttpEntity<?> requestEntity,
                                         Class<T> responseType, String serviceName, String action,
                                         Map<String, ?> uriVariables) {
        
        long requestTime = System.currentTimeMillis();
        
        IntegrationLogEvent.IntegrationLogEventBuilder eventBuilder = IntegrationLogEvent.builder()
                .service(serviceName)
                .action(action)
                .url(url)
                .protocol("HTTP")
                .method(method.name())
                .requestTime(requestTime)
                .status(AppConst.SUCCESS);

        // Capture request data
        try {
            eventBuilder.requestHeader(serializeHeaders(requestEntity.getHeaders()));
            eventBuilder.requestData(serializeRequestBody(requestEntity.getBody()));
        } catch (Exception e) {
            log.warn("Failed to serialize request data for integration logging", e);
        }

        ResponseEntity<T> response = null;
        Exception exception = null;
        
        try {
            // Execute the actual HTTP request
            response = restTemplate.exchange(url, method, requestEntity, responseType, uriVariables);
            return response;
            
        } catch (Exception e) {
            exception = e;
            throw e;
            
        } finally {
            // Log the integration activity
            long responseTime = System.currentTimeMillis();
            
            try {
                eventBuilder.responseTime(responseTime);
                
                if (response != null) {
                    // Success case
                    eventBuilder.responseCode(String.valueOf(response.getStatusCode().value()));
                    eventBuilder.responseHeader(serializeHeaders(response.getHeaders()));
                    eventBuilder.responseData(serializeResponseBody(response.getBody()));
                    eventBuilder.message("Successful request");
                } else if (exception != null) {
                    // Error case
                    eventBuilder.status(AppConst.ERROR);
                    eventBuilder.message("Failed request: " + exception.getMessage());
                    
                    if (exception instanceof org.springframework.web.client.HttpStatusCodeException) {
                        org.springframework.web.client.HttpStatusCodeException httpEx = 
                            (org.springframework.web.client.HttpStatusCodeException) exception;
                        eventBuilder.responseCode(String.valueOf(httpEx.getStatusCode().value()));
                        eventBuilder.responseData(httpEx.getResponseBodyAsString());
                        eventBuilder.responseHeader(serializeHeaders(httpEx.getResponseHeaders()));
                    }
                }
                
                IntegrationLogEvent event = eventBuilder.build();
                auditLogService.logIntegrationLog(event);
                
            } catch (Exception e) {
                log.error("Failed to log integration activity", e);
            }
        }
    }

    /**
     * Serialize HTTP headers to JSON string
     */
    private String serializeHeaders(HttpHeaders headers) {
        try {
            return headers != null ? SensitiveDataMasker.maskSensitiveData(headers) : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Serialize request body to string
     */
    private String serializeRequestBody(Object body) {
        if (body == null) {
            return null;
        }
        
        try {
            if (body instanceof String bodyString) {
                body = JsonUtils.readTree(bodyString);
            }
            return SensitiveDataMasker.maskSensitiveData(body);
        } catch (Exception e) {
            return body.toString();
        }
    }

    /**
     * Serialize response body to string
     */
    private String serializeResponseBody(Object body) {
        if (body == null) {
            return null;
        }
        
        try {
            if (body instanceof String bodyString) {
                body = JsonUtils.readTree(bodyString);
            }
            return SensitiveDataMasker.maskSensitiveData(body);
        } catch (Exception e) {
            return body.toString();
        }
    }
}
