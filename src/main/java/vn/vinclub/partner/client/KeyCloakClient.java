package vn.vinclub.partner.client;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.client.request.AuthenticationRequest;
import vn.vinclub.partner.client.response.AuthenticationResponse;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.exception.BusinessLogicException;

@Slf4j
@Service
@RequiredArgsConstructor
public class KeyCloakClient {
    private static final String SERVICE_NAME = "KEYCLOAK";

    private final ExternalHttpClient externalHttpClient;

    @Value("${keycloak.base-url}")
    private String keycloakUrl;

    @Value("${keycloak.realm}")
    private String realm;

    private final String tokenPath = "/realms/{keycloakRealm}/protocol/openid-connect/token";

    @Profiler
    public AuthenticationResponse authenticate(AuthenticationRequest authenticationRequest) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
            form.add("grant_type", authenticationRequest.getGrantType());
            form.add("client_id", authenticationRequest.getClientId());
            form.add("client_secret", authenticationRequest.getClientSecret());
            form.add("username", authenticationRequest.getUsername());
            form.add("password", authenticationRequest.getPassword());
            form.add("refresh_token", authenticationRequest.getRefreshToken());

            HttpEntity<Object> request = new HttpEntity<>(form, headers);

            String getTokenUri = UriComponentsBuilder.fromUriString(keycloakUrl)
                    .path(tokenPath)
                    .buildAndExpand(realm)
                    .toUriString();
            ResponseEntity<String> result = externalHttpClient.exchange(
                    getTokenUri,
                    HttpMethod.POST,
                    request,
                    String.class,
                    SERVICE_NAME,
                    AppConst.OAUTH2_GRANT_TYPE.REFRESH_TOKEN.equals(authenticationRequest.getGrantType()) ? "REFRESH_TOKEN" : "ISSUE_TOKEN"
            );
            return JsonUtils.toObject(result.getBody(), new TypeReference<>() {});
        } catch (HttpStatusCodeException e) {
            log.error("Failed when authenticate({})\tcode={}, body={}",
                    JsonUtils.toString(authenticationRequest),
                    e.getStatusCode(),
                    e.getResponseBodyAsString()
            );
            throw new BusinessLogicException(AppErrorCode.USER_UNAUTHORIZED);
        } catch (Exception e) {
            log.error("Exception occurs when authenticate({})\tmessage={}",
                    JsonUtils.toString(authenticationRequest),
                    e.getMessage(),
                    e
            );
            throw new BusinessLogicException(AppErrorCode.USER_UNAUTHORIZED);
        }
    }

}
