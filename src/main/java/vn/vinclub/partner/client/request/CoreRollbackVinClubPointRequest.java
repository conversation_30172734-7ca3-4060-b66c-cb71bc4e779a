package vn.vinclub.partner.client.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreRollbackVinClubPointRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String transactionId;
    private String reason;
    private Long vclubUserId;

    // enhance
    private String partnerCode;
}
