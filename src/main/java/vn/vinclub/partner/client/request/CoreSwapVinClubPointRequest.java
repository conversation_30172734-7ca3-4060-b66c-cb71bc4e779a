package vn.vinclub.partner.client.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import vn.vinclub.partner.domain.dto.external.point.PartnerSwapPointInfo;
import vn.vinclub.partner.domain.enums.SwapPointDirection;

import java.io.Serial;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreSwapVinClubPointRequest extends CoreVinClubPointRequest {
    @Serial
    private static final long serialVersionUID = 1L;

    private PartnerSwapPointInfo swapInfo;
    private SwapPointDirection swapDirection;
    private Long swapAmount;
}
