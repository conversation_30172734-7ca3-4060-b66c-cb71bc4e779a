package vn.vinclub.partner.client.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuthenticationWithPasswordRequest {
    private String username;
    private String password;
    private String clientId;
    private String clientSecret;

    public AuthenticationRequest toAuthenticationRequest() {
        return AuthenticationRequest.builder()
                .username(username)
                .password(password)
                .clientId(clientId)
                .clientSecret(clientSecret)
                .grantType("password")
                .build();
    }
}
