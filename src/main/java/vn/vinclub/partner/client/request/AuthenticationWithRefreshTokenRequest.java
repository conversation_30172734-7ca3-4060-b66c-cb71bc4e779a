package vn.vinclub.partner.client.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuthenticationWithRefreshTokenRequest {
    private String refreshToken;
    private String clientId;
    private String clientSecret;

    public AuthenticationRequest toAuthenticationRequest() {
        return AuthenticationRequest.builder()
                .refreshToken(refreshToken)
                .clientId(clientId)
                .clientSecret(clientSecret)
                .grantType("refresh_token")
                .build();
    }
}
