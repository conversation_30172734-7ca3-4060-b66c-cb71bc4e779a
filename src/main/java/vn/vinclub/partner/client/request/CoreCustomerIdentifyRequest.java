package vn.vinclub.partner.client.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CoreCustomerIdentifyRequest implements Serializable {
    private String pnl;
    private String pnlProfileId;
    private String pnlUserId;
    private Long vclubUserId;
    private String phone;
    private String email;
    private String identityDocumentCccdNo;
    private String identityDocumentPassportNo;

    private String myReferralCode;


    // additional info
    @Builder.Default
    private Boolean withRankingProgress = false;

    @Builder.Default
    private Boolean withDemographicMetadata = false;
}
