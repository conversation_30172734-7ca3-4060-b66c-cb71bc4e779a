package vn.vinclub.partner.client.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreVinClubPointRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String partnerCode;
    private String partnerUserId;
    private Long vclubUserId;

    private String transactionId;
    private String partnerTransactionId;
    private String description;
    private Long timestamp;
}

