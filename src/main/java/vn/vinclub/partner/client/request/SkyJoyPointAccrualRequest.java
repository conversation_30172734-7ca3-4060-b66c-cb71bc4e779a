package vn.vinclub.partner.client.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SkyJoyPointAccrualRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String skyjoyId;
    private String transactionId;
    private String transactionDes;
    private String transactionDate;

    @Builder.Default
    private Long transactionAmount = 0L;

    @Builder.Default
    private String transactionCurrency = "VND";

    private ExtraData extraData;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ExtraData implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private Long points;

        @JsonProperty("skypointvoucherDate")
        private String skyPointVoucherDate;

        @JsonProperty("VinclubID")
        private String vinclubId;
    }
}
