package vn.vinclub.partner.client.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.*;
import vn.vinclub.partner.domain.dto.BaseExternalResponse;

import java.io.Serial;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SkyJoyPointAccrualResponse extends BaseExternalResponse {
    @Serial
    private static final long serialVersionUID = 1L;
    private Long sponsorId;
    private String transactionId;
    private String bitReference;
    private String skyjoyId;
    private ObjectNode balance;
    private LocalDateTime timestamp;
    private String requestId;
}
