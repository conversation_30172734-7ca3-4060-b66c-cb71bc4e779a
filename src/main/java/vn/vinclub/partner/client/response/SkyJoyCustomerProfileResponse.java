package vn.vinclub.partner.client.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SkyJoyCustomerProfileResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String skyjoyId;
    private String dateOfBirth;
    private String dateOfJoin;
    private String phone;
    private String email;
    private String firstName;
    private String lastName;
    private String fullName;
    private String eKycStatus;
    private String status;
}
