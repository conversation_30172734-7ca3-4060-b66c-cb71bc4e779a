package vn.vinclub.partner.client.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SkyJoyLinkAccountResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String skyjoyId;
    private String phone;
    private String resultUrl;
    private String message;
    private String errorCode;

    public ObjectNode toPartnerUserIdentity() {
        ObjectNode partnerUserIdentity = JsonNodeFactory.instance.objectNode();
        partnerUserIdentity.put("skyjoyId", skyjoyId);
        partnerUserIdentity.put("phone", phone);
        return partnerUserIdentity;
    }
}
