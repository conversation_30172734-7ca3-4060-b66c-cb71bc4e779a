package vn.vinclub.partner.client.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import vn.vinclub.partner.domain.dto.BaseExternalResponse;

import java.io.Serial;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SkyJoyLookupTxnResponse extends BaseExternalResponse {
    @Serial
    private static final long serialVersionUID = 1L;
    private Long transactionPoint;
    private Long transactionAmount;
    private String transactionSource;
    private String transactionType;
    private String transactionId;
    private String bitReference;
    private String sponsor;
    private String status;
    private String requestId;
    private OffsetDateTime transactionTime;
    private LocalDateTime timestamp;
}
