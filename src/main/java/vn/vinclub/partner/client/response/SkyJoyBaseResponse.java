package vn.vinclub.partner.client.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SkyJoyBaseResponse<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private int statusCode;
    private String message;
    private T data;
    private Error errors;


    @Data
    public static class Error {
        private String code;
        private String message;
        private String name;

        public String toString() {
            return "Error(code=" + this.getCode() + ", message=" + this.getMessage() + ", name=" + this.getName() + ")";
        }
    }
}
