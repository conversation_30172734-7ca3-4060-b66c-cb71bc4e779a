package vn.vinclub.partner.client.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CorePartnerTxnLookupResponse {

    private String transactionId;
    private Long processedBalanceHistoryId;
    private String processedSessionId;
    private Long timestamp;
    private Long vclubPointChange;

    private boolean found;
    private boolean success;
}
