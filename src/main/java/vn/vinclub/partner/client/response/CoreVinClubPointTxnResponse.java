package vn.vinclub.partner.client.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreVinClubPointTxnResponse {

    private String partnerCode;
    private Long vclubUserId;
    private String partnerUserId;

    private Long processedBalanceHistoryId;
    private String processedSessionId;
    private String transactionId;
    private Long timestamp;
    private Long vclubPointChange;
    private boolean success;
}
