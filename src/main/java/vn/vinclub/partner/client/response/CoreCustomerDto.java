package vn.vinclub.partner.client.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import vn.vinclub.partner.domain.enums.CustomerStatus;
import vn.vinclub.partner.domain.enums.GenderEnum;

import java.time.LocalDate;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class CoreCustomerDto {

    private Long id;

    private String originalId;

    private String lastName;

    private String firstName;

    private String fullName;

    private GenderEnum gender;

    private String cccd;

    private boolean identityDocumentVerified;

    private String phoneNumber;

    private String email;

    private LocalDate dob;

    private String nationalityCode;

    private CustomerStatus status;

    private String preferredLanguage;

    private CoreTierDto tier;
}
