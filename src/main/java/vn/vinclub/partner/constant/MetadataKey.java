package vn.vinclub.partner.constant;

public interface MetadataKey {
    interface PartnerTransaction {
        String TOPUP_EXTRA_INFO = "topupExtraInfo";
        String ORDER_INFO = "orderInfo";
        String FAILED_REASON = "failedReason";
        String ROLLBACK_REASON = "rollbackReason";
        String REQUEST = "request";
        String RESPONSE = "response";

        interface TopupExtraInfo {
            String VOUCHER_PURCHASED_TIME = "voucherPurchasedTime";
        }
    }

    interface Partner {
        String INTERACTION_LINKS = "interactionLinks";
    }
}
