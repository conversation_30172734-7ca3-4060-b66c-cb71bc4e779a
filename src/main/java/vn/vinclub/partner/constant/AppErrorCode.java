package vn.vinclub.partner.constant;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum AppErrorCode {
    SUCCESS(0, "Thành công"),
    ERROR(-1, "Có lỗi xảy ra"),

    //--
    DATA_REQUEST_INVALID(8010100, "Dữ liệu truyền vào không hợp lệ"),
    INVALID_TYPE(8010101, "Sai định dạng dữ liệu: %s = %s"),
    INVALID_PARAM(8010102, "Tham số %s không hợp lệ: %s"),
    PARAM_REQUIRED(8010103, "Tham số %s là bắt buộc"),
    OBJECT_EXISTED(8010104, "%s đã tồn tại với %s = %s"),
    INVALID_ENUM(8010105, "<PERSON><PERSON><PERSON> trị %s không hợp lệ với Enum %s"),
    BAD_REQUEST(8010400, ""),
    USER_UNAUTHORIZED(8010401, "Người dùng không hợp lệ"),
    NOT_FOUND(8010404, "%s không tồn tại với %s = %s"),
    METHOD_NOT_ALLOWED(8010405, "Phương thức %s không hỗ trợ. Danh sách các phương thức hỗ trợ là: %s"),
    INTERNAL_SERVER_ERROR(8010500, "Rất tiếc đã có lỗi xảy ra"),
    RATE_LIMIT_EXCEED(8010501, "Người dùng gọi quá số lần sử dụng tối đa cho phép. Vui lòng thử lại sau"),
    DATA_IN_USE(8010502, "Dữ liệu đang được sử dụng, vui lòng thử lại"),
    INTERNAL_API_CALL_FAILED(8010503, "Gọi API tới hệ thống nội bộ thất bại. %s"),
    REQUEST_TIMEOUT(8010504, "Request timeout"),
    UNSUPPORTED(8010505, "Hệ thống chưa hỗ trợ chức năng này"),

    // COMMON ERROR
    INVALID_SIGNATURE_DATA(8010601, "Dữ liệu ký không hợp lệ"),
    LINK_ACCOUNT_UNEXPECTED_ERROR(8010628, "Đã xảy ra lỗi khi liên kết tài khoản: %s"),
    GET_CUSTOMER_PROFILE_UNEXPECTED_ERROR(8010629, "Đã xảy ra lỗi khi lấy thông tin khách hàng: %s"),
    GET_CUSTOMER_BALANCE_UNEXPECTED_ERROR(8010630, "Đã xảy ra lỗi khi lấy số dư tài khoản của khách hàng: %s"),

    // AUTHORIZATION ERROR
    AUTH_METHOD_UNSUPPORTED(8010700, "Loại phương thức [%s] dùng để xác thực không được hỗ trợ"),
    AUTH_DATA_SETTING_INVALID(8010701, "Dữ liệu xác thực cho phương thức [%s] không hợp lệ: %s"),
    AUTH_OAUTH2_GRANT_TYPE_UNSUPPORTED(8010702, "Hệ thống không hỗ trợ xác thực OAUTH2 với grant_type=%s"),
    AUTH_OAUTH2_OBTAIN_TOKEN_ERROR(8010703, "Không thể thực hiện request access token khi xác thực bằng phương thức OAUTH2"),
    AUTH_OAUTH2_ERROR(8010704, "Không thể thực hiện xác thực bằng phương thức OAUTH2"),


    // EXTERNAL ERROR CODE
    LINKED_VINCLUB_ACCOUNT_NOT_EXIST(8010900, "Tài khoản VinClub được liên kết không tồn tại hoặc đã bị xóa"),
    RELINK_ACCOUNT_MISMATCH(8010901, "Thông tin liên kết không khớp với thông tin đã liên kết trước đó"),
    ACCOUNT_LINKED_INFO_MISMATCH(8010902, "Thông tin liên kết không hợp lệ."),
    VINCLUB_BALANCE_ACCOUNT_NOT_CREATED(8010903, "Tài khoản điểm thưởng VinClub chưa được tạo"),
    PARTNER_API_FAILURE(8010904, "Gọi API tới hệ thống partner thất bại: %s"),
    PARTNER_UNSUPPORTED_OPERATION(8010905, "Hệ thống chưa hỗ trợ chức năng này với đối tác này"),
    CUSTOMER_NOT_EXIST(8010906, "Khách hàng không tồn tại"),
    INSUFFICIENT_POINTS(8010907, "Số điểm không đủ"),
    SWAP_POINT_UNEXPECTED_ERROR(8010908, "Đổi điểm không thành công. Lỗi: %s"),
    INVALID_TRANSACTION_STATUS(8010909, "Trạng thái giao dịch không hợp lệ hoặc đã được xử lý trước đó."),
    TRANSACTION_OWNER_MISMATCH(8010910, "Thông tin người dùng không khớp với thông tin trong giao dịch"),
    ROLLBACK_POINT_TXN_UNEXPECTED_ERROR(8010911, "Hoàn tác giao dịch không thành công. Lỗi: %s"),
    TRANSACTION_ID_PROCESSED(8010912, "Giao dịch đã được xử lý trước đó"),
    INVALID_BALANCE_HISTORY(8010913, "Bản ghi lịch sử không hợp lệ"),
    POINT_TXN_ALREADY_ROLLED_BACK(8010914, "Giao dịch điểm đối tác đã được hoàn tác"),
    POINT_TXN_CANNOT_ROLLBACK(8010915, "Giao dịch điểm đối tác không thể hoàn tác"),
    INVALID_SIGNED_TOKEN(8010916, "Token đã ký không hợp lệ"),
    TRANSACTION_NOT_FOUND(8010917, "Giao dịch không tồn tại"),
    ACCOUNT_LINKED(8010918, "Tài khoản đã được liên kết"),
    PARTNER_ACCOUNT_LINKED(8010919, "Tài khoản đối tác đã được liên kết với tài khoản VinClub khác"),
    VINCLUB_ACCOUNT_LINKED(8010920, "Tài khoản VinClub đã được liên kết với tài khoản đối tác khác"),
    ACCOUNT_NOT_LINKED(8010921, "Tài khoản chưa được liên kết"),
    PARTNER_USER_NOT_FOUND(8010922, "Không tìm thấy thông tin khách hàng từ đối tác"),
    GET_PARTNER_CUSTOMER_PROFILE_ERROR(8010923, "Đã xảy ra lỗi khi lấy thông tin khách hàng từ đối tác: %s"),
    GET_PARTNER_CUSTOMER_BALANCE_ERROR(8010924, "Đã xảy ra lỗi khi lấy số dư tài khoản của khách hàng từ đối tác: %s"),
    EXCHANGE_TOKEN_BY_CODE_ERROR(8010925, "Đã xảy ra lỗi khi thực hiện exchange token by code: %s"),
    UNLINK_ACCOUNT_UNEXPECTED_ERROR(8010926, "Đã xảy ra lỗi khi huỷ liên kết tài khoản: %s"),
    TOPUP_POINT_UNEXPECTED_ERROR(8010927, "Nạp điểm không thành công. Lỗi: %s"),
    SPEND_POINT_UNEXPECTED_ERROR(8010928, "Tiêu điểm không thành công. Lỗi: %s"),
    ACCUMULATE_POINT_UNEXPECTED_ERROR(8010929, "Tích điểm không thành công. Lỗi: %s"),
    POINT_PER_TRANSACTION_LIMIT_EXCEEDED(8010930, "Số điểm trong giao dịch vượt quá giới hạn cho phép"),
    DAILY_POINT_QUOTA_EXCEEDED(8010931, "Số điểm trong ngày vượt quá giới hạn cho phép"),
    MONTHLY_POINT_QUOTA_EXCEEDED(8010932, "Số điểm trong tháng vượt quá giới hạn cho phép"),
    INVALID_TRANSACTION_ACTOR_SYSTEM(8010933, "Hệ thống thực hiện giao dịch không hợp lệ"),
    EVENT_DATA_MISMATCH(8010934, "Dữ liệu trong event không khớp với dữ liệu trong giao dịch"),
    POINT_TOPUP_UNEXPECTED_ERROR(8010935, "Đã xảy ra lỗi khi nạp điểm cho khách hàng: %s"),
    POINT_TOPUP_PARTNER_TRANSACTION_CREATED_FAILED(8010936, "Hệ thống đối tác không thể tạo giao dịch điểm cho khách hàng. Nạp điểm thất bại."),
    LOOKUP_TRANSACTION_ERROR(8010937, "Đã xảy ra lỗi khi tra cứu giao dịch: %s"),
    DAILY_TRANSACTION_COUNT_LIMIT_EXCEEDED(8010938, "Số lượng giao dịch trong ngày vượt quá giới hạn cho phép"),
    DAILY_CASH_AMOUNT_LIMIT_EXCEEDED(8010939, "Tổng giá trị giao dịch trong ngày vượt quá giới hạn cho phép")
    ;

    private final Integer code;
    private final String message;

    AppErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    // lookup table to be used to find enum for conversion
    private static final Map<Integer, AppErrorCode> lookup = new HashMap<>();

    static {
        for (AppErrorCode e : AppErrorCode.values())
            lookup.put(e.getCode(), e);
    }

    public static AppErrorCode lookupByErrorCode(Integer errorCode) {
        return lookup.get(errorCode);
    }

}
