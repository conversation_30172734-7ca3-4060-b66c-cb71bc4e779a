package vn.vinclub.partner.constant;

public interface AppConst {

    String UPDATED_BY_AUTO = "SYSTEM";
    String SOURCE_SYSTEM = "PARTNER_SERVICE";
    String SUCCESS = "SUCCESS";
    String ERROR = "ERROR";
    String DEFAULT_LANGUAGE = "vi";
    String DEFAULT_COUNTRY = "VN";
    String DEFAULT_CURRENCY = "VND";
    String IMAGE_TYPE = "image/png";

    // common character
    String SPACE = " ";
    String COLON = ":" ;

    interface CONTENT_TYPE {
        String JSON = "application/json";
        String FORM_URLENCODED = "application/x-www-form-urlencoded";
    }

    interface INTEGRATION_JWT_CLAIM {
        String PARTNER_CODE = "partner_code";
        String USERNAME = "username";
    }

    interface CUSTOMER_JWT_CLAIM {
        String VCLUB_USER_ID = "sub";
        String PHONE = "phone";
        String USERNAME = "username";

        // Additional claim
        String PARTNER_CODE = "partner_code";
        String PARTNER_USER_ID = "partner_user_id";
    }

    interface TOKEN_PREFIX {
        String BASIC = "Basic";
        String BEARER = "Bearer";
    }

    interface OAUTH2_GRANT_TYPE {
        String AUTHORIZATION_CODE = "authorization_code";
        String REFRESH_TOKEN = "refresh_token";
        String PASSWORD = "password";
        String CLIENT_CREDENTIALS = "client_credentials";
        String IMPLICIT = "implicit";
    }

    interface OAUTH2_REQUEST_PARAM {
        String GRANT_TYPE = "grant_type";
        String CLIENT_ID = "client_id";
        String CLIENT_SECRET = "client_secret";
        String USERNAME = "username";
        String PASSWORD = "password";
        String CODE = "code";
        String REDIRECT_URI = "redirect_uri";
        String REFRESH_TOKEN = "refresh_token";
        String SCOPE = "scope";
        String STATE = "state";
    }

    interface OAUTH2_RESPONSE_PARAM {
        String ACCESS_TOKEN = "access_token";
        String TOKEN_TYPE = "token_type";
        String EXPIRES_IN = "expires_in";
        String REFRESH_EXPIRES_IN = "refresh_expires_in";
        String REFRESH_TOKEN = "refresh_token";
        String SCOPE = "scope";
        String STATE = "state";
    }

    interface REDIS_LOCK {
        int WAIT_TIME_IN_MS = 10000;
        int LEASE_TIME_IN_MS = 60000;
        String PARTNER_USER_MAPPING_CUD = "partner_svc:LOCK_PARTNER_USER_MAPPING_CUD:";
        String PARTNER_POINT_TRANSACTION_CUD = "partner_svc:LOCK_PARTNER_POINT_TRANSACTION_CUD:";
    }

    /**
     * Redis message
     */
    interface RedisMessage {
        String CHANGE_PARTNER = "CHANGE_PARTNER";
        String CHANGE_PARTNER_MODULE = "CHANGE_PARTNER_MODULE";
        String CHANGE_PARTNER_POINT_CONFIG = "CHANGE_PARTNER_POINT_CONFIG";
    }

    interface Quota {
        String DAILY = "daily";
        String MONTHLY = "monthly";
    }
}
