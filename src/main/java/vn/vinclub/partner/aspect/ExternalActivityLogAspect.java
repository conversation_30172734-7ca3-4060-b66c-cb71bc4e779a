package vn.vinclub.partner.aspect;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.annotation.ExternalActivityLog;
import vn.vinclub.partner.client.response.AuthenticationResponse;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.event.audit.ExternalActivityLogEvent;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.service.AuditLogService;
import vn.vinclub.partner.util.RequestUtil;
import vn.vinclub.partner.util.SensitiveDataMasker;
import vn.vinclub.partner.util.ServiceResponse;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.LinkedHashMap;
import java.util.Map;



/**
 * AOP Aspect for logging external activity on integration controller methods
 * 
 * <AUTHOR>
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class ExternalActivityLogAspect {

    private static final String SINGLE_BODY_PARAM_NAME = "b_o_d_y";

    private final AuditLogService auditLogService;
    private final JwtDecoder jwtDecoder; // Spring Security JWT decoder for integration tokens
    private final ExpressionParser expressionParser = new SpelExpressionParser();


    /**
     * Helper class to hold authentication information
     */
    static class AuthenticationInfo {
        String externalSystem;
        String username;

        AuthenticationInfo(String externalSystem, String username) {
            this.externalSystem = externalSystem;
            this.username = username;
        }
    }

    @Around("@annotation(externalActivityLog)")
    public Object logExternalActivity(ProceedingJoinPoint joinPoint, ExternalActivityLog externalActivityLog) throws Throwable {
        long requestTime = System.currentTimeMillis();

        // Get HTTP request
        HttpServletRequest request = getCurrentHttpRequest();
        if (request == null) {
            log.warn("No HTTP request found in context, skipping external activity logging");
            return joinPoint.proceed();
        }

        // Extract request data
        String ip = RequestUtil.getClientIpAddress(request);
        String requestData = extractRequestData(joinPoint, request);

        // Initialize log event with basic info
        ExternalActivityLogEvent.ExternalActivityLogEventBuilder eventBuilder = ExternalActivityLogEvent.builder()
                .action(externalActivityLog.action())
                .object(externalActivityLog.object())
                .externalSystem(externalActivityLog.externalSystem())
                .ip(ip)
                .requestTime(requestTime)
                .request(requestData);

        // Extract object ID if specified
        if (StringUtils.hasText(externalActivityLog.objectId())) {
            String objectId = evaluateSpelExpression(externalActivityLog.objectId(), joinPoint);
            eventBuilder.objectId(objectId);
        }

        // Extract authentication info from the request
        var isAuthEndpoint = isAuthenticationEndpoint(request);
        if (!isAuthEndpoint) {
            AuthenticationInfo authInfo = extractAuthenticationInfo();

            if (authInfo.externalSystem != null) {
                eventBuilder.externalSystem(authInfo.externalSystem);
            }
            if (authInfo.username != null) {
                eventBuilder.username(authInfo.username);
            }
        }

        Object result = null;
        Throwable exception = null;

        try {
            // Proceed with method execution
            result = joinPoint.proceed();
            return result;
        } catch (Throwable throwable) {
            exception = throwable;
            throw throwable;
        } finally {
            // Log activity after method execution (success or failure)
            long responseTime = System.currentTimeMillis();

            try {
                if (isAuthEndpoint ) {
                    extractPartnerCodeFromAuthResponse(result, eventBuilder);
                }

                // Extract response data and status
                extractResponseInfo(result, exception, eventBuilder);

                // Complete the event
                ExternalActivityLogEvent event = eventBuilder
                        .responseTime(responseTime)
                        .build();

                // Send it to the audit service
                auditLogService.logExternalActivity(event);

            } catch (Exception e) {
                log.error("Error logging external activity", e);
            }
        }
    }

    private HttpServletRequest getCurrentHttpRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            return attributes.getRequest();
        } catch (Exception e) {
            return null;
        }
    }

    String extractRequestData(ProceedingJoinPoint joinPoint, HttpServletRequest request) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Parameter[] parameters = method.getParameters();
            Object[] args = joinPoint.getArgs();

            Map<String, Object> pathVariables = new LinkedHashMap<>();
            Map<String, Object> queryParameters = new LinkedHashMap<>();
            Map<String, Object> requestBody = new LinkedHashMap<>();

            // Extract path variables, query parameters, and request body
            for (int i = 0; i < parameters.length && i < args.length; i++) {
                Parameter parameter = parameters[i];
                Object argValue = args[i];

                // Skip HttpServletRequest parameters
                if (argValue instanceof HttpServletRequest) {
                    continue;
                }

                // Extract path variables
                PathVariable pathVariableAnnotation = parameter.getAnnotation(PathVariable.class);
                if (pathVariableAnnotation != null) {
                    String paramName = getParameterName(pathVariableAnnotation.value(), pathVariableAnnotation.name(), parameter.getName());
                    pathVariables.put(paramName, argValue);
                    continue;
                }

                // Extract query parameters
                RequestParam requestParamAnnotation = parameter.getAnnotation(RequestParam.class);
                if (requestParamAnnotation != null) {
                    String paramName = getParameterName(requestParamAnnotation.value(), requestParamAnnotation.name(), parameter.getName());
                    queryParameters.put(paramName, argValue);
                    continue;
                }

                // Extract request body
                RequestBody requestBodyAnnotation = parameter.getAnnotation(RequestBody.class);
                if (requestBodyAnnotation != null && argValue != null) {
                    // For request body, we want to capture the entire object structure
                    if (isPrimitiveOrWrapper(argValue.getClass())) {
                        requestBody.put(SINGLE_BODY_PARAM_NAME, argValue);
                    } else {
                        // Convert complex object to map for structured logging
                        try {
                            String jsonString = JsonUtils.toString(argValue);
                            if (jsonString != null) {
                                Map<String, Object> data = JsonUtils.readMap(jsonString);
                                requestBody.putAll(data);
                            }
                        } catch (Exception e) {
                            log.debug("Could not convert request body to JSON, using string representation", e);
                            requestBody.put(SINGLE_BODY_PARAM_NAME, argValue.toString());
                        }
                    }
                }
            }

            // Build structured response
            Map<String, Object> structuredData = new LinkedHashMap<>();
            if (!pathVariables.isEmpty()) {
                structuredData.put("path_variables", pathVariables);
            }
            if (!queryParameters.isEmpty()) {
                structuredData.put("query_parameters", queryParameters);
            }
            if (!requestBody.isEmpty()) {
                if (requestBody.size() == 1 && requestBody.containsKey(SINGLE_BODY_PARAM_NAME)) {
                    structuredData.put("request_body", requestBody.get(SINGLE_BODY_PARAM_NAME));
                } else {
                    structuredData.put("request_body", requestBody);
                }
            }

            // If no structured data found, return null
            if (structuredData.isEmpty()) {
                return null;
            }

            // Apply sensitive data masking to the structured data object
            return SensitiveDataMasker.maskSensitiveData(structuredData);

        } catch (Exception e) {
            log.warn("Error extracting request data", e);
            return null;
        }
    }

    AuthenticationInfo extractAuthenticationInfo() {
        String username = null;
        String partnerCode = null;
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication instanceof JwtAuthenticationToken jwtAuthenticationToken &&
                jwtAuthenticationToken.getPrincipal() instanceof Jwt jwt) {

                username = jwt.getClaim(AppConst.INTEGRATION_JWT_CLAIM.USERNAME);
                partnerCode = jwt.getClaim(AppConst.INTEGRATION_JWT_CLAIM.PARTNER_CODE);
            }
        } catch (Exception e) {
            // Do nothing
        }

        return new AuthenticationInfo(partnerCode, username);
    }

    private void extractResponseInfo(Object result, Throwable exception, ExternalActivityLogEvent.ExternalActivityLogEventBuilder eventBuilder) {
        if (exception != null) {
            // Handle exception case
            if (exception instanceof BusinessLogicException businessLogicException) {
                eventBuilder.responseCode(businessLogicException.getPayload().getCode());
                eventBuilder.responseMsg(businessLogicException.getPayload().getFirstMessage());
                // Use SensitiveDataMasker to mask sensitive fields in exception payload
                eventBuilder.response(SensitiveDataMasker.maskSensitiveData(businessLogicException.getPayload()));
            } else {
                eventBuilder.responseCode(AppErrorCode.ERROR.getCode());
                eventBuilder.responseMsg(AppErrorCode.ERROR.getMessage());
                eventBuilder.response(exception.getMessage());
            }
        } else if (result != null) {
            // Handle successful response
            eventBuilder.responseCode(0);

            if (result instanceof ServiceResponse<?> serviceResponse) {
                eventBuilder.responseCode(serviceResponse.getCode());
                eventBuilder.responseMsg(getFirstMessage(serviceResponse.getMessage()));
                // Use SensitiveDataMasker to mask sensitive fields in response data
                eventBuilder.response(SensitiveDataMasker.maskSensitiveData(result));
            } else if (result instanceof ResponseEntity<?> responseEntity) {
                // Use SensitiveDataMasker to mask sensitive fields in response body
                eventBuilder.response(SensitiveDataMasker.maskSensitiveData(responseEntity.getBody()));
                if (responseEntity.getStatusCode().isError()) {
                    eventBuilder.responseCode(AppErrorCode.ERROR.getCode());
                    eventBuilder.responseMsg(AppErrorCode.ERROR.getMessage());
                }
            } else {
                // Use SensitiveDataMasker to mask sensitive fields in response data
                eventBuilder.response(SensitiveDataMasker.maskSensitiveData(result));
            }
        }
    }

    private void extractPartnerCodeFromAuthResponse(Object result, ExternalActivityLogEvent.ExternalActivityLogEventBuilder eventBuilder) {
        try {
            if (result instanceof ServiceResponse<?> serviceResponse) {
                result = serviceResponse.getData();
                if (result == null) {
                    return;
                }
                if (result instanceof AuthenticationResponse authResponse) {
                    if (StringUtils.hasText(authResponse.getAccessToken())) {
                        // For authentication endpoints, use the Spring Security JWT decoder for integration tokens
                        Jwt jwt = decodeIntegrationJwt(authResponse.getAccessToken());
                        if (jwt != null) {
                            String partnerCode = jwt.getClaim(AppConst.INTEGRATION_JWT_CLAIM.PARTNER_CODE);
                            String username = jwt.getClaim(AppConst.INTEGRATION_JWT_CLAIM.USERNAME);

                            if (StringUtils.hasText(partnerCode)) {
                                eventBuilder.externalSystem(partnerCode);
                                log.debug("Extracted partner_code from auth response: {}", partnerCode);
                            }
                            if (StringUtils.hasText(username)) {
                                eventBuilder.username(username);
                                log.debug("Extracted username from auth response: {}", username);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Could not extract partner code from auth response", e);
        }
    }

    private String evaluateSpelExpression(String expression, ProceedingJoinPoint joinPoint) {
        try {
            Expression spelExpression = expressionParser.parseExpression(expression);
            EvaluationContext context = new StandardEvaluationContext();
            
            // Add method parameters to context
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Parameter[] parameters = method.getParameters();
            Object[] args = joinPoint.getArgs();
            
            for (int i = 0; i < parameters.length && i < args.length; i++) {
                context.setVariable(parameters[i].getName(), args[i]);
            }
            
            Object value = spelExpression.getValue(context);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            log.warn("Error evaluating SpEL expression: " + expression, e);
            return null;
        }
    }

    private boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() ||
               clazz == Boolean.class || clazz == Character.class || clazz == Byte.class ||
               clazz == Short.class || clazz == Integer.class || clazz == Long.class ||
               clazz == Float.class || clazz == Double.class || clazz == String.class;
    }

    /**
     * Helper method to get parameter name from annotation or fallback to parameter name
     */
    private String getParameterName(String annotationValue, String annotationName, String parameterName) {
        // Spring annotations use 'value' as primary and 'name' as alias
        if (StringUtils.hasText(annotationValue)) {
            return annotationValue;
        }
        if (StringUtils.hasText(annotationName)) {
            return annotationName;
        }
        return parameterName;
    }

    String getFirstMessage(java.util.List<String> messages) {
        if (messages == null || messages.isEmpty()) {
            return null;
        }
        return messages.getFirst();
    }

    /**
     * Decode integration JWT token using the Spring Security JWT decoder
     * This is used for JWT tokens returned by authentication endpoints
     */
    private Jwt decodeIntegrationJwt(String token) {
        try {
            return jwtDecoder.decode(token);
        } catch (Exception e) {
            log.debug("Could not decode integration JWT token", e);
            return null;
        }
    }

    /**
     * Check if the current request is to an authentication endpoint
     */
    boolean isAuthenticationEndpoint(HttpServletRequest request) {
        if (request == null) {
            return false;
        }
        String requestURI = request.getRequestURI();
        return requestURI != null && (
            requestURI.contains("/integration/v1/auth/token") ||
            requestURI.contains("/integration/v1/auth/refresh-token")
        );
    }
}
