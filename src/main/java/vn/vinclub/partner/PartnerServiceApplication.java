package vn.vinclub.partner;

import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import vn.vinclub.common.annotation.EnableProfiler;

@SpringBootApplication(scanBasePackages = {"vn.vinclub"})
@EnableProfiler
@Slf4j
@EnableJpaRepositories("vn.vinclub.partner.repository")
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "10m")
public class PartnerServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(PartnerServiceApplication.class, args);
    }
}
