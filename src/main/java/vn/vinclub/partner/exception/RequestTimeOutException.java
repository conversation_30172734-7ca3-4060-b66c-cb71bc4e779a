package vn.vinclub.partner.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ResponseStatus;
import vn.vinclub.partner.constant.AppErrorCode;

import java.io.Serial;

@Getter
@ResponseStatus(value = HttpStatus.REQUEST_TIMEOUT)
public class RequestTimeOutException extends RuntimeException {

	@Serial
	private static final long serialVersionUID = 1L;
	private final String message;

	public RequestTimeOutException(String message) {
		super();
		this.message = message;
	}


    public String getMessage() {
		return StringUtils.hasText(message) ? message : AppErrorCode.REQUEST_TIMEOUT.getMessage();
	}
}
