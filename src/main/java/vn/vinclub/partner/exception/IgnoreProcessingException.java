package vn.vinclub.partner.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.util.ServiceResponse;

@Data
@EqualsAndHashCode(callSuper = false)
public class IgnoreProcessingException extends RuntimeException {
    private final ServiceResponse<?> payload;

    public IgnoreProcessingException(ServiceResponse<?> payload) {
        super(JsonUtils.toString(payload));
        this.payload = payload;
    }

    public IgnoreProcessingException(AppErrorCode errorCode, Object... args) {
        this(ServiceResponse.error(errorCode, args));
    }
}
