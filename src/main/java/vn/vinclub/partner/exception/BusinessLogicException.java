package vn.vinclub.partner.exception;

import lombok.Getter;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.util.ServiceResponse;

import java.io.Serial;
import java.util.List;

@Getter
public class BusinessLogicException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;
    private final ServiceResponse<?> payload;

    public BusinessLogicException(ServiceResponse<?> payload) {
        super(JsonUtils.toString(payload));
        this.payload = payload;

    }

    public BusinessLogicException(AppErrorCode errorCode, Object... args) {
        this(ServiceResponse.error(errorCode, args));
    }

    public <T> BusinessLogicException(T data, int code, List<String> message) {
        this(ServiceResponse.builder().data(data).code(code).message(message).build());
    }

    public <T> BusinessLogicException(T data,AppErrorCode errorCode, Object... args) {
        this(ServiceResponse.error(data, errorCode, args));
    }
}
