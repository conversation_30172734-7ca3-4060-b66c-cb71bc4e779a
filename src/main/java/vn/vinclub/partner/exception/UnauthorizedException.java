package vn.vinclub.partner.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.partner.util.ServiceResponse;

import java.io.Serial;

@Getter
@ResponseStatus(value = HttpStatus.UNAUTHORIZED)
public class UnauthorizedException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;

    private final ServiceResponse<?> payload;

    public UnauthorizedException() {
        super(HttpStatus.UNAUTHORIZED.getReasonPhrase());
        this.payload = ServiceResponse.error(HttpStatus.UNAUTHORIZED.value(), HttpStatus.UNAUTHORIZED.getReasonPhrase());
    }

    public UnauthorizedException(ServiceResponse<?> payload) {
        super(JsonUtils.toString(payload));
        this.payload = payload;
    }
}
