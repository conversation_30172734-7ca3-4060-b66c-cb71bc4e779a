package vn.vinclub.partner.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.vinclub.partner.domain.entity.PartnerModule;
import vn.vinclub.partner.domain.enums.ModuleType;

import java.util.List;
import java.util.Optional;

@Repository
public interface PartnerModuleRepository extends JpaRepository<PartnerModule, Long>, JpaSpecificationExecutor<PartnerModule> {
    Optional<PartnerModule> findByPartnerIdAndModule(Long partnerId, ModuleType module);
    List<PartnerModule> findByModuleAndEnabledIsTrue(ModuleType module);
}