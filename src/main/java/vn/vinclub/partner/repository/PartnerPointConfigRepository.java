package vn.vinclub.partner.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.vinclub.partner.domain.entity.PartnerPointConfig;

import java.util.Optional;

@Repository
public interface PartnerPointConfigRepository extends JpaRepository<PartnerPointConfig, Long>, JpaSpecificationExecutor<PartnerPointConfig> {
    Optional<PartnerPointConfig> findByPartnerIdAndCode(Long partnerId, String code);
    Optional<PartnerPointConfig> findByPartnerIdAndIsDefault(Long partnerId, boolean isDefault);
}