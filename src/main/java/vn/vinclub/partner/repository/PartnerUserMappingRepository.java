package vn.vinclub.partner.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vn.vinclub.partner.domain.entity.PartnerUserMapping;

import java.util.List;
import java.util.Optional;

public interface PartnerUserMappingRepository extends JpaRepository<PartnerUserMapping, Long> {

    List<PartnerUserMapping> findByVclubUserIdAndActiveTrue(Long userId);
    Optional<PartnerUserMapping> findByPartnerIdAndPartnerUserIdAndActive(Long partnerId, String partnerUserId, Boolean active);
    Optional<PartnerUserMapping> findByPartnerIdAndVclubUserIdAndActive(Long partnerId, Long vclubUserId, Boolean active);

    @Query("select count(p) > 0 from PartnerUserMapping p where p.active = true and p.partnerId = ?1 and (p.partnerUserId = ?2 or p.vclubUserId = ?3)")
    boolean existsByPartnerUserIdOrVClubUserId(Long partnerId, String partnerUserId, Long vclubUserId);
}
