package vn.vinclub.partner.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.vinclub.partner.domain.entity.OutboxEvent;
import vn.vinclub.partner.domain.enums.OutboxStatusEnum;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public interface OutboxEventRepository extends JpaRepository<OutboxEvent, Long>, JpaSpecificationExecutor<OutboxEvent> {

    List<OutboxEvent> findAllByStatusOrderByIdAsc(OutboxStatusEnum status);

    Optional<OutboxEvent> findByEventId(String outboxEventId);

}
