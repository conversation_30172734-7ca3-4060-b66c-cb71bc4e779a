package vn.vinclub.partner.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark controller methods for external activity logging
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExternalActivityLog {
    
    /**
     * Action performed (e.g., "GET_PROFILE", "TOPUP_POINT", "AUTHENTICATE")
     */
    String action();
    
    /**
     * Object being acted upon (e.g., "CUSTOMER", "POINT", "AUTHENTICATION")
     */
    String object();
    
    /**
     * Optional object ID expression (SpEL supported)
     * Can reference method parameters like "#partnerUserId" or "#request.partnerUserId"
     */
    String objectId() default "";

    /**
     * Optional external system name (e.g., "SKYJOY", "KFC")
     */
    String externalSystem() default "";
}
