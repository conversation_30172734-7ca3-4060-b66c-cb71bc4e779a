package vn.vinclub.partner.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import vn.vinclub.partner.domain.enums.SwapPointDirection;
@Converter(autoApply = true)
public class SwapPointDirectionEnumConverter implements AttributeConverter<SwapPointDirection, Integer> {

    @Override
    public Integer convertToDatabaseColumn(SwapPointDirection attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.toValue();
    }

    @Override
    public SwapPointDirection convertToEntityAttribute(Integer dbData) {
        if (dbData == null) {
            return null;
        }
        try{
            return SwapPointDirection.fromValue(dbData);
        }catch (IllegalArgumentException e){
            return null;
        }

    }
}
