package vn.vinclub.partner.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.jackson.JsonComponent;
import vn.vinclub.common.util.DateUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Objects;

@JsonComponent
public class LocalDateTimeCombinedSerializer {

	public static class LocalDateTimeJsonSerializer extends JsonSerializer<LocalDateTime> {

		@Override
		public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
			if (Objects.nonNull(value)) {
				gen.writeNumber(DateUtils.getMilliseconds(value));
			}
		}

	}

	public static class LocalDateTimeJsonDeserializer extends JsonDeserializer<LocalDateTime> {

		@Override
		public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
			if (p.hasToken(JsonToken.VALUE_STRING) && StringUtils.isNumeric(p.getText())) {
				return DateUtils.fromMilliseconds(Long.parseLong(p.getText()));
			}

			if (p.hasToken(JsonToken.VALUE_STRING)) {
				return DateUtils.convertStringToLocalDateTime(p.getText());
			}

			return p.hasToken(JsonToken.VALUE_NUMBER_INT) ? DateUtils.fromMilliseconds(Long.parseLong(p.getText())) : null;
		}

	}

}
