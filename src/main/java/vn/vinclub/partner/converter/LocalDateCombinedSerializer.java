package vn.vinclub.partner.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.boot.jackson.JsonComponent;
import vn.vinclub.common.util.DateUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.util.Objects;

@JsonComponent
public class LocalDateCombinedSerializer {

	public static class LocalDateJsonSerializer extends JsonSerializer<LocalDate> {

		@Override
		public void serialize(LocalDate value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
			if (Objects.nonNull(value)) {
				gen.writeNumber(DateUtils.getMillisecondsStartDate(value));
			}
		}

	}

	public static class LocalDateJsonDeserializer extends JsonDeserializer<LocalDate> {

		@Override
		public LocalDate deserialize(JsonParser p, DeserializationContext ctxt)
				throws IOException {
			return p.hasToken(JsonToken.VALUE_NUMBER_INT) ? DateUtils.fromMilliseconds(Long.parseLong(p.getText())).toLocalDate() : null;
		}

	}

}
