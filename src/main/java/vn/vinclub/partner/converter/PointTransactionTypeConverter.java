package vn.vinclub.partner.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import vn.vinclub.partner.domain.enums.TransactionType;

@Converter
public class PointTransactionTypeConverter implements AttributeConverter<TransactionType, String> {

    @Override
    public String convertToDatabaseColumn(TransactionType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getCode();
    }

    @Override
    public TransactionType convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return TransactionType.fromValue(dbData);
    }
}