package vn.vinclub.partner.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;

@Converter(autoApply = true)
public class PointHistoryStatusEnumConverter implements AttributeConverter<PointHistoryStatus, String> {
    @Override
    public String convertToDatabaseColumn(PointHistoryStatus attribute) {
        return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
    }

    @Override
    public PointHistoryStatus convertToEntityAttribute(String dbData) {
        return PointHistoryStatus.fromValue(dbData);
    }
}
