package vn.vinclub.partner.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import vn.vinclub.partner.domain.enums.PartnerStatus;

@Converter(autoApply = true)
public class PartnerStatusConverter implements AttributeConverter<PartnerStatus, String> {
    @Override
    public String convertToDatabaseColumn(PartnerStatus attribute) {
        return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
    }

    @Override
    public PartnerStatus convertToEntityAttribute(String dbData) {
        return PartnerStatus.fromValue(dbData);
    }
}
