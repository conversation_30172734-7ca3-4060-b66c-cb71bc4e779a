# common
spring.application.name=partner-service
server.port = 8080
server.servlet.context-path = /pn
spring.main.allow-bean-definition-overriding=true
spring.main.banner-mode=off

# server
server.max-http-request-header-size=2MB
server.servlet.encoding.charset = UTF-8

########## PostgreSQL - connection
spring.datasource.url = ${POSTGRESQL_URL:********************************************************************}
spring.datasource.username = ${POSTGRESQL_USER:vhm_user}
spring.datasource.password = ${POSTGRESQL_PASSWORD:vhm_pass}
spring.datasource.hikari.minimumIdle = 2
spring.datasource.hikari.maximumPoolSize = 3
spring.datasource.hikari.idleTimeout = 30000
spring.datasource.hikari.poolName = SpringBootJPAHikariCP
spring.datasource.hikari.maxLifetime = 2000000
spring.datasource.hikari.connectionTimeout = 30000

# The SQL dialect makes Hibernate generate better SQL for the chosen database
spring.jpa.properties.hibernate.default_schema = ${DB_DEFAULT_SCHEMA:partner_db}
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults = false
spring.jpa.database-platform = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto = update
spring.jpa.show-sql = true

# REDDISON
redisson.config.address=${REDISSON_CONFIG_ADDRESS:redis://localhost:6379}
redisson.config.password=${REDISSON_CONFIG_PASSWORD:}
redisson.config.database=${REDISSON_CONFIG_DATABASE:0}
redisson.config.response.timeout=${REDISSON_CONFIG_RESPONSE_TIMEOUT:3000}
redisson.config.connection.timeout=${REDISSON_CONFIG_CONNECTION_TIMEOUT:3000}
redisson.config.connection.idle.time=${REDISSON_CONFIG_CONNECTION_IDLE_TIME:300000}
redisson.config.connection.keep-alive=${REDISSON_CONFIG_CONNECTION_KEEP_ALIVE:true}
redisson.config.connection.max=${REDISSON_CONFIG_CONNECTION_MAX:24}
redisson.config.connection.min=${REDISSON_CONFIG_CONNECTION_MIN:2}

# redisson channel
redis.channel.change=CHANNEL.UPDATE

#kafka configuration
kafka.properties.sasl.mechanism=${KAFKA_SASL_MECHANISM:PLAIN}
kafka.properties.sasl.jaas.config=${KAFKA_SASL_JAAS_CONFIG:org.apache.kafka.common.security.plain.PlainLoginModule required username="vhm-user" password="vhm-pass";}
kafka.properties.bootstrap.servers=${KAFKA_BOOTSTRAP_SERVER:localhost:9092}
kafka.properties.security.protocol=${KAFKA_SECURITY_PROTOCOL:PLAINTEXT}
kafka.properties.basic.auth.credentials.source=USER_INFO
kafka.properties.schema.registry.basic.auth.user.info=${KAFKA_REGISTRY_USERNAME:}:${KAFKA_REGISTRY_PASSWORD:}
kafka.properties.schema.registry.url=${KAFKA_REGISTRY_URL:}
kafka.properties.max-poll-records = ${KAFKA_MAX_POLL_RECORDS:10}
kafka.properties.auto.offset.reset = ${KAFKA_AUTO_OFFSET_RESET:}
kafka.properties.main.concurrency = ${KAFKA_MAIN_CONCURRENCY:2}
kafka.properties.main.group.name=partner-service.consumer.main_${spring.profiles.active}

# kafka topic
# Customer historical
kafka.historical.vclub_customer.topic.name=historical.vclub_customer
kafka.historical.vclub_customer.group.name=group_${spring.application.name}_${spring.profiles.active}_historical_vclub_customer
kafka.historical.vclub_customer.enabled=true

# Partner user mapping historical
kafka.historical.partner_user_mapping.topic.name=historical.partner_user_mapping
kafka.historical.partner_user_mapping.group.name=group_${spring.application.name}_${spring.profiles.active}_historical_partner_user_mapping
kafka.historical.partner_user_mapping.enabled=true

# Partner point transaction history
kafka.historical.partner_point_transaction_history.topic.name=historical.partner_point_transaction_history
kafka.historical.partner_point_transaction_history.group.name=group_${spring.application.name}_${spring.profiles.active}_historical_partner_point_transaction_history
kafka.historical.partner_point_transaction_history.enabled=true

# partner point transaction request
kafka.partner_point_txn_request.topic.name=partner_svc.partner_point_txn_request
kafka.partner_point_txn_request.group.name=group_${spring.application.name}_${spring.profiles.active}_partner_point_txn_request
kafka.partner_point_txn_request.enabled=true

# partner point transaction response
kafka.partner_point_txn_response.topic.name=partner_svc.partner_point_txn_response

# audit log
kafka.log.external_activity_audit.topic.name=log.external_activity_audit
kafka.log.vclub_integration_audit.topic.name=log.vclub_integration_audit

# core service internal endpoint
core-service.internal.endpoint = ${CORE_SERVICE_INTERNAL_ENDPOINT:http://stag-svc.vinclub.internal:30082/r/internal}
core-service.auth = ${AUTHZ_VCLB_CORE_SVC_TOKEN:Bearer eyJ4NXQiOiJNell4TW1Ga09HWXdNV0kwWldObU5EY3hOR1l3WW1NNFpUQTNNV0kyTkRBelpHUXpOR00wWkdSbE5qSmtPREZrWkRSaU9URmtNV0ZoTXpVMlpHVmxOZyIsImtpZCI6Ik16WXhNbUZrT0dZd01XSTBaV05tTkRjeE5HWXdZbU00WlRBM01XSTJOREF6WkdRek5HTTBaR1JsTmpKa09ERmtaRFJpT1RGa01XRmhNelUyWkdWbE5nX1JTMjU2IiwiYWxnIjoiUlMyNTYifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RtcMaiR4aASzXmUsmrREFB0uROJUrqkHl8-Z3afsGW8dS4Qqpi4A-QuGh-G49bFthmJbZ9m73LUjLead1npCIE_YaTFm0aGbb-fUbi1-beGS27_j4TXJszBHMb6MGK7Eiw0kaTSRJy5V41uiK5rmuzyVHbjHZ7pBnJLToXFjCxuYoSiVMo-33bmUMXbe_8WghUZMEuCPt0FWEv1ZA9Xyh-QlnebpJdjciZX9CYCdOhdqj-Wb_JkcaPLKS91Zr3srn14munUMRdPnZnmLVCCxAwBC4EeXCcj4X0Xaybnne01Ovcl5KFD2FGhkvksn2iPqAaAwA7bs7nVB-mrQFXFCKg}

# customer service internal endpoint
customer-service.internal.endpoint = ${CUSTOMER_SERVICE_INTERNAL_ENDPOINT:http://stag-svc.vinclub.internal:31158/internal}
customer-service.integration-key = ${CUSTOMER_SERVICE_INTEGRATION_KEY:dmNsdWItcGFydG5lcjpOeWticGRId0pZZmFpd3NCUXBpcnlqWUV6c05RblB0ejJr}

# keycloak integration
keycloak.base-url=${KEYCLOAK_BASE_URL:https://stag-id.vinclub.vn}
keycloak.realm=${KEYCLOAK_PARTNER_REALM:loyalty-partner}
jwt.partner_integration.jwks-uri=${PARTNER_INTEGRATION_JWT_JWKS_URI:https://stag-id.vinclub.vn/realms/loyalty-partner/protocol/openid-connect/certs}
jwt.vclub_customer_public_key_rs256 = ${VCLUB_CUSTOMER_JWT_PUBLIC_KEY:MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAh4z4zEl1yHPne+QM3IyynbeSHf0HURwfgghhGoleJYC4XkAVFVvqHv+LmQu3UuM0JFdB4GllaGJJaAnSdU26aGIctYITlXytQZcEptooCguWGh0VgMdF+DH74q+ZO+yLDtv74irr+KsSHC4qs5gFPG000eStbEvd7A8nlbbdldsYOKMza51YIsnDNcYifjoRANBeVxn2pV3f810/mqZrtSls+/YndQ9SGCQrtIIDrQotnBBJyZdOSBPo7aWyMcFl+Sv7H1Ngm6MZyWwkqSo1kQ7iJAjJyPHZMmlQqLNdAqZT9vryvv4h8T4WvriYzNbdvXymtmF6sVU9dgi2zGhceAGQuAbYKz1FUagbdBL6lKeIdzlnrWlKSL/TR+6Feg6dbL9c1GfMhTqq8MeAKLuKAuCBU220AkG1f0ZOD3pw7J6laWCHtz6brp2tdWQc4Q3UNuGWtNGygxP5GCDF9/7Pi0cB04k5A2wO/1otU7tAeSTjMC/ZZYpmsrIC+CPyRsHNDwkjRDKY5gl1Hnd8KgiGIkwlHt7wsFAzmQzYsaOFXXs4LAX8QdxPLOvEtsl5nWOB1LSx5k0HQUzGgFMeqLiL59YjCqqJxQYLZsLnfvoHx0JVDcEYpx19ayhOZd3sXXjqNGOkkCzZy01dBfuOMixmfkbA/QvfH7wSbvhKCZJrflkCAwEAAQ==}
jwt.issuer=${PARTNER_JWT_ISSUER:vclub-partner-service}
jwt.expiration_time_in_ms=${PARTNER_JWT_EXPIRATION_TIME_IN_MS:1800000}

# swagger configuration
springdoc.api-docs.path = /api-docs
springdoc.swagger-ui.path = /swagger-ui.html
logging.level.io.swagger.models.parameters.AbstractSerializableParameter=error

#http
httppool.maxPerRoute= 100
httppool.maxTotal= 1000
httppool.timeout= 5
httppool.max-idle-time= 30
httppool.loggingLevel= DEBUG

# VinClub
default.tenant.id = ${DEFAULT_TENANT_ID:1}
default.org.id = ${DEFAULT_ORG_ID:66}
default.vinclub.point.code = ${DEFAULT_VINCLUB_POINT_CODE:VPOINT}
default.vinclub.point.cash.value = ${DEFAULT_VINCLUB_POINT_CASH_VALUE:1000}

# partner integration configuration
# common
partner.process-module.redirect-to-app-uri.template=${PARTNER_MODULE_PROCESS_REDIRECT_TO_APP_URI_TEMPLATE:https://stag-vinclub.vn/app/redirect/partner/{partner_code}/module/{module}?success={success}&next_step={next_step}}
partner.process-module.redirect-to-server-uri.template=${PARTNER_MODULE_PROCESS_REDIRECT_TO_SERVER_URI_TEMPLATE:http://localhost:8080/pn/external/redirect/partner/{partner_code}/module/{module}?execution_id={execution_id}}
partner.integration.max_token_alive_time_in_ms = ${PARTNER_INTEGRATION_TOKEN_MAX_ALIVE_TIME_IN_MS:300000}

# transaction config
partner.transaction.max-request-by-user-per-day = ${PARTNER_TRANSACTION_MAX_REQUEST_BY_USER_PER_DAY:100}
partner.transaction.max-cash-amount-by-user-per-day = ${PARTNER_TRANSACTION_MAX_AMOUNT_BY_USER_PER_DAY:200000000}

# SkyJoy Integration:
partner.skyjoy.partner-code = ${SKY_JOY_CODE:SKYJOY}
partner.skyjoy.auth.client-id=${SKY_JOY_AUTH_CLIENT_ID:7844d303-b156-40fd-b987-e7ee361e4d18}
partner.skyjoy.auth.client-secret=${SKY_JOY_AUTH_CLIENT_SECRET:CTyylhML3V9A4jdlovJBXMIeFBwZrlEr}
partner.skyjoy.auth.username=${SKY_JOY_AUTH_USERNAME:be_vinclub_partner}
partner.skyjoy.auth.password=${SKY_JOY_AUTH_PASSWORD:xTWDdNdgDMG8}
partner.skyjoy.auth.access-token-url=${SKY_JOY_AUTH_ACCESS_TOKEN_URL:https://id.uat.skyjoy.io/realms/loyalty-partner/protocol/openid-connect/token}
partner.skyjoy.api.base-url = ${SKY_JOY_API_BASE_URL:https://api.uat.skyjoy.io}
partner.skyjoy.token.public-key = ${SKY_JOY_JWT_PUBLIC_KEY:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAms0io9Fyt2hjlffWDIhhOUgHZ12hetRLr2LY1CevIy58lqZPt3stIhWHCMVvbN395yZGo75alAQOeXM6hvIh6L9jx3j1wUv/cva39st+nOoPt18FBWCXbQ0WwsWvg/etn9G1JhBgGUAmBsvMf3wBG78DQWIdDEBz2CWatjTtLYL3ll00WaOsZ7o/BNJMBkWP8e7IUkzndjd8PMyiTA7BNKbOcpZw/xH8YpJpoA65nXGRuHMbatHMI8fzU9Es3WQ/NtqI3shNpYXBpMMBAnaDdVyeYkJRYo1bQ2THOm52R1gXOlOJPlON8UAaoY4gfGTQQtrRsCWFQid+H/pzEz7ziwIDAQAB}
partner.skyjoy.link-account.auth-url = ${SKY_JOY_LINK_ACCOUNT_AUTH_URL:https://id.uat.skyjoy.io/realms/uat-loyalty/protocol/openid-connect/auth}
partner.skyjoy.link-account.token-url = ${SKY_JOY_LINK_ACCOUNT_TOKEN_URL:https://id.uat.skyjoy.io/realms/uat-loyalty/protocol/openid-connect/token}
partner.skyjoy.link-account.client-id = ${SKY_JOY_LINK_ACCOUNT_CLIENT_ID:966dfaa1-6db8-454a-8ade-0741ee2fc45c}