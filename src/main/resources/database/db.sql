create table partner_db.outbox_event
(
    id          bigserial primary key,
    event_id    varchar(50)  not null,
    event_code  varchar(100) not null,
    message_key varchar(100),
    payload     jsonb        not null,
    metadata    jsonb,
    status      varchar(20)  not null,
    created_by  varchar(100),
    created_on  timestamp with time zone default current_timestamp,
    updated_by  varchar(100),
    updated_on  timestamp with time zone default current_timestamp,
    version     integer                  default 0
);

-- Indexes for outbox_event table
create index outbox_event_idx_status on partner_db.outbox_event (status);
create index outbox_event_idx_event_id on partner_db.outbox_event (event_id);

create table partner_db.partner
(
    id             bigserial primary key,
    code           varchar(50) not null,
    display_names  jsonb       not null,
    descriptions   jsonb,
    logos          jsonb       not null,
    encryption_key jsonb,
    metadata       jsonb,
    tags           text[],
    status         varchar(50),
    active         boolean,
    version        integer,
    created_by     varchar(255),
    created_on     timestamp with time zone,
    updated_by     varchar(255),
    updated_on     timestamp with time zone
);

-- Indexes for partner table
create index partner_idx_code on partner_db.partner (code);
create index partner_idx_tags on partner_db.partner (tags);

create table partner_db.partner_point_config
(
    id                 bigserial primary key,
    code               varchar(50) not null,
    display_names      jsonb       not null,
    exchange_cash_rate jsonb       not null,
    images             jsonb       not null,
    is_default         boolean,
    partner_id         bigint      not null,
    active             boolean,
    status             varchar(50),
    created_by         varchar(255),
    created_on         timestamp with time zone,
    updated_by         varchar(255),
    updated_on         timestamp with time zone,
    version            integer
);

-- Indexes for partner_point_config table
create index partner_point_config_idx_partner_id_code on partner_db.partner_point_config (partner_id, code);

create table partner_db.partner_module
(
    id         bigserial primary key,
    partner_id bigint       not null,
    module     varchar(255) not null,
    config     jsonb,
    enabled    boolean,
    version    integer,
    created_by varchar(255),
    created_on timestamp with time zone,
    updated_by varchar(255),
    updated_on timestamp with time zone
);

-- Indexes for partner_module table
create index partner_module_idx_partner_id_module on partner_db.partner_module (partner_id, module);

create table partner_db.partner_user_mapping
(
    id                    bigserial primary key,
    vclub_user_id         bigint      not null,
    partner_id            bigint      not null,
    partner_user_id       varchar(50) not null,
    mapping_at            bigint,
    partner_user_identity jsonb,
    delete_at             bigint,
    active                boolean,
    version               integer,
    created_by            varchar(255),
    created_on            timestamp with time zone,
    updated_by            varchar(255),
    updated_on            timestamp with time zone
);

-- Indexes for partner_user_mapping table
create index partner_user_mapping_idx_partner_id_partner_user_id on partner_db.partner_user_mapping (partner_id, partner_user_id);
create index partner_user_mapping_idx_partner_id_vclub_user_id on partner_db.partner_user_mapping (partner_id, vclub_user_id);

create table partner_db.partner_point_transaction_history
(
    id                     bigserial primary key,
    partner_id             bigint       not null,
    partner_user_id        varchar(100) not null,
    vclub_user_id          bigint       not null,
    actor_system           varchar(50)  not null,
    transaction_type       varchar(50)  not null,
    transaction_id         varchar(255),
    partner_transaction_id varchar(255),
    description            varchar(255),
    point_amount           bigint       not null,
    point_code             varchar(50)  not null,
    request_time           bigint,
    internal_ref_code      varchar(255),
    processed_time         bigint,
    metadata               jsonb,
    status                 varchar(50)  not null,
    active                 boolean,
    version                integer,
    created_by             varchar(255),
    created_on             timestamp with time zone,
    updated_by             varchar(255),
    updated_on             timestamp with time zone
);

-- Indexes for partner_point_transaction_history table
create index partner_point_txn_history_idx_partner_id_partner_transaction_id on partner_db.partner_point_transaction_history (partner_id, partner_transaction_id);
create index partner_point_txn_history_idx_transaction_id on partner_db.partner_point_transaction_history (transaction_id);
create index partner_point_txn_history_idx_internal_ref_code on partner_db.partner_point_transaction_history (internal_ref_code);
