# common
spring.application.name=partner-service
server.port = 8080
server.servlet.context-path = /pn
spring.main.allow-bean-definition-overriding=true
spring.main.banner-mode=off

# server
server.max-http-request-header-size=2MB
server.servlet.encoding.charset = UTF-8

########## PostgreSQL - connection
spring.datasource.url = ${POSTGRESQL_URL}
spring.datasource.username = ${POSTGRESQL_USER}
spring.datasource.password = ${POSTGRESQL_PASSWORD}
spring.datasource.hikari.minimumIdle = 8
spring.datasource.hikari.maximumPoolSize = 16
spring.datasource.hikari.idleTimeout = 30000
spring.datasource.hikari.poolName = SpringBootJPAHikariCP
spring.datasource.hikari.maxLifetime = 2000000
spring.datasource.hikari.connectionTimeout = 30000

# The SQL dialect makes Hibernate generate better SQL for the chosen database
spring.jpa.properties.hibernate.default_schema = ${DB_DEFAULT_SCHEMA:partner_db}
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults = false
spring.jpa.database-platform = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto = none
spring.jpa.show-sql = true

# REDDISON
redisson.config.address=${REDISSON_CONFIG_ADDRESS}
redisson.config.password=${REDISSON_CONFIG_PASSWORD:}
redisson.config.database=${REDISSON_CONFIG_DATABASE:0}
redisson.config.response.timeout=${REDISSON_CONFIG_RESPONSE_TIMEOUT:3000}
redisson.config.connection.timeout=${REDISSON_CONFIG_CONNECTION_TIMEOUT:3000}
redisson.config.connection.idle.time=${REDISSON_CONFIG_CONNECTION_IDLE_TIME:300000}
redisson.config.connection.keep-alive=${REDISSON_CONFIG_CONNECTION_KEEP_ALIVE:true}
redisson.config.connection.max=${REDISSON_CONFIG_CONNECTION_MAX:48}
redisson.config.connection.min=${REDISSON_CONFIG_CONNECTION_MIN:12}

# redisson channel
redis.channel.change=CHANNEL.UPDATE

#kafka configuration
kafka.properties.sasl.mechanism=${KAFKA_SASL_MECHANISM:PLAIN}
kafka.properties.sasl.jaas.config=${KAFKA_SASL_JAAS_CONFIG}
kafka.properties.bootstrap.servers=${KAFKA_BOOTSTRAP_SERVER}
kafka.properties.security.protocol=${KAFKA_SECURITY_PROTOCOL:PLAINTEXT}
kafka.properties.basic.auth.credentials.source=USER_INFO
kafka.properties.schema.registry.basic.auth.user.info=${KAFKA_REGISTRY_USERNAME:}:${KAFKA_REGISTRY_PASSWORD:}
kafka.properties.schema.registry.url=${KAFKA_REGISTRY_URL:}
kafka.properties.max-poll-records = ${KAFKA_MAX_POLL_RECORDS:10}
kafka.properties.auto.offset.reset = ${KAFKA_AUTO_OFFSET_RESET:}
kafka.properties.main.concurrency = ${KAFKA_MAIN_CONCURRENCY:2}
kafka.properties.main.group.name=partner-service.consumer.main_${spring.profiles.active}

# kafka topic
# Customer historical
kafka.historical.vclub_customer.topic.name=historical.vclub_customer
kafka.historical.vclub_customer.group.name=group_${spring.application.name}_${spring.profiles.active}_historical_vclub_customer
kafka.historical.vclub_customer.enabled=true

# Partner user mapping historical
kafka.historical.partner_user_mapping.topic.name=historical.partner_user_mapping
kafka.historical.partner_user_mapping.group.name=group_${spring.application.name}_${spring.profiles.active}_historical_partner_user_mapping
kafka.historical.partner_user_mapping.enabled=true

# Partner point transaction history
kafka.historical.partner_point_transaction_history.topic.name=historical.partner_point_transaction_history
kafka.historical.partner_point_transaction_history.group.name=group_${spring.application.name}_${spring.profiles.active}_historical_partner_point_transaction_history
kafka.historical.partner_point_transaction_history.enabled=true

# partner point transaction request
kafka.partner_point_txn_request.topic.name=partner_svc.partner_point_txn_request
kafka.partner_point_txn_request.group.name=group_${spring.application.name}_${spring.profiles.active}_partner_point_txn_request
kafka.partner_point_txn_request.enabled=true

# partner point transaction response
kafka.partner_point_txn_response.topic.name=partner_svc.partner_point_txn_response

# audit log
kafka.log.external_activity_audit.topic.name=log.external_activity_audit
kafka.log.vclub_integration_audit.topic.name=log.vclub_integration_audit

# core service internal endpoint
core-service.internal.endpoint = ${CORE_SERVICE_INTERNAL_ENDPOINT}
core-service.auth = ${AUTHZ_VCLB_CORE_SVC_TOKEN}

# customer service internal endpoint
customer-service.internal.endpoint = ${CUSTOMER_SERVICE_INTERNAL_ENDPOINT}
customer-service.integration-key = ${CUSTOMER_SERVICE_INTEGRATION_KEY}

# keycloak integration
keycloak.base-url=${KEYCLOAK_BASE_URL}
keycloak.realm=${KEYCLOAK_PARTNER_REALM}
jwt.partner_integration.jwks-uri=${PARTNER_INTEGRATION_JWT_JWKS_URI}
jwt.vclub_customer_public_key_rs256 = ${VCLUB_CUSTOMER_JWT_PUBLIC_KEY}
jwt.issuer=${PARTNER_JWT_ISSUER:vclub-partner-service}
jwt.expiration_time_in_ms=${PARTNER_JWT_EXPIRATION_TIME_IN_MS:1800000}

# swagger configuration
springdoc.api-docs.path = /api-docs
springdoc.swagger-ui.path = /swagger-ui.html
logging.level.io.swagger.models.parameters.AbstractSerializableParameter=error

#http
httppool.maxPerRoute= 100
httppool.maxTotal= 1000
httppool.timeout= 30
httppool.max-idle-time= 30
httppool.loggingLevel= DEBUG

# VinClub
default.tenant.id = ${DEFAULT_TENANT_ID:1}
default.org.id = ${DEFAULT_ORG_ID:66}
default.vinclub.point.code = ${DEFAULT_VINCLUB_POINT_CODE:VPOINT}
default.vinclub.point.cash.value = ${DEFAULT_VINCLUB_POINT_CASH_VALUE:1000}

# partner integration configuration
# common
partner.process-module.redirect-to-app-uri.template=${PARTNER_MODULE_PROCESS_REDIRECT_TO_APP_URI_TEMPLATE}
partner.process-module.redirect-to-server-uri.template=${PARTNER_MODULE_PROCESS_REDIRECT_TO_SERVER_URI_TEMPLATE}
partner.integration.max_token_alive_time_in_ms = ${PARTNER_INTEGRATION_TOKEN_MAX_ALIVE_TIME_IN_MS}

# transaction config
partner.transaction.max-request-by-user-per-day = ${PARTNER_TRANSACTION_MAX_REQUEST_BY_USER_PER_DAY:100}
partner.transaction.max-cash-amount-by-user-per-day = ${PARTNER_TRANSACTION_MAX_AMOUNT_BY_USER_PER_DAY:200000000}

# SkyJoy Integration:
partner.skyjoy.partner-code = ${SKY_JOY_CODE:SKYJOY}
partner.skyjoy.auth.client-id=${SKY_JOY_AUTH_CLIENT_ID}
partner.skyjoy.auth.client-secret=${SKY_JOY_AUTH_CLIENT_SECRET}
partner.skyjoy.auth.username=${SKY_JOY_AUTH_USERNAME}
partner.skyjoy.auth.password=${SKY_JOY_AUTH_PASSWORD}
partner.skyjoy.auth.access-token-url=${SKY_JOY_AUTH_ACCESS_TOKEN_URL}
partner.skyjoy.api.base-url = ${SKY_JOY_API_BASE_URL}
partner.skyjoy.token.public-key = ${SKY_JOY_JWT_PUBLIC_KEY}
partner.skyjoy.link-account.auth-url = ${SKY_JOY_LINK_ACCOUNT_AUTH_URL}
partner.skyjoy.link-account.token-url = ${SKY_JOY_LINK_ACCOUNT_TOKEN_URL}
partner.skyjoy.link-account.client-id = ${SKY_JOY_LINK_ACCOUNT_CLIENT_ID}