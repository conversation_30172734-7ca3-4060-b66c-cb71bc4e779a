<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.1.7</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>vn.vinclub</groupId>
    <artifactId>vclub-partner-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>vclub-partner-service</name>
    <description>vclub-partner-service</description>

    <properties>
        <java.version>21</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.34</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jdk8</artifactId>
            </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.jknack</groupId>
            <artifactId>handlebars</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>io.hypersistence</groupId>
            <artifactId>hypersistence-utils-hibernate-63</artifactId>
            <version>3.7.6</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/javax.xml.bind/jaxb-api -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.1</version>
        </dependency>

        <!-- END -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.5.0</version>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.38.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.40.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>vn.vinclub</groupId>
            <artifactId>vclub-common_spring3</artifactId>
            <version>1.0.5</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>5.3.1</version>
        </dependency>

        <!-- Test libs -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.5.3.Final</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.5.Final</version>
        </dependency>

        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
            <version>6.0.2</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-redis-spring</artifactId>
            <version>6.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>3.2.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!--                    <excludes>-->
                    <!--                        <exclude>-->
                    <!--                            <groupId>org.projectlombok</groupId>-->
                    <!--                            <artifactId>lombok</artifactId>-->
                    <!--                        </exclude>-->
                    <!--                    </excludes>-->
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.44.0</version>
                <configuration>
                    <skip>${skipDockerMaven}</skip>
                    <images>
                        <image>
                            <alias>postgresql16_test</alias>
                            <name>postgres:16.1</name>
                            <run>
                                <env>
                                    <POSTGRES_USER>vhm_user</POSTGRES_USER>
                                    <POSTGRES_PASSWORD>vhm_pass</POSTGRES_PASSWORD>
                                    <POSTGRES_DB>vinclub_db</POSTGRES_DB>
                                    <POSTGRES_SCHEMA>partner_db</POSTGRES_SCHEMA>
                                </env>
                                <ports>
                                    <port>2432:5432</port>
                                </ports>
                                <volumes>
                                    <bind>
                                        <volume>src/test/data/pg_docker_init.sh:/docker-entrypoint-initdb.d/db.sh
                                        </volume>
                                    </bind>
                                </volumes>
                                <log>
                                    <enabled>true</enabled>
                                </log>
                                <wait>
                                    <log>database system is ready to accept connections</log>
                                    <time>20000</time>
                                </wait>
                            </run>
                        </image>
                        <image>
                            <name>r3v3r/zookeeper:test-2.4.1</name>
                            <alias>zookeeper</alias>
                            <run>
                                <ports>
                                    <port>2181:2181</port>
                                </ports>
                                <wait>
                                    <log>binding to port</log>
                                    <time>60000</time>
                                </wait>
                                <env>
                                    <ALLOW_ANONYMOUS_LOGIN>yes</ALLOW_ANONYMOUS_LOGIN>
                                </env>
                            </run>
                        </image>
                        <image>
                            <name>bitnami/redis:6.2.10</name>
                            <!--                            <name>public.ecr.aws/bitnami/redis:6.2.10</name>-->
                            <alias>redis_test</alias>
                            <run>
                                <ports>
                                    <port>6379:6379</port>
                                </ports>
                                <env>
                                    <REDIS_AOF_ENABLED>no</REDIS_AOF_ENABLED>
                                    <ALLOW_EMPTY_PASSWORD>yes</ALLOW_EMPTY_PASSWORD>
                                </env>
                                <cmd>
                                    <exec>
                                        <arg>/opt/bitnami/scripts/redis/run.sh</arg>
                                        <arg>--maxmemory 10mb</arg>
                                        <arg>--maxmemory-policy</arg>
                                        <arg>allkeys-lru</arg>
                                    </exec>
                                </cmd>
                            </run>
                        </image>

                        <image>
                            <name>bitnami/kafka:3.5.1</name>
                            <alias>kafka_test</alias>
                            <run>
                                <ports>
                                    <port>9092:9092</port>
                                    <port>9093:9093</port>
                                    <port>9094:9094</port>
                                </ports>
                                <links>
                                    <link>zookeeper:zk</link>
                                </links>
                                <env>
                                    <KAFKA_CLIENT_USERS>vhm-user</KAFKA_CLIENT_USERS>
                                    <KAFKA_CLIENT_PASSWORDS>vhm-pass</KAFKA_CLIENT_PASSWORDS>
                                    <!--                                    <KAFKA_CFG_SASL_ENABLED_MECHANISMS>PLAIN,SCRAM-SHA-256,SCRAM-SHA-512-->
                                    <!--                                    </KAFKA_CFG_SASL_ENABLED_MECHANISMS>-->
                                    <!--                                    <KAFKA_CFG_LISTENERS>PLAINTEXT://127.0.0.1:9092</KAFKA_CFG_LISTENERS>-->
                                    <!--                                    <KAFKA_CLIENT_LISTENER_NAME>SASL_PLAINTEXT</KAFKA_CLIENT_LISTENER_NAME>-->
                                    <!--                                    <KAFKA_INTER_BROKER_LISTENER_NAME>SASL_PLAINTEXT</KAFKA_INTER_BROKER_LISTENER_NAME>-->
                                    <KAFKA_CFG_ADVERTISED_LISTENERS>
                                        PLAINTEXT://localhost:9092
                                    </KAFKA_CFG_ADVERTISED_LISTENERS>
                                    <!--                                    <KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP>-->
                                    <!--                                        CONTROLLER:PLAINTEXT,EXTERNAL:PLAINTEXT,PLAINTEXT:PLAINTEXT,SASL_PLAINTEXT:PLAINTEXT-->
                                    <!--                                    </KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP>-->
                                    <!--                                    <KAFKA_CFG_CONTROLLER_LISTENER_NAMES>CONTROLLER-->
                                    <!--                                    </KAFKA_CFG_CONTROLLER_LISTENER_NAMES>-->
                                    <!--                                    <KAFKA_CFG_CONTROLLER_QUORUM_VOTERS>0@:9093</KAFKA_CFG_CONTROLLER_QUORUM_VOTERS>-->
                                    <!--                                    <KAFKA_ADVERTISED_HOST_NAME>127.0.0.1</KAFKA_ADVERTISED_HOST_NAME>-->
                                    <KAFKA_ZOOKEEPER_PROTOCOL>PLAINTEXT</KAFKA_ZOOKEEPER_PROTOCOL>
                                    <KAFKA_CFG_ZOOKEEPER_CONNECT>zk:2181</KAFKA_CFG_ZOOKEEPER_CONNECT>
                                </env>
                                <wait>
                                    <log>started \(kafka\.server\.KafkaServer\)</log>
                                    <time>60000</time>
                                </wait>
                                <log>
                                    <enabled>false</enabled>
                                </log>
                            </run>
                        </image>

                    </images>
                </configuration>
                <executions>
                    <execution>
                        <id>start</id>
                        <!--<phase>generate-test-resources</phase>-->
                        <phase>process-test-classes</phase>
                        <!--<phase>test</phase>-->
                        <goals>
                            <goal>start</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>stop</id>
                        <phase>test</phase>
                        <goals>
                            <goal>stop</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>

        <repository>
            <id>vclub-release-reader</id>
            <name>vclub-release-local</name>
            <url>https://gitlab.vin-group.net/api/v4/projects/1123/packages/maven</url>
            <layout>default</layout>
        </repository>

    </repositories>

</project>
